#include <iostream>
#include <string>
#include <thread>
#include <chrono>
#include <iomanip>
#include <sstream>
#include <glog.h>

#define CPPHTTPLIB_OPENSSL_SUPPORT
#include <httplib.h>
#include <nlohmann/json.hpp>


/**
 * @brief Heating Magnetic Stirrer REST API Test Program
 *
 * This program tests the REST API interface for heating magnetic stirrer in AnalysisRobotApp
 * Including device status query, operation control, smart rapid heating and other functions
 */

class HeaterApiTester {
private:
    std::string m_baseUrl;
    httplib::Client m_client;
    
public:
    HeaterApiTester(const std::string& host = "*************", int port = 8080)
        : m_baseUrl("http://" + host + ":" + std::to_string(port))
        , m_client(host, port) {
        
        // 设置超时
        m_client.set_connection_timeout(5, 0);  // 5 seconds
        m_client.set_read_timeout(10, 0);       // 10 seconds
        m_client.set_write_timeout(5, 0);       // 5 seconds
    }
    
    /**
     * @brief 测试服务器连接
     */
    bool testConnection() {
        std::cout << "\n=== Test 1: Server Connection Test ===" << std::endl;


        auto res = m_client.Get("/health");
        if (!res) {
            std::cout << "❌ Cannot connect to server: " << m_baseUrl << std::endl;
            return false;
        }

        if (res->status == 200) {
            std::cout << "✅ Server connection successful" << std::endl;
            std::cout << "Response: " << res->body << std::endl;
            return true;
        } else {
            std::cout << "❌ Server response error: " << res->status << std::endl;
            std::cout << "Response: " << res->body << std::endl;
            return false;
        }
    }
    
    /**
     * @brief 测试设备状态查询
     */
    bool testDeviceStatus() {
        std::cout << "\n=== Test 2: Device Status Query ===" << std::endl;
        
        auto res = m_client.Get("/api/heater/status");
        if (!res) {
            std::cout << "❌ Status query request failed" << std::endl;
            return false;
        }
        
        if (res->status == 200) {
            std::cout << "✅ Status query successful" << std::endl;
            
            // 解析JSON响应
            try {
                nlohmann::json root = nlohmann::json::parse(res->body);
                bool allConnected = root.value("data", false);
                std::cout << "Magnetic stirrer heater connection status: " << (allConnected ? "✅ All connected" : "❌ Partially disconnected") << std::endl;

                if (allConnected) {
                    std::cout << "All magnetic stirrer heaters are connected, operation tests can proceed" << std::endl;
                } else {
                    std::cout << "Some magnetic stirrer heaters are disconnected, operation tests may fail" << std::endl;
                }
            } catch (const std::exception& e) {
                std::cout << "JSON parsing failed: " << e.what() << std::endl;
            }
            return true;
        } else {
            std::cout << "❌ Status query failed: " << res->status << std::endl;
            std::cout << "Response: " << res->body << std::endl;
            return false;
        }
    }
    
    /**
     * @brief 测试启动加热搅拌功能
     */
    bool testStartHeating() {
        std::cout << "\n=== Test 3: Start Heating Stirring Function ===" << std::endl;
        std::cout << "Default parameters: 1200 RPM, 60°C, 30 minutes timer" << std::endl;

        // According to interface documentation, use channel parameter to specify magnetic stirrer
        nlohmann::json request;
        request["action"] = "START";
        request["data"]["channel"] = 3;  // Use heater3 (COM5)

        std::string requestBody = request.dump();

        std::cout << "Sending request: " << requestBody << std::endl;

        auto res = m_client.Post("/api/heater/operation", requestBody, "application/json");
        if (!res) {
            std::cout << "❌ 加热搅拌请求失败" << std::endl;
            return false;
        }

        if (res->status == 200) {
            std::cout << "✅ 加热搅拌启动成功" << std::endl;
            std::cout << "智能升温功能已自动启动（1200转, 60°C, 30分钟）" << std::endl;

            // 解析响应
            try {
                nlohmann::json response = nlohmann::json::parse(res->body);
                std::cout << "任务ID: " << response.value("taskId", 0) << std::endl;
                std::cout << "操作: " << response.value("action", "未知") << std::endl;
                std::cout << "状态: " << response.value("status", "未知") << std::endl;
                std::cout << "消息: " << response.value("message", "无") << std::endl;
            } catch (const std::exception& e) {
                std::cout << "JSON解析失败: " << e.what() << std::endl;
            }
            return true;
        } else {
            std::cout << "❌ 加热搅拌启动失败: " << res->status << std::endl;
            std::cout << "响应: " << res->body << std::endl;
            return false;
        }
    }
    
    /**
     * @brief 监控设备状态变化
     */
    void monitorDeviceStatus(int durationSeconds = 60) {
        std::cout << "\n=== 测试4: 设备状态监控 (" << durationSeconds << "秒) ===" << std::endl;
        
        auto startTime = std::chrono::steady_clock::now();
        int count = 0;
        
        while (true) {
            auto currentTime = std::chrono::steady_clock::now();
            auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(currentTime - startTime).count();
            
            if (elapsed >= durationSeconds) {
                break;
            }
            
            auto res = m_client.Get("/api/heater/status");
            if (res && res->status == 200) {
                try {
                    nlohmann::json root = nlohmann::json::parse(res->body);
                    std::cout << "[" << std::setfill('0') << std::setw(2) << (elapsed / 60)
                              << ":" << std::setw(2) << (elapsed % 60) << "] ";

                    bool allConnected = root.value("data", false);
                    std::cout << "磁力搅拌器状态: " << (allConnected ? "✅ 全部连接" : "❌ 部分未连接") << std::endl;
                } catch (const std::exception& e) {
                    std::cout << "JSON解析失败: " << e.what() << std::endl;
                }
            } else {
                std::cout << "[" << std::setfill('0') << std::setw(2) << (elapsed / 60) 
                          << ":" << std::setw(2) << (elapsed % 60) << "] "
                          << "❌ 状态查询失败" << std::endl;
            }
            
            std::this_thread::sleep_for(std::chrono::seconds(5));
            count++;
        }
        
        std::cout << "监控完成，共查询 " << count << " 次" << std::endl;
    }
    
    /**
     * @brief 测试停止加热搅拌功能
     */
    bool testStopHeating() {
        std::cout << "\n=== 测试5: 停止加热搅拌功能 ===" << std::endl;
        std::cout << "执行: 停止搅拌 → 冲洗动作 → 移盖操作" << std::endl;

        // 根据接口文档，使用STOP操作
        nlohmann::json request;
        request["action"] = "STOP";
        request["data"]["channel"] = 3;

        std::string requestBody = request.dump();

        std::cout << "发送停止请求: " << requestBody << std::endl;

        auto res = m_client.Post("/api/heater/operation", requestBody, "application/json");
        if (!res) {
            std::cout << "❌ 停止请求失败" << std::endl;
            return false;
        }

        if (res->status == 200) {
            std::cout << "✅ 停止操作成功" << std::endl;

            try {
                nlohmann::json response = nlohmann::json::parse(res->body);
                std::cout << "任务ID: " << response.value("taskId", 0) << std::endl;
                std::cout << "操作: " << response.value("action", "未知") << std::endl;
                std::cout << "状态: " << response.value("status", "未知") << std::endl;
                std::cout << "消息: " << response.value("message", "无") << std::endl;
            } catch (const std::exception& e) {
                std::cout << "JSON解析失败: " << e.what() << std::endl;
            }
            return true;
        } else {
            std::cout << "❌ 停止操作失败: " << res->status << std::endl;
            std::cout << "响应: " << res->body << std::endl;
            return false;
        }
    }
    


    /**
     * @brief 运行完整测试套件
     */
    void runFullTestSuite() {
        std::cout << "=== 加热磁力搅拌器 REST API 测试套件 ===" << std::endl;
        std::cout << "目标服务器: " << m_baseUrl << std::endl;
        std::cout << "测试时间: " << getCurrentTimeString() << std::endl;
        std::cout << "智能升温: 默认1200转, 60°C, 30分钟" << std::endl;

        bool allPassed = true;

        // 测试1: 连接测试
        if (!testConnection()) {
            std::cout << "\n❌ 连接测试失败，无法继续后续测试" << std::endl;
            return;
        }

        // 测试2: 设备状态查询
        allPassed &= testDeviceStatus();


        // 等待一下
        std::this_thread::sleep_for(std::chrono::seconds(3));

        // 测试4: 启动加热搅拌（智能升温）
        allPassed &= testStartHeating();

        // 测试5: 状态监控
        monitorDeviceStatus(30);  // 监控30秒

        // 测试6: 停止加热搅拌
        allPassed &= testStopHeating();

        // 最终状态检查
        std::this_thread::sleep_for(std::chrono::seconds(3));
        std::cout << "\n=== 最终状态检查 ===" << std::endl;
        testDeviceStatus();

        // 测试结果总结
        std::cout << "\n" << std::string(50, '=') << std::endl;
        if (allPassed) {
            std::cout << "🎉 所有测试通过！" << std::endl;
        } else {
            std::cout << "❌ 部分测试失败，请检查日志" << std::endl;
        }
        std::cout << "测试完成时间: " << getCurrentTimeString() << std::endl;
        std::cout << std::string(50, '=') << std::endl;
    }

private:
    std::string getCurrentTimeString() {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        std::stringstream ss;
        ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
        return ss.str();
    }
};

/**
 * @brief 显示使用帮助
 */
void showUsage(const char* programName) {
    std::cout << "用法: " << programName << " [选项]" << std::endl;
    std::cout << "选项:" << std::endl;
    std::cout << "  -h, --help              显示此帮助信息" << std::endl;
    std::cout << "  -s, --server <host>     指定服务器地址 (默认: localhost)" << std::endl;
    std::cout << "  -p, --port <port>       指定服务器端口 (默认: 8080)" << std::endl;
    std::cout << "  -t, --test <test_name>  运行指定测试:" << std::endl;
    std::cout << "                          connection  - 连接测试" << std::endl;
    std::cout << "                          status      - 状态查询测试" << std::endl;
    std::cout << "                          heating     - 加热搅拌测试" << std::endl;
    std::cout << "                          monitor     - 状态监控测试" << std::endl;
    std::cout << "                          stop        - 停止功能测试" << std::endl;
    std::cout << "                          all         - 运行所有测试 (默认)" << std::endl;
    std::cout << std::endl;
    std::cout << "示例:" << std::endl;
    std::cout << "  " << programName << "                           # 运行所有测试" << std::endl;
    std::cout << "  " << programName << " -s ************* -p 8080  # 指定服务器" << std::endl;
    std::cout << "  " << programName << " -t heating                # 只测试智能加热" << std::endl;
}

/**
 * @brief 主函数
 */
int main(int argc, char* argv[]) {
    // 初始化日志
    google::InitGoogleLogging(argv[0]);
    google::SetLogDestination(google::GLOG_INFO, "");
    google::SetStderrLogging(google::GLOG_WARNING);

    // 解析命令行参数
    std::string host = "localhost";
    int port = 8080;
    std::string testName = "all";

    for (int i = 1; i < argc; i++) {
        std::string arg = argv[i];

        if (arg == "-h" || arg == "--help") {
            showUsage(argv[0]);
            return 0;
        } else if (arg == "-s" || arg == "--server") {
            if (i + 1 < argc) {
                host = argv[++i];
            } else {
                std::cerr << "错误: " << arg << " 需要参数" << std::endl;
                return 1;
            }
        } else if (arg == "-p" || arg == "--port") {
            if (i + 1 < argc) {
                port = std::stoi(argv[++i]);
            } else {
                std::cerr << "错误: " << arg << " 需要参数" << std::endl;
                return 1;
            }
        } else if (arg == "-t" || arg == "--test") {
            if (i + 1 < argc) {
                testName = argv[++i];
            } else {
                std::cerr << "错误: " << arg << " 需要参数" << std::endl;
                return 1;
            }
        } else {
            std::cerr << "错误: 未知参数 " << arg << std::endl;
            showUsage(argv[0]);
            return 1;
        }
    }

    try {
        HeaterApiTester tester(host, port);

        if (testName == "all") {
            tester.runFullTestSuite();
        } else if (testName == "connection") {
            tester.testConnection();
        } else if (testName == "status") {
            tester.testDeviceStatus();
        } else if (testName == "heating") {
            tester.testStartHeating();
        } else if (testName == "monitor") {
            tester.monitorDeviceStatus(60);
        } else if (testName == "stop") {
            tester.testStopHeating();
        } else {
            std::cerr << "错误: 未知测试名称 " << testName << std::endl;
            showUsage(argv[0]);
            return 1;
        }

    } catch (const std::exception& e) {
        std::cerr << "❌ 测试异常: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "❌ 未知异常" << std::endl;
        return 1;
    }

    return 0;
}
