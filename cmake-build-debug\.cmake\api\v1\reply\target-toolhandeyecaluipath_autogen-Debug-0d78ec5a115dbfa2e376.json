{"backtrace": 0, "backtraceGraph": {"commands": [], "files": ["tool/handeyecaluipath/CMakeLists.txt"], "nodes": [{"file": 0}]}, "dependencies": [{"backtrace": 0, "id": "fuxicommon::@58335e9a86196d0a97e7"}, {"backtrace": 0, "id": "hardwaredriverHikVisionCamera::@bc252bb14595a0f09d26"}, {"backtrace": 0, "id": "hardwaredriverAuboArcsDriver::@fd5e493b37d2bab880d9"}], "id": "toolhandeyecaluipath_autogen::@f8ebbc87f7fac77328c8", "isGeneratorProvided": true, "name": "toolhandeyecaluipath_autogen", "paths": {"build": "tool/handeyecaluipath", "source": "tool/handeyecaluipath"}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1]}], "sources": [{"backtrace": 0, "isGenerated": true, "path": "cmake-build-debug/tool/handeyecaluipath/CMakeFiles/toolhandeyecaluipath_autogen", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "cmake-build-debug/tool/handeyecaluipath/CMakeFiles/toolhandeyecaluipath_autogen.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}