{"artifacts": [{"path": "D:/newfuxios/install/x64-install/dev/bin/Debug/RoboticLaserMarkingTestlaser.exe"}, {"path": "D:/newfuxios/install/x64-install/dev/bin/Debug/RoboticLaserMarkingTestlaser.pdb"}], "backtrace": 3, "backtraceGraph": {"commands": ["add_executable", "include", "target_link_libraries", "add_unique_libraries", "set_property", "find_package", "boost_find_component", "ADD_DEFINITIONS", "include_directories"], "files": ["builder/cmake/executable.cmake", "RoboticLaserMarking/Test/laser/CMakeLists.txt", "builder/cmake/common.cmake", "builder/cmake/library.cmake", "RoboticLaserMarking/laserDriver/CMakeLists.txt", "fuxicommon/CMakeLists.txt", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_filesystem-1.78.0/libboost_filesystem-variant-vc142-mt-gd-x64-1_78-static.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_filesystem-1.78.0/boost_filesystem-config.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/Boost-1.78.0/BoostConfig.cmake", "D:/opt/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30/Modules/FindBoost.cmake", "builder/cmake/add_boost.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_thread-1.78.0/boost_thread-config.cmake", "builder/cmake/add_eigen.cmake", "builder/cmake/add_glog.cmake", "builder/cmake/add_libmodbus.cmake"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 3, "parent": 0}, {"file": 0, "parent": 1}, {"command": 0, "file": 0, "line": 26, "parent": 2}, {"command": 3, "file": 0, "line": 30, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 4}, {"command": 3, "file": 0, "line": 35, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 6}, {"command": 3, "file": 0, "line": 35, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 8}, {"command": 2, "file": 0, "line": 46, "parent": 2}, {"file": 4}, {"command": 1, "file": 4, "line": 2, "parent": 11}, {"file": 3, "parent": 12}, {"command": 3, "file": 3, "line": 90, "parent": 13}, {"command": 2, "file": 3, "line": 53, "parent": 14}, {"file": 5}, {"command": 1, "file": 5, "line": 3, "parent": 16}, {"file": 3, "parent": 17}, {"command": 3, "file": 3, "line": 78, "parent": 18}, {"command": 2, "file": 3, "line": 53, "parent": 19}, {"command": 1, "file": 3, "line": 1, "parent": 18}, {"file": 2, "parent": 21}, {"command": 1, "file": 2, "line": 81, "parent": 22}, {"file": 10, "parent": 23}, {"command": 5, "file": 10, "line": 33, "parent": 24}, {"file": 9, "parent": 25}, {"command": 5, "file": 9, "line": 610, "parent": 26}, {"file": 8, "parent": 27}, {"command": 6, "file": 8, "line": 262, "parent": 28}, {"command": 5, "file": 8, "line": 141, "parent": 29}, {"file": 7, "parent": 30}, {"command": 1, "file": 7, "line": 53, "parent": 31}, {"file": 6, "parent": 32}, {"command": 4, "file": 6, "line": 101, "parent": 33}, {"command": 4, "file": 7, "line": 103, "parent": 31}, {"command": 6, "file": 8, "line": 262, "parent": 28}, {"command": 5, "file": 8, "line": 141, "parent": 36}, {"file": 11, "parent": 37}, {"command": 4, "file": 11, "line": 103, "parent": 38}, {"command": 3, "file": 3, "line": 84, "parent": 18}, {"command": 2, "file": 3, "line": 53, "parent": 40}, {"command": 3, "file": 3, "line": 84, "parent": 18}, {"command": 2, "file": 3, "line": 53, "parent": 42}, {"command": 3, "file": 3, "line": 84, "parent": 18}, {"command": 2, "file": 3, "line": 53, "parent": 44}, {"command": 3, "file": 3, "line": 84, "parent": 18}, {"command": 2, "file": 3, "line": 53, "parent": 46}, {"command": 3, "file": 0, "line": 35, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 48}, {"command": 3, "file": 0, "line": 35, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 50}, {"command": 3, "file": 0, "line": 35, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 52}, {"command": 7, "file": 0, "line": 17, "parent": 2}, {"command": 7, "file": 0, "line": 13, "parent": 2}, {"command": 7, "file": 0, "line": 16, "parent": 2}, {"command": 7, "file": 0, "line": 14, "parent": 2}, {"command": 7, "file": 0, "line": 15, "parent": 2}, {"command": 1, "file": 0, "line": 1, "parent": 2}, {"file": 2, "parent": 59}, {"command": 8, "file": 2, "line": 54, "parent": 60}, {"command": 1, "file": 2, "line": 81, "parent": 60}, {"file": 12, "parent": 62}, {"command": 8, "file": 12, "line": 30, "parent": 63}, {"command": 1, "file": 2, "line": 81, "parent": 60}, {"file": 13, "parent": 65}, {"command": 8, "file": 13, "line": 16, "parent": 66}, {"command": 1, "file": 2, "line": 81, "parent": 60}, {"file": 14, "parent": 68}, {"command": 8, "file": 14, "line": 8, "parent": 69}, {"command": 8, "file": 0, "line": 34, "parent": 2}, {"command": 8, "file": 0, "line": 34, "parent": 2}, {"command": 8, "file": 0, "line": 34, "parent": 2}, {"command": 8, "file": 0, "line": 34, "parent": 2}, {"command": 8, "file": 0, "line": 34, "parent": 2}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc /MDd /Zi /Ob0 /Od /RTC1 -std:c++20"}], "defines": [{"backtrace": 5, "define": "BOOST_ATOMIC_NO_LIB"}, {"backtrace": 5, "define": "BOOST_CHRONO_NO_LIB"}, {"backtrace": 5, "define": "BOOST_DATE_TIME_NO_LIB"}, {"backtrace": 5, "define": "BOOST_FILESYSTEM_NO_LIB"}, {"backtrace": 5, "define": "BOOST_IOSTREAMS_NO_LIB"}, {"backtrace": 5, "define": "BOOST_PROGRAM_OPTIONS_NO_LIB"}, {"backtrace": 5, "define": "BOOST_SERIALIZATION_NO_LIB"}, {"backtrace": 5, "define": "BOOST_SYSTEM_NO_LIB"}, {"backtrace": 5, "define": "BOOST_THREAD_NO_LIB"}, {"backtrace": 9, "define": "QT_CHARTS_LIB"}, {"backtrace": 54, "define": "QT_CORE_LIB"}, {"backtrace": 55, "define": "QT_DISABLE_DEPRECATED_BEFORE=0"}, {"backtrace": 56, "define": "QT_GUI_LIB"}, {"backtrace": 57, "define": "QT_NO_DEBUG"}, {"backtrace": 5, "define": "QT_SQL_LIB"}, {"backtrace": 58, "define": "QT_WIDGETS_LIB"}, {"backtrace": 5, "define": "RTTR_DLL"}], "includes": [{"backtrace": 0, "path": "D:/newfuxios/build/RoboticLaserMarking/Test/laser/RoboticLaserMarkingTestlaser_autogen/include_Debug"}, {"backtrace": 61, "path": "D:/newfuxios/RoboticLaserMarking/Test/laser/include"}, {"backtrace": 64, "path": "C:/opt/PCL/3rdParty/Eigen/eigen3"}, {"backtrace": 67, "path": "C:/opt/glog/include"}, {"backtrace": 70, "path": "C:/opt/libmodbus/include"}, {"backtrace": 5, "path": "D:/newfuxios/RoboticLaserMarking/laserDriver/include"}, {"backtrace": 5, "path": "C:/opt/laserControlFrame/include"}, {"backtrace": 5, "path": "D:/newfuxios/fuxicommon/include"}, {"backtrace": 5, "path": "C:/opt/openssl/include"}, {"backtrace": 71, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt"}, {"backtrace": 71, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt/QtCore"}, {"backtrace": 71, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/./mkspecs/win32-msvc"}, {"backtrace": 72, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt/QtGui"}, {"backtrace": 72, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt/QtANGLE"}, {"backtrace": 73, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt/QtSql"}, {"backtrace": 74, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt/QtWidgets"}, {"backtrace": 75, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt/QtCharts"}, {"backtrace": 5, "isSystem": true, "path": "C:/opt/PCL/3rdParty/Boost/include/boost-1_78"}, {"backtrace": 5, "isSystem": true, "path": "C:/opt/rttr/include"}], "language": "CXX", "languageStandard": {"backtraces": [5, 5], "standard": "20"}, "sourceIndexes": [0, 1]}], "dependencies": [{"backtrace": 5, "id": "fuxicommon::@58335e9a86196d0a97e7"}, {"backtrace": 5, "id": "RoboticLaserMarkinglaserDriver::@cf8a855e37e415d7ca08"}, {"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "RoboticLaserMarkingTestlaser::@fef5c779e5dd4e5a5c7c", "link": {"commandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc /MDd /Zi /Ob0 /Od /RTC1", "role": "flags"}, {"fragment": "/machine:x64                                                                                                                                                 /debug /INCREMENTAL /subsystem:console /machine:x64", "role": "flags"}, {"backtrace": 5, "fragment": "..\\..\\laserDriver\\Debug\\RoboticLaserMarkinglaserDriver.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\libmodbus\\lib\\modbus.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\glog\\lib\\glogd.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "..\\..\\..\\fuxicommon\\Debug\\fuxicommon.lib", "role": "libraries"}, {"backtrace": 7, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5Sql_conda.lib", "role": "libraries"}, {"backtrace": 9, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5Charts_conda.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "ws2_32.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "kernel32.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "user32.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "gdi32.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "winspool.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "shell32.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "ole32.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "oleaut32.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "uuid.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "comdlg32.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "advapi32.lib", "role": "libraries"}, {"backtrace": 15, "fragment": "C:\\opt\\laserControlFrame\\lib\\LaserControlFrameSDK.lib", "role": "libraries"}, {"backtrace": 20, "fragment": "C:\\opt\\opencv\\build\\x64\\vc16\\lib\\opencv_world4110d.lib", "role": "libraries"}, {"backtrace": 20, "fragment": "C:\\opt\\opencv\\build\\x64\\vc16\\lib\\opencv_img_hash4110d.lib", "role": "libraries"}, {"backtrace": 20, "fragment": "C:\\opt\\xerces-c\\lib\\xerces-c_3D.lib", "role": "libraries"}, {"backtrace": 20, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_system-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 20, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_filesystem-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 34, "fragment": "bcrypt.lib", "role": "libraries"}, {"backtrace": 34, "fragment": "synchronization.lib", "role": "libraries"}, {"backtrace": 35, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_atomic-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 20, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_date_time-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 20, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_iostreams-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 20, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_serialization-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 20, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_thread-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 39, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_chrono-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 20, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_program_options-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 20, "fragment": "C:\\opt\\openssl\\lib\\libssl.lib", "role": "libraries"}, {"backtrace": 20, "fragment": "C:\\opt\\openssl\\lib\\libcrypto.lib", "role": "libraries"}, {"backtrace": 20, "fragment": "C:\\opt\\openssl\\lib\\jsoncpp.lib", "role": "libraries"}, {"backtrace": 20, "fragment": "C:\\opt\\openssl\\lib\\libcurl.lib", "role": "libraries"}, {"backtrace": 20, "fragment": "C:\\opt\\openssl\\lib\\zlib.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "ws2_32.lib", "role": "libraries"}, {"backtrace": 20, "fragment": "crypt32.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\glog\\lib\\glogd.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "user32.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "advapi32.lib", "role": "libraries"}, {"backtrace": 20, "fragment": "C:\\opt\\rttr\\lib\\rttr_core_d.lib", "role": "libraries"}, {"backtrace": 41, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5Sql_conda.lib", "role": "libraries"}, {"backtrace": 43, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5Widgets_conda.lib", "role": "libraries"}, {"backtrace": 45, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5Gui_conda.lib", "role": "libraries"}, {"backtrace": 47, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5Core_conda.lib", "role": "libraries"}, {"backtrace": 49, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5Widgets_conda.lib", "role": "libraries"}, {"backtrace": 51, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5Gui_conda.lib", "role": "libraries"}, {"backtrace": 53, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5Core_conda.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "RoboticLaserMarkingTestlaser", "nameOnDisk": "RoboticLaserMarkingTestlaser.exe", "paths": {"build": "RoboticLaserMarking/Test/laser", "source": "RoboticLaserMarking/Test/laser"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/RoboticLaserMarking/Test/laser/RoboticLaserMarkingTestlaser_autogen/mocs_compilation_Debug.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "RoboticLaserMarking/Test/laser/src/laser.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}