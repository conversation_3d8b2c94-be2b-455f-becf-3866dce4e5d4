{"artifacts": [{"path": "Test/test_micro_dosing/test_micro_dosing.exe"}, {"path": "Test/test_micro_dosing/test_micro_dosing.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "target_link_libraries", "set_property", "_populate_Widgets_target_properties", "find_package", "include", "add_unique_libraries", "boost_find_component", "include_directories"], "files": ["Test/test_micro_dosing/CMakeLists.txt", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfig.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5/Qt5Config.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfigExtras.cmake", "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Core/Qt5CoreConfig.cmake", "builder/cmake/library.cmake", "fuxicommon/CMakeLists.txt", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_filesystem-1.78.0/libboost_filesystem-variant-vc142-mt-gd-x64-1_78-static.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_filesystem-1.78.0/boost_filesystem-config.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/Boost-1.78.0/BoostConfig.cmake", "C:/Program Files/JetBrains/CLion 2025.1.3/bin/cmake/win/x64/share/cmake-3.31/Modules/FindBoost.cmake", "builder/cmake/add_boost.cmake", "builder/cmake/common.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_thread-1.78.0/boost_thread-config.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 24, "parent": 0}, {"command": 1, "file": 0, "line": 27, "parent": 0}, {"command": 4, "file": 0, "line": 10, "parent": 0}, {"file": 2, "parent": 3}, {"command": 4, "file": 2, "line": 28, "parent": 4}, {"file": 1, "parent": 5}, {"command": 3, "file": 1, "line": 185, "parent": 6}, {"command": 2, "file": 1, "line": 45, "parent": 7}, {"command": 4, "file": 2, "line": 28, "parent": 4}, {"file": 4, "parent": 9}, {"command": 5, "file": 4, "line": 216, "parent": 10}, {"file": 3, "parent": 11}, {"command": 2, "file": 3, "line": 110, "parent": 12}, {"file": 6}, {"command": 5, "file": 6, "line": 3, "parent": 14}, {"file": 5, "parent": 15}, {"command": 6, "file": 5, "line": 78, "parent": 16}, {"command": 1, "file": 5, "line": 53, "parent": 17}, {"command": 5, "file": 5, "line": 1, "parent": 16}, {"file": 12, "parent": 19}, {"command": 5, "file": 12, "line": 81, "parent": 20}, {"file": 11, "parent": 21}, {"command": 4, "file": 11, "line": 33, "parent": 22}, {"file": 10, "parent": 23}, {"command": 4, "file": 10, "line": 610, "parent": 24}, {"file": 9, "parent": 25}, {"command": 7, "file": 9, "line": 262, "parent": 26}, {"command": 4, "file": 9, "line": 141, "parent": 27}, {"file": 8, "parent": 28}, {"command": 5, "file": 8, "line": 53, "parent": 29}, {"file": 7, "parent": 30}, {"command": 2, "file": 7, "line": 104, "parent": 31}, {"command": 2, "file": 8, "line": 103, "parent": 29}, {"command": 7, "file": 9, "line": 262, "parent": 26}, {"command": 4, "file": 9, "line": 141, "parent": 34}, {"file": 13, "parent": 35}, {"command": 2, "file": 13, "line": 103, "parent": 36}, {"command": 6, "file": 5, "line": 84, "parent": 16}, {"command": 1, "file": 5, "line": 53, "parent": 38}, {"command": 6, "file": 5, "line": 84, "parent": 16}, {"command": 1, "file": 5, "line": 53, "parent": 40}, {"command": 6, "file": 5, "line": 84, "parent": 16}, {"command": 1, "file": 5, "line": 53, "parent": 42}, {"command": 6, "file": 5, "line": 84, "parent": 16}, {"command": 1, "file": 5, "line": 53, "parent": 44}, {"command": 4, "file": 5, "line": 62, "parent": 16}, {"file": 4, "parent": 46}, {"command": 5, "file": 4, "line": 216, "parent": 47}, {"file": 3, "parent": 48}, {"command": 2, "file": 3, "line": 110, "parent": 49}, {"command": 8, "file": 0, "line": 13, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc /MDd /Zi /Ob0 /Od /RTC1 -std:c++20"}], "defines": [{"backtrace": 2, "define": "BOOST_ATOMIC_NO_LIB"}, {"backtrace": 2, "define": "BOOST_CHRONO_NO_LIB"}, {"backtrace": 2, "define": "BOOST_DATE_TIME_NO_LIB"}, {"backtrace": 2, "define": "BOOST_FILESYSTEM_NO_LIB"}, {"backtrace": 2, "define": "BOOST_IOSTREAMS_NO_LIB"}, {"backtrace": 2, "define": "BOOST_PROGRAM_OPTIONS_NO_LIB"}, {"backtrace": 2, "define": "BOOST_SERIALIZATION_NO_LIB"}, {"backtrace": 2, "define": "BOOST_SYSTEM_NO_LIB"}, {"backtrace": 2, "define": "BOOST_THREAD_NO_LIB"}, {"backtrace": 2, "define": "QT_CORE_LIB"}, {"backtrace": 2, "define": "QT_GUI_LIB"}, {"backtrace": 2, "define": "QT_SQL_LIB"}, {"backtrace": 2, "define": "QT_WIDGETS_LIB"}, {"backtrace": 2, "define": "RTTR_DLL"}], "includes": [{"backtrace": 51, "path": "D:/newfuxios/fuxicommon/include"}, {"backtrace": 51, "path": "D:/newfuxios/hardwaredriver/pouringControl/include"}, {"backtrace": 2, "path": "C:/opt/openssl/include"}, {"backtrace": 2, "path": "C:/opt/glog/include"}, {"backtrace": 2, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include"}, {"backtrace": 2, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore"}, {"backtrace": 2, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/./mkspecs/win32-msvc"}, {"backtrace": 2, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets"}, {"backtrace": 2, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui"}, {"backtrace": 2, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE"}, {"backtrace": 2, "isSystem": true, "path": "C:/opt/PCL/3rdParty/Boost/include/boost-1_78"}, {"backtrace": 2, "isSystem": true, "path": "C:/opt/rttr/include"}, {"backtrace": 2, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSql"}], "language": "CXX", "languageStandard": {"backtraces": [2, 2], "standard": "20"}, "sourceIndexes": [0]}], "dependencies": [{"backtrace": 2, "id": "fuxicommon::@58335e9a86196d0a97e7"}], "id": "test_micro_dosing::@65e3165a8812532710ef", "link": {"commandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc /MDd /Zi /Ob0 /Od /RTC1", "role": "flags"}, {"fragment": "/machine:x64                                                                                                                                                                                                                                                                         /debug /INCREMENTAL /subsystem:windows", "role": "flags"}, {"backtrace": 2, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Widgetsd.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "fuxicommon\\fuxicommon.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "hardwaredriverpouringControl.lib", "role": "libraries"}, {"backtrace": 8, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Guid.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Cored.lib", "role": "libraries"}, {"backtrace": 13, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\qtmaind.lib", "role": "libraries"}, {"backtrace": 18, "fragment": "C:\\opt\\opencv\\build\\x64\\vc16\\lib\\opencv_world4110d.lib", "role": "libraries"}, {"backtrace": 18, "fragment": "C:\\opt\\opencv\\build\\x64\\vc16\\lib\\opencv_img_hash4110d.lib", "role": "libraries"}, {"backtrace": 18, "fragment": "C:\\opt\\xerces-c\\lib\\xerces-c_3D.lib", "role": "libraries"}, {"backtrace": 18, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_system-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 18, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_filesystem-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 32, "fragment": "bcrypt.lib", "role": "libraries"}, {"backtrace": 32, "fragment": "synchronization.lib", "role": "libraries"}, {"backtrace": 33, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_atomic-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 18, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_date_time-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 18, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_iostreams-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 18, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_serialization-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 18, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_thread-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 37, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_chrono-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 18, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_program_options-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 18, "fragment": "C:\\opt\\openssl\\lib\\libssl.lib", "role": "libraries"}, {"backtrace": 18, "fragment": "C:\\opt\\openssl\\lib\\libcrypto.lib", "role": "libraries"}, {"backtrace": 18, "fragment": "C:\\opt\\openssl\\lib\\jsoncpp.lib", "role": "libraries"}, {"backtrace": 18, "fragment": "C:\\opt\\openssl\\lib\\libcurl.lib", "role": "libraries"}, {"backtrace": 18, "fragment": "C:\\opt\\openssl\\lib\\zlib.lib", "role": "libraries"}, {"backtrace": 18, "fragment": "ws2_32.lib", "role": "libraries"}, {"backtrace": 18, "fragment": "crypt32.lib", "role": "libraries"}, {"backtrace": 18, "fragment": "advapi32.lib", "role": "libraries"}, {"backtrace": 18, "fragment": "user32.lib", "role": "libraries"}, {"backtrace": 18, "fragment": "C:\\opt\\rttr\\lib\\rttr_core_d.lib", "role": "libraries"}, {"backtrace": 18, "fragment": "C:\\opt\\glog\\lib\\glogd.lib", "role": "libraries"}, {"backtrace": 39, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Sqld.lib", "role": "libraries"}, {"backtrace": 41, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Widgetsd.lib", "role": "libraries"}, {"backtrace": 43, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Guid.lib", "role": "libraries"}, {"backtrace": 45, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Cored.lib", "role": "libraries"}, {"backtrace": 50, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\qtmaind.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "test_micro_dosing", "nameOnDisk": "test_micro_dosing.exe", "paths": {"build": "Test/test_micro_dosing", "source": "Test/test_micro_dosing"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "Test/test_micro_dosing/src/main.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}