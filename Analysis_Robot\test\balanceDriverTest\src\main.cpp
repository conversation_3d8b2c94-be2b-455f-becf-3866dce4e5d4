#include "BalanceDriver.h"
#include <iostream>
#include <iomanip>

using namespace AnalysisRobot::Balance;

void statusCallback(BalanceStatus status, const std::string& message) {
    std::cout << "Status: ";
    switch (status) {
        case BalanceStatus::DISCONNECTED:
            std::cout << "DISCONNECTED";
            break;
        case BalanceStatus::CONNECTED:
            std::cout << "CONNECTED";
            break;
        case BalanceStatus::READING:
            std::cout << "READING";
            break;
        case BalanceStatus::FAULT:
            std::cout << "FAULT";
            break;
    }
    std::cout << " - " << message << std::endl;
}

int main() {
    std::cout << "=== 天平驱动测试程序 ===" << std::endl;
    
    // 创建天平驱动实例
    BalanceDriver balance;
    
    // 设置状态回调
    balance.setStatusCallback(statusCallback);
    
    // 配置参数
    BalanceConfig config;
    config.serialPort = "COM3";        // 根据实际情况修改
    config.baudRate = 9600;
    config.parity = 'N';
    config.dataBits = 8;
    config.stopBits = 1;
    config.slaveId = 1;
    config.responseTimeout = 1000;
    
    std::cout << "配置参数:" << std::endl;
    std::cout << "  串口: " << config.serialPort << std::endl;
    std::cout << "  波特率: " << config.baudRate << std::endl;
    std::cout << "  从机地址: " << config.slaveId << std::endl;
    std::cout << std::endl;
    
    // 连接天平
    std::cout << "正在连接天平..." << std::endl;
    if (!balance.connect(config)) {
        std::cout << "连接失败: " << balance.getLastError() << std::endl;
        return -1;
    }
    
    std::cout << "连接成功!" << std::endl;
    std::cout << std::endl;
    
    // 测试读取重量
    std::cout << "=== 重量读取测试 ===" << std::endl;
    for (int i = 0; i < 5; i++) {
        std::cout << "第 " << (i + 1) << " 次读取:" << std::endl;
        
        WeightReading reading = balance.readWeight();
        
        if (reading.success) {
            std::cout << "  重量: " << std::fixed << std::setprecision(4) 
                      << reading.weight << " g" << std::endl;
            std::cout << "  内码值: 0x" << std::hex << std::uppercase 
                      << reading.rawValue << std::dec << " (" << reading.rawValue << ")" << std::endl;
        } else {
            std::cout << "  读取失败: " << reading.errorMsg << std::endl;
        }
        
        std::cout << std::endl;
        
        // 等待1秒
        std::this_thread::sleep_for(std::chrono::seconds(1));
    }
    
    // 测试去皮功能
    std::cout << "=== 去皮功能测试 ===" << std::endl;
    std::cout << "正在执行去皮..." << std::endl;
    
    if (balance.tare()) {
        std::cout << "去皮成功!" << std::endl;
    } else {
        std::cout << "去皮失败: " << balance.getLastError() << std::endl;
    }
    
    std::cout << std::endl;
    
    // 去皮后再次读取重量
    std::cout << "去皮后重量读取:" << std::endl;
    WeightReading reading = balance.readWeight();
    
    if (reading.success) {
        std::cout << "  重量: " << std::fixed << std::setprecision(4) 
                  << reading.weight << " g" << std::endl;
        std::cout << "  内码值: 0x" << std::hex << std::uppercase 
                  << reading.rawValue << std::dec << " (" << reading.rawValue << ")" << std::endl;
    } else {
        std::cout << "  读取失败: " << reading.errorMsg << std::endl;
    }
    
    // 断开连接
    std::cout << std::endl;
    std::cout << "正在断开连接..." << std::endl;
    balance.disconnect();
    
    std::cout << "测试完成!" << std::endl;
    
    return 0;
}
