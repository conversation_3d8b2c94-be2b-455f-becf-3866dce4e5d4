﻿#include "BalanceDriver.h"
#include <iostream>
#include <thread>
#include <chrono>

using namespace AnalysisRobot::Balance;

int main() {
    std::cout << "=== 天平驱动测试程序 ===" << std::endl;
    
    // 创建天平驱动实例
    BalanceDriver balance;
    
    // 配置天平参数
    BalanceConfig config;
    config.serialPort = "COM6";  // 根据实际情况修改串口号
    config.baudRate = 9600;
    config.slaveId = 1;
    config.responseTimeout = 20000;
    
    std::cout << "配置信息:" << std::endl;
    std::cout << "串口: " << config.serialPort << std::endl;
    std::cout << "波特率: " << config.baudRate << std::endl;
    std::cout << "从机地址: " << config.slaveId << std::endl;
    
    // 设置状态回调
    balance.setStatusCallback([](BalanceStatus status, const std::string& message) {
        std::cout << "[状态] ";
        switch (status) {
            case BalanceStatus::DISCONNECTED: std::cout << "未连接"; break;
            case BalanceStatus::CONNECTED: std::cout << "已连接"; break;
            case BalanceStatus::READING: std::cout << "读取中"; break;
            case BalanceStatus::FAULT: std::cout << "错误"; break;
        }
        std::cout << " - " << message << std::endl;
    });
    
    // 初始化天平
    std::cout << "\n正在初始化天平..." << std::endl;
    if (!balance.initialize(config)) {
        std::cout << "初始化失败!" << std::endl;
        return -1;
    }
    
    // 连接天平
    std::cout << "正在连接天平..." << std::endl;
    if (!balance.connect()) {
        std::cout << "连接失败!" << std::endl;
        return -1;
    }
    
    std::cout << "\n天平连接成功!" << std::endl;
    std::cout << balance.getDeviceInfo() << std::endl;
    
    // 主循环
    char choice;
    do {
        std::cout << "\n=== 操作菜单 ===" << std::endl;
        std::cout << "1. 读取重量 (w)" << std::endl;
        std::cout << "2. 去皮操作 (t)" << std::endl;
        std::cout << "3. 查看状态 (s)" << std::endl;
        std::cout << "4. 退出程序 (q)" << std::endl;
        std::cout << "请选择操作: ";
        
        std::cin >> choice;
        
        switch (choice) {
            case '1':
            case 'w':
            case 'W': {
                std::cout << "\n正在读取重量..." << std::endl;
                WeightReading reading = balance.readWeight();
                
                if (reading.success) {
                    std::cout << "重量: " << reading.weight << " g" << std::endl;
                    std::cout << "内码值: " << reading.rawValue << std::endl;
                } else {
                    std::cout << "读取失败: " << reading.errorMsg << std::endl;
                }
                break;
            }
            
            case '2':
            case 't':
            case 'T': {
                std::cout << "\n正在执行去皮操作..." << std::endl;
                if (balance.tare()) {
                    std::cout << "去皮成功!" << std::endl;
                } else {
                    std::cout << "去皮失败!" << std::endl;
                }
                break;
            }
            
            case '3':
            case 's':
            case 'S': {
                std::cout << "\n当前状态:" << std::endl;
                std::cout << balance.getDeviceInfo() << std::endl;
                std::cout << "连接状态: " << (balance.isConnected() ? "已连接" : "未连接") << std::endl;
                break;
            }
            
            case '4':
            case 'q':
            case 'Q':
                std::cout << "\n正在退出程序..." << std::endl;
                break;
                
            default:
                std::cout << "无效选择，请重新输入!" << std::endl;
                break;
        }
        
        // 短暂延时
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        
    } while (choice != '4' && choice != 'q' && choice != 'Q');
    
    // 断开连接
    balance.disconnect();
    std::cout << "程序结束。" << std::endl;
    
    return 0;
}
