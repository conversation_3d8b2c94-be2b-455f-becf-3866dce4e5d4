# This is the CMakeCache file.
# For build in directory: d:/newfuxios/cmake-build-debug
# It was generated by CMake: C:/Program Files/JetBrains/CLion 2025.1.3/bin/cmake/win/x64/bin/cmake.exe
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Value Computed by CMake
Analysis_RobotApp_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/Analysis_Robot/App

//Value Computed by CMake
Analysis_RobotApp_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
Analysis_RobotApp_SOURCE_DIR:STATIC=D:/newfuxios/Analysis_Robot/App

//Value Computed by CMake
Analysis_RobotalgorithmscoordinateTransform_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/Analysis_Robot/algorithms/coordinateTransform

//Value Computed by CMake
Analysis_RobotalgorithmscoordinateTransform_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
Analysis_RobotalgorithmscoordinateTransform_SOURCE_DIR:STATIC=D:/newfuxios/Analysis_Robot/algorithms/coordinateTransform

//Value Computed by CMake
Analysis_RobotalgorithmspouringControl_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/Analysis_Robot/algorithms/pouringControl

//Value Computed by CMake
Analysis_RobotalgorithmspouringControl_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
Analysis_RobotalgorithmspouringControl_SOURCE_DIR:STATIC=D:/newfuxios/Analysis_Robot/algorithms/pouringControl

//Value Computed by CMake
Analysis_RobotalgorithmstcpPositionMaintain_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/Analysis_Robot/algorithms/tcpPositionMaintain

//Value Computed by CMake
Analysis_RobotalgorithmstcpPositionMaintain_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
Analysis_RobotalgorithmstcpPositionMaintain_SOURCE_DIR:STATIC=D:/newfuxios/Analysis_Robot/algorithms/tcpPositionMaintain

//Value Computed by CMake
Analysis_RobotdriversaixsDriver_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/Analysis_Robot/drivers/aixsDriver

//Value Computed by CMake
Analysis_RobotdriversaixsDriver_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
Analysis_RobotdriversaixsDriver_SOURCE_DIR:STATIC=D:/newfuxios/Analysis_Robot/drivers/aixsDriver

//Value Computed by CMake
Analysis_RobotdriversbalanceDriver_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/Analysis_Robot/drivers/balanceDriver

//Value Computed by CMake
Analysis_RobotdriversbalanceDriver_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
Analysis_RobotdriversbalanceDriver_SOURCE_DIR:STATIC=D:/newfuxios/Analysis_Robot/drivers/balanceDriver

//Value Computed by CMake
Analysis_RobotdriversheatingMagneticStirrerDriver_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/Analysis_Robot/drivers/heatingMagneticStirrerDriver

//Value Computed by CMake
Analysis_RobotdriversheatingMagneticStirrerDriver_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
Analysis_RobotdriversheatingMagneticStirrerDriver_SOURCE_DIR:STATIC=D:/newfuxios/Analysis_Robot/drivers/heatingMagneticStirrerDriver

//Value Computed by CMake
Analysis_RobotdriversmoistureAnalyzerDriver_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/Analysis_Robot/drivers/moistureAnalyzerDriver

//Value Computed by CMake
Analysis_RobotdriversmoistureAnalyzerDriver_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
Analysis_RobotdriversmoistureAnalyzerDriver_SOURCE_DIR:STATIC=D:/newfuxios/Analysis_Robot/drivers/moistureAnalyzerDriver

//Value Computed by CMake
Analysis_RobotdriversplcDriver_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/Analysis_Robot/drivers/plcDriver

//Value Computed by CMake
Analysis_RobotdriversplcDriver_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
Analysis_RobotdriversplcDriver_SOURCE_DIR:STATIC=D:/newfuxios/Analysis_Robot/drivers/plcDriver

//Value Computed by CMake
Analysis_RobotdriversrestInterfaceDriver_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/Analysis_Robot/drivers/restInterfaceDriver

//Value Computed by CMake
Analysis_RobotdriversrestInterfaceDriver_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
Analysis_RobotdriversrestInterfaceDriver_SOURCE_DIR:STATIC=D:/newfuxios/Analysis_Robot/drivers/restInterfaceDriver

//Value Computed by CMake
Analysis_RobotdriversrobotDriver_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/Analysis_Robot/drivers/robotDriver

//Value Computed by CMake
Analysis_RobotdriversrobotDriver_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
Analysis_RobotdriversrobotDriver_SOURCE_DIR:STATIC=D:/newfuxios/Analysis_Robot/drivers/robotDriver

//Value Computed by CMake
Analysis_Robottest_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/Analysis_Robot/test

//Value Computed by CMake
Analysis_Robottest_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
Analysis_Robottest_SOURCE_DIR:STATIC=D:/newfuxios/Analysis_Robot/test

//Value Computed by CMake
Analysis_RobottestaixsDriverTest_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/Analysis_Robot/test/aixsDriverTest

//Value Computed by CMake
Analysis_RobottestaixsDriverTest_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
Analysis_RobottestaixsDriverTest_SOURCE_DIR:STATIC=D:/newfuxios/Analysis_Robot/test/aixsDriverTest

//Value Computed by CMake
Analysis_RobottestbalanceDriverTest_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/Analysis_Robot/test/balanceDriverTest

//Value Computed by CMake
Analysis_RobottestbalanceDriverTest_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
Analysis_RobottestbalanceDriverTest_SOURCE_DIR:STATIC=D:/newfuxios/Analysis_Robot/test/balanceDriverTest

//Value Computed by CMake
Analysis_RobottestheaterApiTest_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/Analysis_Robot/test/heaterApiTest

//Value Computed by CMake
Analysis_RobottestheaterApiTest_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
Analysis_RobottestheaterApiTest_SOURCE_DIR:STATIC=D:/newfuxios/Analysis_Robot/test/heaterApiTest

//Value Computed by CMake
Analysis_RobottestheatingMagneticStirrerDriverTest_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/Analysis_Robot/test/heatingMagneticStirrerDriverTest

//Value Computed by CMake
Analysis_RobottestheatingMagneticStirrerDriverTest_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
Analysis_RobottestheatingMagneticStirrerDriverTest_SOURCE_DIR:STATIC=D:/newfuxios/Analysis_Robot/test/heatingMagneticStirrerDriverTest

//Value Computed by CMake
Analysis_RobottestheatingMagneticStirrerDriver_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/Analysis_Robot/test/heatingMagneticStirrerDriver

//Value Computed by CMake
Analysis_RobottestheatingMagneticStirrerDriver_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
Analysis_RobottestheatingMagneticStirrerDriver_SOURCE_DIR:STATIC=D:/newfuxios/Analysis_Robot/test/heatingMagneticStirrerDriver

//Value Computed by CMake
Analysis_RobottestmoistureAnalyzerDriverTest_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/Analysis_Robot/test/moistureAnalyzerDriverTest

//Value Computed by CMake
Analysis_RobottestmoistureAnalyzerDriverTest_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
Analysis_RobottestmoistureAnalyzerDriverTest_SOURCE_DIR:STATIC=D:/newfuxios/Analysis_Robot/test/moistureAnalyzerDriverTest

//Value Computed by CMake
Analysis_Robottestnative_stirrer_test_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/Analysis_Robot/test/native_stirrer_test

//Value Computed by CMake
Analysis_Robottestnative_stirrer_test_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
Analysis_Robottestnative_stirrer_test_SOURCE_DIR:STATIC=D:/newfuxios/Analysis_Robot/test/native_stirrer_test

//Value Computed by CMake
Analysis_RobottestpouringControlTest_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/Analysis_Robot/test/pouringControlTest

//Value Computed by CMake
Analysis_RobottestpouringControlTest_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
Analysis_RobottestpouringControlTest_SOURCE_DIR:STATIC=D:/newfuxios/Analysis_Robot/test/pouringControlTest

//Value Computed by CMake
Analysis_RobottestrestInterfaceDriverTest_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/Analysis_Robot/test/restInterfaceDriverTest

//Value Computed by CMake
Analysis_RobottestrestInterfaceDriverTest_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
Analysis_RobottestrestInterfaceDriverTest_SOURCE_DIR:STATIC=D:/newfuxios/Analysis_Robot/test/restInterfaceDriverTest

//Value Computed by CMake
Analysis_RobottestrobotDriverTest_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/Analysis_Robot/test/robotDriverTest

//Value Computed by CMake
Analysis_RobottestrobotDriverTest_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
Analysis_RobottestrobotDriverTest_SOURCE_DIR:STATIC=D:/newfuxios/Analysis_Robot/test/robotDriverTest

Boost_DATE_TIME_LIBRARY_DEBUG:STRING=C:/opt/PCL/3rdParty/Boost/lib/libboost_date_time-vc142-mt-gd-x64-1_78.lib

//The directory containing a CMake configuration file for Boost.
Boost_DIR:PATH=C:/opt/PCL/3rdParty/Boost/lib/cmake/Boost-1.78.0

Boost_FILESYSTEM_LIBRARY_DEBUG:STRING=C:/opt/PCL/3rdParty/Boost/lib/libboost_filesystem-vc142-mt-gd-x64-1_78.lib

//Path to a file.
Boost_INCLUDE_DIR:PATH=C:/opt/PCL/3rdParty/Boost/include/boost-1_78

Boost_IOSTREAMS_LIBRARY_DEBUG:STRING=C:/opt/PCL/3rdParty/Boost/lib/libboost_iostreams-vc142-mt-gd-x64-1_78.lib

Boost_PROGRAM_OPTIONS_LIBRARY_DEBUG:STRING=C:/opt/PCL/3rdParty/Boost/lib/libboost_program_options-vc142-mt-gd-x64-1_78.lib

Boost_SERIALIZATION_LIBRARY_DEBUG:STRING=C:/opt/PCL/3rdParty/Boost/lib/libboost_serialization-vc142-mt-gd-x64-1_78.lib

Boost_SYSTEM_LIBRARY_DEBUG:STRING=C:/opt/PCL/3rdParty/Boost/lib/libboost_system-vc142-mt-gd-x64-1_78.lib

Boost_THREAD_LIBRARY_DEBUG:STRING=C:/opt/PCL/3rdParty/Boost/lib/libboost_thread-vc142-mt-gd-x64-1_78.lib

//Path to a program.
CMAKE_AR:FILEPATH=C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/VC/Tools/MSVC/14.29.30133/bin/Hostx64/x64/lib.exe

//Choose the type of build, options are: None Debug Release RelWithDebInfo
// MinSizeRel ...
CMAKE_BUILD_TYPE:STRING=Debug

//Enable colored diagnostics throughout.
CMAKE_COLOR_DIAGNOSTICS:BOOL=ON

//CXX compiler
CMAKE_CXX_COMPILER:FILEPATH=C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/VC/Tools/MSVC/14.29.30133/bin/Hostx64/x64/cl.exe

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=/DWIN32 /D_WINDOWS /W3 /GR /EHsc

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=/MDd /Zi /Ob0 /Od /RTC1

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=/MD /O1 /Ob1 /DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=/MD /O2 /Ob2 /DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=/MD /Zi /O2 /Ob1 /DNDEBUG

//Libraries linked by default with all C++ applications.
CMAKE_CXX_STANDARD_LIBRARIES:STRING=kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib

//C compiler
CMAKE_C_COMPILER:FILEPATH=C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/VC/Tools/MSVC/14.29.30133/bin/Hostx64/x64/cl.exe

//Flags used by the C compiler during all build types.
CMAKE_C_FLAGS:STRING=/DWIN32 /D_WINDOWS /W3

//Flags used by the C compiler during DEBUG builds.
CMAKE_C_FLAGS_DEBUG:STRING=/MDd /Zi /Ob0 /Od /RTC1

//Flags used by the C compiler during MINSIZEREL builds.
CMAKE_C_FLAGS_MINSIZEREL:STRING=/MD /O1 /Ob1 /DNDEBUG

//Flags used by the C compiler during RELEASE builds.
CMAKE_C_FLAGS_RELEASE:STRING=/MD /O2 /Ob2 /DNDEBUG

//Flags used by the C compiler during RELWITHDEBINFO builds.
CMAKE_C_FLAGS_RELWITHDEBINFO:STRING=/MD /Zi /O2 /Ob1 /DNDEBUG

//Libraries linked by default with all C applications.
CMAKE_C_STANDARD_LIBRARIES:STRING=kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib

CMAKE_EXE_LINKER_FLAGS:STRING='/machine:x64                                                                                                                                                                                                                                                                        '

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=/debug /INCREMENTAL

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=/INCREMENTAL:NO

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=/INCREMENTAL:NO

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=/debug /INCREMENTAL

//Enable/Disable output of build database during the build.
CMAKE_EXPORT_BUILD_DATABASE:BOOL=

//Enable/Disable output of compile commands during generation.
CMAKE_EXPORT_COMPILE_COMMANDS:BOOL=

//Value Computed by CMake.
CMAKE_FIND_PACKAGE_REDIRECTS_DIR:STATIC=D:/newfuxios/cmake-build-debug/CMakeFiles/pkgRedirects

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=C:/Program Files (x86)/Project

//Path to a program.
CMAKE_LINKER:FILEPATH=C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/VC/Tools/MSVC/14.29.30133/bin/Hostx64/x64/link.exe

//make program
CMAKE_MAKE_PROGRAM:FILEPATH=C:/Program Files/JetBrains/CLion 2025.1.3/bin/ninja/win/x64/ninja.exe

CMAKE_MODULE_LINKER_FLAGS:STRING='/machine:x64                                                                                                                                                                                                                                                                        '

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=/debug /INCREMENTAL

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=/INCREMENTAL:NO

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=/INCREMENTAL:NO

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=/debug /INCREMENTAL

//Path to a program.
CMAKE_MT:FILEPATH=C:/Program Files (x86)/Windows Kits/10/bin/10.0.19041.0/x64/mt.exe

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=Project

//RC compiler
CMAKE_RC_COMPILER:FILEPATH=C:/Program Files (x86)/Windows Kits/10/bin/10.0.19041.0/x64/rc.exe

//Flags for Windows Resource Compiler during all build types.
CMAKE_RC_FLAGS:STRING=-DWIN32

//Flags for Windows Resource Compiler during DEBUG builds.
CMAKE_RC_FLAGS_DEBUG:STRING=-D_DEBUG

//Flags for Windows Resource Compiler during MINSIZEREL builds.
CMAKE_RC_FLAGS_MINSIZEREL:STRING=

//Flags for Windows Resource Compiler during RELEASE builds.
CMAKE_RC_FLAGS_RELEASE:STRING=

//Flags for Windows Resource Compiler during RELWITHDEBINFO builds.
CMAKE_RC_FLAGS_RELWITHDEBINFO:STRING=

CMAKE_SHARED_LINKER_FLAGS:STRING='/machine:x64                                                                                                                                                                                                                                                                        '

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=/debug /INCREMENTAL

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=/INCREMENTAL:NO

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=/INCREMENTAL:NO

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=/debug /INCREMENTAL

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=/machine:x64

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//Path to a library.
FOUND:FILEPATH=C:/opt/robwork-21.12/lib/sdurw_math.lib

//Value Computed by CMake
MJServerAPP_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/MJServer/APP

//Value Computed by CMake
MJServerAPP_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
MJServerAPP_SOURCE_DIR:STATIC=D:/newfuxios/MJServer/APP

//Value Computed by CMake
MJServer_RefactorApp_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/MJServer_Refactor/App

//Value Computed by CMake
MJServer_RefactorApp_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
MJServer_RefactorApp_SOURCE_DIR:STATIC=D:/newfuxios/MJServer_Refactor/App

//Value Computed by CMake
MJServer_RefactorLibrary_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/MJServer_Refactor/Library

//Value Computed by CMake
MJServer_RefactorLibrary_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
MJServer_RefactorLibrary_SOURCE_DIR:STATIC=D:/newfuxios/MJServer_Refactor/Library

//Value Computed by CMake
MJServer_RefactorTestphase1_test_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/MJServer_Refactor/Test/phase1_test

//Value Computed by CMake
MJServer_RefactorTestphase1_test_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
MJServer_RefactorTestphase1_test_SOURCE_DIR:STATIC=D:/newfuxios/MJServer_Refactor/Test/phase1_test

//Value Computed by CMake
MJServer_RefactorTestsimple_abb_client_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/MJServer_Refactor/Test/simple_abb_client

//Value Computed by CMake
MJServer_RefactorTestsimple_abb_client_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
MJServer_RefactorTestsimple_abb_client_SOURCE_DIR:STATIC=D:/newfuxios/MJServer_Refactor/Test/simple_abb_client

//Value Computed by CMake
MJServer_RefactorTestsimple_feeder_client_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/MJServer_Refactor/Test/simple_feeder_client

//Value Computed by CMake
MJServer_RefactorTestsimple_feeder_client_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
MJServer_RefactorTestsimple_feeder_client_SOURCE_DIR:STATIC=D:/newfuxios/MJServer_Refactor/Test/simple_feeder_client

//The directory containing a CMake configuration file for OpenCV.
OpenCV_DIR:PATH=C:/opt/opencv/build

//Value Computed by CMake
Project_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug

//Value Computed by CMake
Project_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
Project_SOURCE_DIR:STATIC=D:/newfuxios

//The directory containing a CMake configuration file for Qt5Charts.
Qt5Charts_DIR:PATH=C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Charts

//The directory containing a CMake configuration file for Qt5Core.
Qt5Core_DIR:PATH=C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Core

//The directory containing a CMake configuration file for Qt5Gui.
Qt5Gui_DIR:PATH=C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Gui

//The directory containing a CMake configuration file for Qt5Network.
Qt5Network_DIR:PATH=C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Network

//The directory containing a CMake configuration file for Qt5SerialPort.
Qt5SerialPort_DIR:PATH=C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5SerialPort

//The directory containing a CMake configuration file for Qt5Sql.
Qt5Sql_DIR:PATH=C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Sql

//The directory containing a CMake configuration file for Qt5Test.
Qt5Test_DIR:PATH=C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Test

//The directory containing a CMake configuration file for Qt5Widgets.
Qt5Widgets_DIR:PATH=C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5Widgets

//The directory containing a CMake configuration file for Qt5.
Qt5_DIR:PATH=C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5

//RobWork version
ROBWORK_VERSION:STRING=21.12.14

//The directory containing a CMake configuration file for RTTR.
RTTR_DIR:PATH=C:/opt/rttr/cmake

//Change this to force using your own
//\n                      flags and not those of RobWork
RW_CXX_FLAGS:STRING=-EHa -bigobj /MP -openmp

//Change this to force using your own
//\n                      flags and not those of RobWork
RW_C_FLAGS:STRING=

//Change this to force using your own
//\n                      definitions and not those of RobWork
RW_DEFINITIONS:STRING=-DNOMINMAX;-DBIND_FORTRAN_LOWERCASE_UNDERSCORE;-DWIN32_LEAN_AND_MEAN;-D_WIN32_WINNT=0x0501;-D_SCL_SECURE_NO_WARNINGS;-D_CRT_SECURE_NO_WARNINGS;-D_CRT_SECURE_NO_DEPRECATE;-DMSVC_AMD64

//Change this to force using your own linker
//\n                      flags and not those of RobWork
RW_LINKER_FLAGS:STRING=

//The directory containing a CMake configuration file for RobWork.
RobWork_DIR:PATH=C:/opt/robwork-21.12/robwork-21.12/cmake

//Value Computed by CMake
RoboticLaserMarkingAbbDriver_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/RoboticLaserMarking/AbbDriver

//Value Computed by CMake
RoboticLaserMarkingAbbDriver_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
RoboticLaserMarkingAbbDriver_SOURCE_DIR:STATIC=D:/newfuxios/RoboticLaserMarking/AbbDriver

//Value Computed by CMake
RoboticLaserMarkingLicenseGenerator_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/RoboticLaserMarking/LicenseGenerator

//Value Computed by CMake
RoboticLaserMarkingLicenseGenerator_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
RoboticLaserMarkingLicenseGenerator_SOURCE_DIR:STATIC=D:/newfuxios/RoboticLaserMarking/LicenseGenerator

//Value Computed by CMake
RoboticLaserMarkingRFIDDriver_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/RoboticLaserMarking/RFIDDriver

//Value Computed by CMake
RoboticLaserMarkingRFIDDriver_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
RoboticLaserMarkingRFIDDriver_SOURCE_DIR:STATIC=D:/newfuxios/RoboticLaserMarking/RFIDDriver

//Value Computed by CMake
RoboticLaserMarkingTestabbsocket_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/RoboticLaserMarking/Test/abbsocket

//Value Computed by CMake
RoboticLaserMarkingTestabbsocket_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
RoboticLaserMarkingTestabbsocket_SOURCE_DIR:STATIC=D:/newfuxios/RoboticLaserMarking/Test/abbsocket

//Value Computed by CMake
RoboticLaserMarkingTestlaserUI_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/RoboticLaserMarking/Test/laserUI

//Value Computed by CMake
RoboticLaserMarkingTestlaserUI_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
RoboticLaserMarkingTestlaserUI_SOURCE_DIR:STATIC=D:/newfuxios/RoboticLaserMarking/Test/laserUI

//Value Computed by CMake
RoboticLaserMarkingTestlaser_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/RoboticLaserMarking/Test/laser

//Value Computed by CMake
RoboticLaserMarkingTestlaser_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
RoboticLaserMarkingTestlaser_SOURCE_DIR:STATIC=D:/newfuxios/RoboticLaserMarking/Test/laser

//Value Computed by CMake
RoboticLaserMarkingTestrfiddriver_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/RoboticLaserMarking/Test/rfiddriver

//Value Computed by CMake
RoboticLaserMarkingTestrfiddriver_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
RoboticLaserMarkingTestrfiddriver_SOURCE_DIR:STATIC=D:/newfuxios/RoboticLaserMarking/Test/rfiddriver

//Value Computed by CMake
RoboticLaserMarkingTestrfidserver_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/RoboticLaserMarking/Test/rfidserver

//Value Computed by CMake
RoboticLaserMarkingTestrfidserver_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
RoboticLaserMarkingTestrfidserver_SOURCE_DIR:STATIC=D:/newfuxios/RoboticLaserMarking/Test/rfidserver

//Value Computed by CMake
RoboticLaserMarkingUI_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/RoboticLaserMarking/UI

//Value Computed by CMake
RoboticLaserMarkingUI_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
RoboticLaserMarkingUI_SOURCE_DIR:STATIC=D:/newfuxios/RoboticLaserMarking/UI

//Value Computed by CMake
RoboticLaserMarkinglaserDriverSim_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/RoboticLaserMarking/laserDriverSim

//Value Computed by CMake
RoboticLaserMarkinglaserDriverSim_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
RoboticLaserMarkinglaserDriverSim_SOURCE_DIR:STATIC=D:/newfuxios/RoboticLaserMarking/laserDriverSim

//Value Computed by CMake
RoboticLaserMarkinglaserDriver_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/RoboticLaserMarking/laserDriver

//Value Computed by CMake
RoboticLaserMarkinglaserDriver_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
RoboticLaserMarkinglaserDriver_SOURCE_DIR:STATIC=D:/newfuxios/RoboticLaserMarking/laserDriver

//Value Computed by CMake
Testtest_abb_socket_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/Test/test_abb_socket

//Value Computed by CMake
Testtest_abb_socket_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
Testtest_abb_socket_SOURCE_DIR:STATIC=D:/newfuxios/Test/test_abb_socket

//Value Computed by CMake
Testtest_config_manager_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/Test/test_config_manager

//Value Computed by CMake
Testtest_config_manager_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
Testtest_config_manager_SOURCE_DIR:STATIC=D:/newfuxios/Test/test_config_manager

//Value Computed by CMake
Testtest_csv_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/Test/test_csv

//Value Computed by CMake
Testtest_csv_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
Testtest_csv_SOURCE_DIR:STATIC=D:/newfuxios/Test/test_csv

//Value Computed by CMake
Testtest_event_listener_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/Test/test_event_listener

//Value Computed by CMake
Testtest_event_listener_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
Testtest_event_listener_SOURCE_DIR:STATIC=D:/newfuxios/Test/test_event_listener

//Value Computed by CMake
Testtest_executor_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/Test/test_executor

//Value Computed by CMake
Testtest_executor_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
Testtest_executor_SOURCE_DIR:STATIC=D:/newfuxios/Test/test_executor

//Value Computed by CMake
Testtest_executor_context_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/Test/test_executor_context

//Value Computed by CMake
Testtest_executor_context_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
Testtest_executor_context_SOURCE_DIR:STATIC=D:/newfuxios/Test/test_executor_context

//Value Computed by CMake
Testtest_fa2204n_balance_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/Test/test_fa2204n_balance

//Value Computed by CMake
Testtest_fa2204n_balance_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
Testtest_fa2204n_balance_SOURCE_DIR:STATIC=D:/newfuxios/Test/test_fa2204n_balance

//Value Computed by CMake
Testtest_fa2204n_balance_basic_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/Test/test_fa2204n_balance_basic

//Value Computed by CMake
Testtest_fa2204n_balance_basic_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
Testtest_fa2204n_balance_basic_SOURCE_DIR:STATIC=D:/newfuxios/Test/test_fa2204n_balance_basic

//Value Computed by CMake
Testtest_fileutil_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/Test/test_fileutil

//Value Computed by CMake
Testtest_fileutil_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
Testtest_fileutil_SOURCE_DIR:STATIC=D:/newfuxios/Test/test_fileutil

//Value Computed by CMake
Testtest_json_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/Test/test_json

//Value Computed by CMake
Testtest_json_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
Testtest_json_SOURCE_DIR:STATIC=D:/newfuxios/Test/test_json

//Value Computed by CMake
Testtest_license_manager_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/Test/test_license_manager

//Value Computed by CMake
Testtest_license_manager_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
Testtest_license_manager_SOURCE_DIR:STATIC=D:/newfuxios/Test/test_license_manager

//Value Computed by CMake
Testtest_license_ui_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/Test/test_license_ui

//Value Computed by CMake
Testtest_license_ui_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
Testtest_license_ui_SOURCE_DIR:STATIC=D:/newfuxios/Test/test_license_ui

//Value Computed by CMake
Testtest_network_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/Test/test_network

//Value Computed by CMake
Testtest_network_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
Testtest_network_SOURCE_DIR:STATIC=D:/newfuxios/Test/test_network

//Value Computed by CMake
Testtest_serial_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/Test/test_serial

//Value Computed by CMake
Testtest_serial_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
Testtest_serial_SOURCE_DIR:STATIC=D:/newfuxios/Test/test_serial

//Value Computed by CMake
Testtest_service_container_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/Test/test_service_container

//Value Computed by CMake
Testtest_service_container_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
Testtest_service_container_SOURCE_DIR:STATIC=D:/newfuxios/Test/test_service_container

//Value Computed by CMake
Testtest_socket_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/Test/test_socket

//Value Computed by CMake
Testtest_socket_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
Testtest_socket_SOURCE_DIR:STATIC=D:/newfuxios/Test/test_socket

//Value Computed by CMake
Testtest_sqlite_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/Test/test_sqlite

//Value Computed by CMake
Testtest_sqlite_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
Testtest_sqlite_SOURCE_DIR:STATIC=D:/newfuxios/Test/test_sqlite

//Value Computed by CMake
Testtest_taskflow_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/Test/test_taskflow

//Value Computed by CMake
Testtest_taskflow_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
Testtest_taskflow_SOURCE_DIR:STATIC=D:/newfuxios/Test/test_taskflow

//Value Computed by CMake
Testtest_twoaixsrobot_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/Test/test_twoaixsrobot

//Value Computed by CMake
Testtest_twoaixsrobot_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
Testtest_twoaixsrobot_SOURCE_DIR:STATIC=D:/newfuxios/Test/test_twoaixsrobot

//Value Computed by CMake
Testtest_xml_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/Test/test_xml

//Value Computed by CMake
Testtest_xml_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
Testtest_xml_SOURCE_DIR:STATIC=D:/newfuxios/Test/test_xml

//The directory containing a CMake configuration file for boost_atomic.
boost_atomic_DIR:PATH=C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_atomic-1.78.0

//The directory containing a CMake configuration file for boost_chrono.
boost_chrono_DIR:PATH=C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_chrono-1.78.0

//The directory containing a CMake configuration file for boost_date_time.
boost_date_time_DIR:PATH=C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_date_time-1.78.0

//The directory containing a CMake configuration file for boost_filesystem.
boost_filesystem_DIR:PATH=C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_filesystem-1.78.0

//The directory containing a CMake configuration file for boost_headers.
boost_headers_DIR:PATH=C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_headers-1.78.0

//The directory containing a CMake configuration file for boost_iostreams.
boost_iostreams_DIR:PATH=C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_iostreams-1.78.0

//The directory containing a CMake configuration file for boost_program_options.
boost_program_options_DIR:PATH=C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_program_options-1.78.0

//The directory containing a CMake configuration file for boost_serialization.
boost_serialization_DIR:PATH=C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_serialization-1.78.0

//The directory containing a CMake configuration file for boost_system.
boost_system_DIR:PATH=C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_system-1.78.0

//The directory containing a CMake configuration file for boost_thread.
boost_thread_DIR:PATH=C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_thread-1.78.0

//Value Computed by CMake
fuxicommon_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/fuxicommon

//Value Computed by CMake
fuxicommon_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
fuxicommon_SOURCE_DIR:STATIC=D:/newfuxios/fuxicommon

//Value Computed by CMake
fuxicore_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/fuxicore

//Value Computed by CMake
fuxicore_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
fuxicore_SOURCE_DIR:STATIC=D:/newfuxios/fuxicore

//Value Computed by CMake
hardwaredriverAuboArcsDriver_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/hardwaredriver/AuboArcsDriver

//Value Computed by CMake
hardwaredriverAuboArcsDriver_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
hardwaredriverAuboArcsDriver_SOURCE_DIR:STATIC=D:/newfuxios/hardwaredriver/AuboArcsDriver

//Value Computed by CMake
hardwaredriverAuboDriver_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/hardwaredriver/AuboDriver

//Value Computed by CMake
hardwaredriverAuboDriver_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
hardwaredriverAuboDriver_SOURCE_DIR:STATIC=D:/newfuxios/hardwaredriver/AuboDriver

//Value Computed by CMake
hardwaredriverElectricGripperDriver_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/hardwaredriver/ElectricGripperDriver

//Value Computed by CMake
hardwaredriverElectricGripperDriver_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
hardwaredriverElectricGripperDriver_SOURCE_DIR:STATIC=D:/newfuxios/hardwaredriver/ElectricGripperDriver

//Value Computed by CMake
hardwaredriverHikVisionCamera_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/hardwaredriver/HikVisionCamera

//Value Computed by CMake
hardwaredriverHikVisionCamera_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
hardwaredriverHikVisionCamera_SOURCE_DIR:STATIC=D:/newfuxios/hardwaredriver/HikVisionCamera

//Value Computed by CMake
hardwaredriverLabelPrinter_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/hardwaredriver/LabelPrinter

//Value Computed by CMake
hardwaredriverLabelPrinter_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
hardwaredriverLabelPrinter_SOURCE_DIR:STATIC=D:/newfuxios/hardwaredriver/LabelPrinter

//Value Computed by CMake
hardwaredriverMettlerBalance_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/hardwaredriver/MettlerBalance

//Value Computed by CMake
hardwaredriverMettlerBalance_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
hardwaredriverMettlerBalance_SOURCE_DIR:STATIC=D:/newfuxios/hardwaredriver/MettlerBalance

//Value Computed by CMake
hardwaredriverOpcDa_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/hardwaredriver/OpcDa

//Value Computed by CMake
hardwaredriverOpcDa_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
hardwaredriverOpcDa_SOURCE_DIR:STATIC=D:/newfuxios/hardwaredriver/OpcDa

//Value Computed by CMake
hardwaredriverOpcUa_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/hardwaredriver/OpcUa

//Value Computed by CMake
hardwaredriverOpcUa_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
hardwaredriverOpcUa_SOURCE_DIR:STATIC=D:/newfuxios/hardwaredriver/OpcUa

//Value Computed by CMake
hardwaredriverabbRobotDriver_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/hardwaredriver/abbRobotDriver

//Value Computed by CMake
hardwaredriverabbRobotDriver_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
hardwaredriverabbRobotDriver_SOURCE_DIR:STATIC=D:/newfuxios/hardwaredriver/abbRobotDriver

//Value Computed by CMake
hardwaredriveragilerobotDriver_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/hardwaredriver/agilerobotDriver

//Value Computed by CMake
hardwaredriveragilerobotDriver_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
hardwaredriveragilerobotDriver_SOURCE_DIR:STATIC=D:/newfuxios/hardwaredriver/agilerobotDriver

//Value Computed by CMake
hardwaredriverfairinoDriver_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/hardwaredriver/fairinoDriver

//Value Computed by CMake
hardwaredriverfairinoDriver_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
hardwaredriverfairinoDriver_SOURCE_DIR:STATIC=D:/newfuxios/hardwaredriver/fairinoDriver

//Value Computed by CMake
hardwaredriverjunduoHandDriver_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/hardwaredriver/junduoHandDriver

//Value Computed by CMake
hardwaredriverjunduoHandDriver_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
hardwaredriverjunduoHandDriver_SOURCE_DIR:STATIC=D:/newfuxios/hardwaredriver/junduoHandDriver

//Value Computed by CMake
hardwaredrivermodbus_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/hardwaredriver/modbus

//Value Computed by CMake
hardwaredrivermodbus_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
hardwaredrivermodbus_SOURCE_DIR:STATIC=D:/newfuxios/hardwaredriver/modbus

//Value Computed by CMake
hardwaredriverserial_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/hardwaredriver/serial

//Value Computed by CMake
hardwaredriverserial_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
hardwaredriverserial_SOURCE_DIR:STATIC=D:/newfuxios/hardwaredriver/serial

//Value Computed by CMake
hardwaredriversocket_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/hardwaredriver/socket

//Value Computed by CMake
hardwaredriversocket_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
hardwaredriversocket_SOURCE_DIR:STATIC=D:/newfuxios/hardwaredriver/socket

//Value Computed by CMake
hardwaredriverusbcamera_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/hardwaredriver/usbcamera

//Value Computed by CMake
hardwaredriverusbcamera_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
hardwaredriverusbcamera_SOURCE_DIR:STATIC=D:/newfuxios/hardwaredriver/usbcamera

//Value Computed by CMake
test_micro_dosing_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/Test/test_micro_dosing

//Value Computed by CMake
test_micro_dosing_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
test_micro_dosing_SOURCE_DIR:STATIC=D:/newfuxios/Test/test_micro_dosing

//Path to a library.
tmp:FILEPATH=C:/opt/robwork-21.12/lib/libboost_unit_test_framework-vc142-mt-gd-x64-1_78.lib

//Value Computed by CMake
toolcalbuild_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/tool/calbuild

//Value Computed by CMake
toolcalbuild_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
toolcalbuild_SOURCE_DIR:STATIC=D:/newfuxios/tool/calbuild

//Value Computed by CMake
toolcaltest_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/tool/caltest

//Value Computed by CMake
toolcaltest_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
toolcaltest_SOURCE_DIR:STATIC=D:/newfuxios/tool/caltest

//Value Computed by CMake
toolcameraCalibrator_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/tool/cameraCalibrator

//Value Computed by CMake
toolcameraCalibrator_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
toolcameraCalibrator_SOURCE_DIR:STATIC=D:/newfuxios/tool/cameraCalibrator

//Value Computed by CMake
toolcommunication_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/tool/communication

//Value Computed by CMake
toolcommunication_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
toolcommunication_SOURCE_DIR:STATIC=D:/newfuxios/tool/communication

//Value Computed by CMake
toolhandeyecal_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/tool/handeyecal

//Value Computed by CMake
toolhandeyecal_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
toolhandeyecal_SOURCE_DIR:STATIC=D:/newfuxios/tool/handeyecal

//Value Computed by CMake
toolhandeyecaltest_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/tool/handeyecaltest

//Value Computed by CMake
toolhandeyecaltest_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
toolhandeyecaltest_SOURCE_DIR:STATIC=D:/newfuxios/tool/handeyecaltest

//Value Computed by CMake
toolhandeyecaluihandeyecalui_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/tool/handeyecalui/handeyecalui

//Value Computed by CMake
toolhandeyecaluihandeyecalui_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
toolhandeyecaluihandeyecalui_SOURCE_DIR:STATIC=D:/newfuxios/tool/handeyecalui/handeyecalui

//Value Computed by CMake
toolhandeyecaluipathAuto_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/tool/handeyecaluipathAuto

//Value Computed by CMake
toolhandeyecaluipathAuto_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
toolhandeyecaluipathAuto_SOURCE_DIR:STATIC=D:/newfuxios/tool/handeyecaluipathAuto

//Value Computed by CMake
toolhandeyecaluipath_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/tool/handeyecaluipath

//Value Computed by CMake
toolhandeyecaluipath_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
toolhandeyecaluipath_SOURCE_DIR:STATIC=D:/newfuxios/tool/handeyecaluipath

//Value Computed by CMake
toolverify_calibration_BINARY_DIR:STATIC=D:/newfuxios/cmake-build-debug/tool/verify_calibration

//Value Computed by CMake
toolverify_calibration_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
toolverify_calibration_SOURCE_DIR:STATIC=D:/newfuxios/tool/verify_calibration


########################
# INTERNAL cache entries
########################

//ADVANCED property for variable: Boost_DIR
Boost_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=d:/newfuxios/cmake-build-debug
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=3
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=31
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=6
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=C:/Program Files/JetBrains/CLion 2025.1.3/bin/cmake/win/x64/bin/cmake.exe
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=C:/Program Files/JetBrains/CLion 2025.1.3/bin/cmake/win/x64/bin/cpack.exe
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=C:/Program Files/JetBrains/CLion 2025.1.3/bin/cmake/win/x64/bin/ctest.exe
//ADVANCED property for variable: CMAKE_CXX_COMPILER
CMAKE_CXX_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_STANDARD_LIBRARIES
CMAKE_CXX_STANDARD_LIBRARIES-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER
CMAKE_C_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS
CMAKE_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_DEBUG
CMAKE_C_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_MINSIZEREL
CMAKE_C_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELEASE
CMAKE_C_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELWITHDEBINFO
CMAKE_C_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_STANDARD_LIBRARIES
CMAKE_C_STANDARD_LIBRARIES-ADVANCED:INTERNAL=1
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=Unknown
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXPORT_BUILD_DATABASE
CMAKE_EXPORT_BUILD_DATABASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXPORT_COMPILE_COMMANDS
CMAKE_EXPORT_COMPILE_COMMANDS-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Ninja
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Test CMAKE_HAVE_LIBC_PTHREAD
CMAKE_HAVE_LIBC_PTHREAD:INTERNAL=
//Have library pthreads
CMAKE_HAVE_PTHREADS_CREATE:INTERNAL=
//Have library pthread
CMAKE_HAVE_PTHREAD_CREATE:INTERNAL=
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=D:/newfuxios
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MT
CMAKE_MT-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=81
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//noop for ranlib
CMAKE_RANLIB:INTERNAL=:
//ADVANCED property for variable: CMAKE_RC_COMPILER
CMAKE_RC_COMPILER-ADVANCED:INTERNAL=1
CMAKE_RC_COMPILER_WORKS:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS
CMAKE_RC_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_DEBUG
CMAKE_RC_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_MINSIZEREL
CMAKE_RC_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_RELEASE
CMAKE_RC_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_RELWITHDEBINFO
CMAKE_RC_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=C:/Program Files/JetBrains/CLion 2025.1.3/bin/cmake/win/x64/share/cmake-3.31
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//Details about finding Boost
FIND_PACKAGE_MESSAGE_DETAILS_Boost:INTERNAL=[C:/opt/PCL/3rdParty/Boost/lib/cmake/Boost-1.78.0/BoostConfig.cmake][cfound components: system filesystem date_time iostreams serialization thread program_options ][v1.78.0(1.65.0)]
//Details about finding OpenCV
FIND_PACKAGE_MESSAGE_DETAILS_OpenCV:INTERNAL=[C:/opt/opencv/build][v4.11.0()]
//ADVANCED property for variable: boost_atomic_DIR
boost_atomic_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: boost_chrono_DIR
boost_chrono_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: boost_date_time_DIR
boost_date_time_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: boost_filesystem_DIR
boost_filesystem_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: boost_headers_DIR
boost_headers_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: boost_iostreams_DIR
boost_iostreams_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: boost_program_options_DIR
boost_program_options_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: boost_serialization_DIR
boost_serialization_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: boost_system_DIR
boost_system_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: boost_thread_DIR
boost_thread_DIR-ADVANCED:INTERNAL=1

