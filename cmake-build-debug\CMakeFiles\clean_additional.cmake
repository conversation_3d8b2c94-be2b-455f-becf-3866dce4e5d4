# Additional clean files
cmake_minimum_required(VERSION 3.16)

if("${CONFIG}" STREQUAL "" OR "${CONFIG}" STREQUAL "Debug")
  file(REMOVE_RECURSE
  "MJServer\\APP\\CMakeFiles\\MJServerAPP_autogen.dir\\AutogenUsed.txt"
  "MJServer\\APP\\CMakeFiles\\MJServerAPP_autogen.dir\\ParseCache.txt"
  "MJServer\\APP\\MJServerAPP_autogen"
  "MJServer_Refactor\\App\\CMakeFiles\\MJServer_RefactorApp_autogen.dir\\AutogenUsed.txt"
  "MJServer_Refactor\\App\\CMakeFiles\\MJServer_RefactorApp_autogen.dir\\ParseCache.txt"
  "MJServer_Refactor\\App\\MJServer_RefactorApp_autogen"
  "MJServer_Refactor\\Library\\CMakeFiles\\MJServer_RefactorLibrary_autogen.dir\\AutogenUsed.txt"
  "MJServer_Refactor\\Library\\CMakeFiles\\MJServer_RefactorLibrary_autogen.dir\\ParseCache.txt"
  "MJServer_Refactor\\Library\\MJServer_RefactorLibrary_autogen"
  "MJServer_Refactor\\Test\\phase1_test\\CMakeFiles\\MJServer_RefactorTestphase1_test_autogen.dir\\AutogenUsed.txt"
  "MJServer_Refactor\\Test\\phase1_test\\CMakeFiles\\MJServer_RefactorTestphase1_test_autogen.dir\\ParseCache.txt"
  "MJServer_Refactor\\Test\\phase1_test\\MJServer_RefactorTestphase1_test_autogen"
  "MJServer_Refactor\\Test\\simple_abb_client\\CMakeFiles\\MJServer_RefactorTestsimple_abb_client_autogen.dir\\AutogenUsed.txt"
  "MJServer_Refactor\\Test\\simple_abb_client\\CMakeFiles\\MJServer_RefactorTestsimple_abb_client_autogen.dir\\ParseCache.txt"
  "MJServer_Refactor\\Test\\simple_abb_client\\MJServer_RefactorTestsimple_abb_client_autogen"
  "MJServer_Refactor\\Test\\simple_feeder_client\\CMakeFiles\\MJServer_RefactorTestsimple_feeder_client_autogen.dir\\AutogenUsed.txt"
  "MJServer_Refactor\\Test\\simple_feeder_client\\CMakeFiles\\MJServer_RefactorTestsimple_feeder_client_autogen.dir\\ParseCache.txt"
  "MJServer_Refactor\\Test\\simple_feeder_client\\MJServer_RefactorTestsimple_feeder_client_autogen"
  "RoboticLaserMarking\\LicenseGenerator\\CMakeFiles\\RoboticLaserMarkingLicenseGenerator_autogen.dir\\AutogenUsed.txt"
  "RoboticLaserMarking\\LicenseGenerator\\CMakeFiles\\RoboticLaserMarkingLicenseGenerator_autogen.dir\\ParseCache.txt"
  "RoboticLaserMarking\\LicenseGenerator\\RoboticLaserMarkingLicenseGenerator_autogen"
  "RoboticLaserMarking\\Test\\abbsocket\\CMakeFiles\\RoboticLaserMarkingTestabbsocket_autogen.dir\\AutogenUsed.txt"
  "RoboticLaserMarking\\Test\\abbsocket\\CMakeFiles\\RoboticLaserMarkingTestabbsocket_autogen.dir\\ParseCache.txt"
  "RoboticLaserMarking\\Test\\abbsocket\\RoboticLaserMarkingTestabbsocket_autogen"
  "RoboticLaserMarking\\Test\\laser\\CMakeFiles\\RoboticLaserMarkingTestlaser_autogen.dir\\AutogenUsed.txt"
  "RoboticLaserMarking\\Test\\laser\\CMakeFiles\\RoboticLaserMarkingTestlaser_autogen.dir\\ParseCache.txt"
  "RoboticLaserMarking\\Test\\laser\\RoboticLaserMarkingTestlaser_autogen"
  "RoboticLaserMarking\\Test\\laserUI\\CMakeFiles\\RoboticLaserMarkingTestlaserUI_autogen.dir\\AutogenUsed.txt"
  "RoboticLaserMarking\\Test\\laserUI\\CMakeFiles\\RoboticLaserMarkingTestlaserUI_autogen.dir\\ParseCache.txt"
  "RoboticLaserMarking\\Test\\laserUI\\RoboticLaserMarkingTestlaserUI_autogen"
  "RoboticLaserMarking\\Test\\rfiddriver\\CMakeFiles\\RoboticLaserMarkingTestrfiddriver_autogen.dir\\AutogenUsed.txt"
  "RoboticLaserMarking\\Test\\rfiddriver\\CMakeFiles\\RoboticLaserMarkingTestrfiddriver_autogen.dir\\ParseCache.txt"
  "RoboticLaserMarking\\Test\\rfiddriver\\RoboticLaserMarkingTestrfiddriver_autogen"
  "RoboticLaserMarking\\Test\\rfidserver\\CMakeFiles\\RoboticLaserMarkingTestrfidserver_autogen.dir\\AutogenUsed.txt"
  "RoboticLaserMarking\\Test\\rfidserver\\CMakeFiles\\RoboticLaserMarkingTestrfidserver_autogen.dir\\ParseCache.txt"
  "RoboticLaserMarking\\Test\\rfidserver\\RoboticLaserMarkingTestrfidserver_autogen"
  "RoboticLaserMarking\\UI\\CMakeFiles\\RoboticLaserMarkingUI_autogen.dir\\AutogenUsed.txt"
  "RoboticLaserMarking\\UI\\CMakeFiles\\RoboticLaserMarkingUI_autogen.dir\\ParseCache.txt"
  "RoboticLaserMarking\\UI\\RoboticLaserMarkingUI_autogen"
  "Test\\test_abb_socket\\CMakeFiles\\Testtest_abb_socket_autogen.dir\\AutogenUsed.txt"
  "Test\\test_abb_socket\\CMakeFiles\\Testtest_abb_socket_autogen.dir\\ParseCache.txt"
  "Test\\test_abb_socket\\Testtest_abb_socket_autogen"
  "Test\\test_license_manager\\CMakeFiles\\Testtest_license_manager_autogen.dir\\AutogenUsed.txt"
  "Test\\test_license_manager\\CMakeFiles\\Testtest_license_manager_autogen.dir\\ParseCache.txt"
  "Test\\test_license_manager\\Testtest_license_manager_autogen"
  "Test\\test_license_ui\\CMakeFiles\\Testtest_license_ui_autogen.dir\\AutogenUsed.txt"
  "Test\\test_license_ui\\CMakeFiles\\Testtest_license_ui_autogen.dir\\ParseCache.txt"
  "Test\\test_license_ui\\Testtest_license_ui_autogen"
  "Test\\test_twoaixsrobot\\CMakeFiles\\Testtest_twoaixsrobot_autogen.dir\\AutogenUsed.txt"
  "Test\\test_twoaixsrobot\\CMakeFiles\\Testtest_twoaixsrobot_autogen.dir\\ParseCache.txt"
  "Test\\test_twoaixsrobot\\Testtest_twoaixsrobot_autogen"
  "fuxicommon\\CMakeFiles\\fuxicommon_autogen.dir\\AutogenUsed.txt"
  "fuxicommon\\CMakeFiles\\fuxicommon_autogen.dir\\ParseCache.txt"
  "fuxicommon\\fuxicommon_autogen"
  "fuxicore\\CMakeFiles\\fuxicore_autogen.dir\\AutogenUsed.txt"
  "fuxicore\\CMakeFiles\\fuxicore_autogen.dir\\ParseCache.txt"
  "fuxicore\\fuxicore_autogen"
  "hardwaredriver\\AuboArcsDriver\\CMakeFiles\\hardwaredriverAuboArcsDriver_autogen.dir\\AutogenUsed.txt"
  "hardwaredriver\\AuboArcsDriver\\CMakeFiles\\hardwaredriverAuboArcsDriver_autogen.dir\\ParseCache.txt"
  "hardwaredriver\\AuboArcsDriver\\hardwaredriverAuboArcsDriver_autogen"
  "hardwaredriver\\AuboDriver\\CMakeFiles\\hardwaredriverAuboDriver_autogen.dir\\AutogenUsed.txt"
  "hardwaredriver\\AuboDriver\\CMakeFiles\\hardwaredriverAuboDriver_autogen.dir\\ParseCache.txt"
  "hardwaredriver\\AuboDriver\\hardwaredriverAuboDriver_autogen"
  "hardwaredriver\\ElectricGripperDriver\\CMakeFiles\\hardwaredriverElectricGripperDriver_autogen.dir\\AutogenUsed.txt"
  "hardwaredriver\\ElectricGripperDriver\\CMakeFiles\\hardwaredriverElectricGripperDriver_autogen.dir\\ParseCache.txt"
  "hardwaredriver\\ElectricGripperDriver\\hardwaredriverElectricGripperDriver_autogen"
  "hardwaredriver\\HikVisionCamera\\CMakeFiles\\hardwaredriverHikVisionCamera_autogen.dir\\AutogenUsed.txt"
  "hardwaredriver\\HikVisionCamera\\CMakeFiles\\hardwaredriverHikVisionCamera_autogen.dir\\ParseCache.txt"
  "hardwaredriver\\HikVisionCamera\\hardwaredriverHikVisionCamera_autogen"
  "hardwaredriver\\LabelPrinter\\CMakeFiles\\hardwaredriverLabelPrinter_autogen.dir\\AutogenUsed.txt"
  "hardwaredriver\\LabelPrinter\\CMakeFiles\\hardwaredriverLabelPrinter_autogen.dir\\ParseCache.txt"
  "hardwaredriver\\LabelPrinter\\hardwaredriverLabelPrinter_autogen"
  "hardwaredriver\\MettlerBalance\\CMakeFiles\\hardwaredriverMettlerBalance_autogen.dir\\AutogenUsed.txt"
  "hardwaredriver\\MettlerBalance\\CMakeFiles\\hardwaredriverMettlerBalance_autogen.dir\\ParseCache.txt"
  "hardwaredriver\\MettlerBalance\\hardwaredriverMettlerBalance_autogen"
  "hardwaredriver\\OpcDa\\CMakeFiles\\hardwaredriverOpcDa_autogen.dir\\AutogenUsed.txt"
  "hardwaredriver\\OpcDa\\CMakeFiles\\hardwaredriverOpcDa_autogen.dir\\ParseCache.txt"
  "hardwaredriver\\OpcDa\\hardwaredriverOpcDa_autogen"
  "hardwaredriver\\OpcUa\\CMakeFiles\\hardwaredriverOpcUa_autogen.dir\\AutogenUsed.txt"
  "hardwaredriver\\OpcUa\\CMakeFiles\\hardwaredriverOpcUa_autogen.dir\\ParseCache.txt"
  "hardwaredriver\\OpcUa\\hardwaredriverOpcUa_autogen"
  "hardwaredriver\\abbRobotDriver\\CMakeFiles\\hardwaredriverabbRobotDriver_autogen.dir\\AutogenUsed.txt"
  "hardwaredriver\\abbRobotDriver\\CMakeFiles\\hardwaredriverabbRobotDriver_autogen.dir\\ParseCache.txt"
  "hardwaredriver\\abbRobotDriver\\hardwaredriverabbRobotDriver_autogen"
  "hardwaredriver\\agilerobotDriver\\CMakeFiles\\hardwaredriveragilerobotDriver_autogen.dir\\AutogenUsed.txt"
  "hardwaredriver\\agilerobotDriver\\CMakeFiles\\hardwaredriveragilerobotDriver_autogen.dir\\ParseCache.txt"
  "hardwaredriver\\agilerobotDriver\\hardwaredriveragilerobotDriver_autogen"
  "hardwaredriver\\modbus\\CMakeFiles\\hardwaredrivermodbus_autogen.dir\\AutogenUsed.txt"
  "hardwaredriver\\modbus\\CMakeFiles\\hardwaredrivermodbus_autogen.dir\\ParseCache.txt"
  "hardwaredriver\\modbus\\hardwaredrivermodbus_autogen"
  "hardwaredriver\\serial\\CMakeFiles\\hardwaredriverserial_autogen.dir\\AutogenUsed.txt"
  "hardwaredriver\\serial\\CMakeFiles\\hardwaredriverserial_autogen.dir\\ParseCache.txt"
  "hardwaredriver\\serial\\hardwaredriverserial_autogen"
  "hardwaredriver\\socket\\CMakeFiles\\hardwaredriversocket_autogen.dir\\AutogenUsed.txt"
  "hardwaredriver\\socket\\CMakeFiles\\hardwaredriversocket_autogen.dir\\ParseCache.txt"
  "hardwaredriver\\socket\\hardwaredriversocket_autogen"
  "tool\\communication\\CMakeFiles\\toolcommunication_autogen.dir\\AutogenUsed.txt"
  "tool\\communication\\CMakeFiles\\toolcommunication_autogen.dir\\ParseCache.txt"
  "tool\\communication\\toolcommunication_autogen"
  "tool\\handeyecalui\\handeyecalui\\CMakeFiles\\toolhandeyecaluihandeyecalui_autogen.dir\\AutogenUsed.txt"
  "tool\\handeyecalui\\handeyecalui\\CMakeFiles\\toolhandeyecaluihandeyecalui_autogen.dir\\ParseCache.txt"
  "tool\\handeyecalui\\handeyecalui\\toolhandeyecaluihandeyecalui_autogen"
  "tool\\handeyecaluipath\\CMakeFiles\\toolhandeyecaluipath_autogen.dir\\AutogenUsed.txt"
  "tool\\handeyecaluipath\\CMakeFiles\\toolhandeyecaluipath_autogen.dir\\ParseCache.txt"
  "tool\\handeyecaluipath\\toolhandeyecaluipath_autogen"
  "tool\\handeyecaluipathAuto\\CMakeFiles\\toolhandeyecaluipathAuto_autogen.dir\\AutogenUsed.txt"
  "tool\\handeyecaluipathAuto\\CMakeFiles\\toolhandeyecaluipathAuto_autogen.dir\\ParseCache.txt"
  "tool\\handeyecaluipathAuto\\toolhandeyecaluipathAuto_autogen"
  )
endif()
