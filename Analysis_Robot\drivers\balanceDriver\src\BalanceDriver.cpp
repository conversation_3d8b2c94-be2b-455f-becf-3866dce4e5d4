#include "BalanceDriver.h"
#include "glog.h"
#include <iomanip>
#include <sstream>
#include <cstring>
#include <errno.h>
#include <cstdio>

namespace AnalysisRobot {
namespace Balance {

BalanceDriver::BalanceDriver()
    : m_modbusCtx(nullptr)
    , m_status(BalanceStatus::DISCONNECTED)
    , m_statusCallback(nullptr) {
}

BalanceDriver::~BalanceDriver() {
    disconnect();
}

bool BalanceDriver::initialize(const BalanceConfig& config) {
    m_config = config;
    
    // 创建MODBUS RTU上下文
    m_modbusCtx = modbus_new_rtu(
        m_config.serialPort.c_str(),
        m_config.baudRate,
        m_config.parity,
        m_config.dataBits,
        m_config.stopBits
    );
    
    if (m_modbusCtx == nullptr) {
        setError("Failed to create MODBUS context: " + std::string(modbus_strerror(errno)));
        return false;
    }
    
    // 设置从机地址
    if (modbus_set_slave(m_modbusCtx, m_config.slaveId) == -1) {
        setError("Failed to set slave ID: " + std::string(modbus_strerror(errno)));
        modbus_free(m_modbusCtx);
        m_modbusCtx = nullptr;
        return false;
    }
    
    // 设置响应超时
    struct timeval timeout;
    timeout.tv_sec = m_config.responseTimeout / 1000;
    timeout.tv_usec = (m_config.responseTimeout % 1000) * 1000;
    modbus_set_response_timeout(m_modbusCtx, timeout.tv_sec, timeout.tv_usec);
    
    setStatus(BalanceStatus::DISCONNECTED, "Initialized successfully");
    return true;
}

bool BalanceDriver::connect() {
    if (m_modbusCtx == nullptr) {
        setError("MODBUS context not initialized");
        return false;
    }
    
    if (modbus_connect(m_modbusCtx) == -1) {
        setError("Connection failed: " + std::string(modbus_strerror(errno)));
        return false;
    }
    
    setStatus(BalanceStatus::CONNECTED, "Connected successfully");
    return true;
}

void BalanceDriver::disconnect() {
    if (m_modbusCtx != nullptr) {
        modbus_close(m_modbusCtx);
        modbus_free(m_modbusCtx);
        m_modbusCtx = nullptr;
    }
    setStatus(BalanceStatus::DISCONNECTED, "Disconnected");
}

bool BalanceDriver::isConnected() const {
    return m_status == BalanceStatus::CONNECTED;
}

WeightReading BalanceDriver::readWeight() {
    WeightReading result;
    
    if (!isConnected()) {
        result.errorMsg = "Not connected to balance";
        setError(result.errorMsg);
        return result;
    }
    
    setStatus(BalanceStatus::READING, "Reading weight...");
    
    // 根据文档：读取重量命令
    // 主机发送：01 03 00 00 00 02 C4 0B
    // 从机返回：01 03 04 XX XX XX XX CRC CRC
    // 寄存器起始地址：0x0000，寄存器数量：0x0002
    
    uint16_t registers[2];

    int rc = modbus_read_registers(m_modbusCtx, 0x0000, 2, registers);
    if (!checkModbusResult(rc, "read weight")) {
        result.errorMsg = m_lastError;
        setStatus(BalanceStatus::FAULT, result.errorMsg);
        return result;
    }
    
    // 拼接4字节内码值：X1 X2 X3 X4
    // 根据协议文档示例：从机返回 01 03 04 00 20 89 66 1C 43
    // 读取内容为拼接：X1 X2 X3 X4 = 00 20 89 66
    // registers[0] 包含前两个字节，registers[1] 包含后两个字节
    // MODBUS寄存器是大端序（高字节在前）
    result.rawValue = (static_cast<uint32_t>(registers[0]) << 16) | registers[1];

    // 调试输出
    printf("Debug: registers[0]=0x%04X, registers[1]=0x%04X, rawValue=0x%08X\n",
           registers[0], registers[1], result.rawValue);
    
    // 转换为重量值
    result.weight = convertRawToWeight(result.rawValue);
    result.success = true;
    
    setStatus(BalanceStatus::CONNECTED, "Weight read successfully");
    return result;
}

bool BalanceDriver::tare() {
    if (!isConnected()) {
        setError("Not connected to balance");
        return false;
    }
    
    // 根据文档：去皮命令
    // 主机发送：01 06 00 06 00 01 A8 00
    // 寄存器地址：0x0006，写入值：0x0001
    
    int rc = modbus_write_register(m_modbusCtx, 0x0006, 0x0001);
    
    if (!checkModbusResult(rc, "tare")) {
        setStatus(BalanceStatus::FAULT, m_lastError);
        return false;
    }
    
    setStatus(BalanceStatus::CONNECTED, "Tare completed successfully");
    return true;
}

BalanceStatus BalanceDriver::getStatus() const {
    return m_status;
}

void BalanceDriver::setStatusCallback(StatusCallback callback) {
    m_statusCallback = callback;
}

std::string BalanceDriver::getLastError() const {
    return m_lastError;
}

std::string BalanceDriver::getDeviceInfo() const {
    std::ostringstream oss;
    oss << "FA2204N Balance Driver\n";
    oss << "Serial Port: " << m_config.serialPort << "\n";
    oss << "Baud Rate: " << m_config.baudRate << "\n";
    oss << "Slave ID: " << m_config.slaveId << "\n";
    oss << "Status: ";
    
    switch (m_status) {
        case BalanceStatus::DISCONNECTED: oss << "Disconnected"; break;
        case BalanceStatus::CONNECTED: oss << "Connected"; break;
        case BalanceStatus::READING: oss << "Reading"; break;
        case BalanceStatus::FAULT: oss << "Error"; break;
    }
    
    return oss.str();
}

void BalanceDriver::setStatus(BalanceStatus status, const std::string& message) {
    m_status = status;
    if (m_statusCallback) {
        m_statusCallback(status, message);
    }
}

void BalanceDriver::setError(const std::string& error) {
    m_lastError = error;
    LOG(ERROR) << "BalanceDriver Error: " << error;
}

double BalanceDriver::convertRawToWeight(uint32_t rawValue) {
    // 根据文档示例：0x00208966(十六进制) → 2,132,326(十进制) → 213.2326g
    // 转换公式：内码值 / 10000.0
    return static_cast<double>(rawValue) / 10000.0;
}

bool BalanceDriver::checkModbusResult(int result, const std::string& operation) {
    if (result == -1) {
        setError("MODBUS " + operation + " failed: " + std::string(modbus_strerror(errno)));
        return false;
    }
    return true;
}

} // namespace Balance
} // namespace AnalysisRobot
