#include "BalanceDriver.h"
#include "glog.h"
#include <iomanip>
#include <sstream>
#include <cstring>
#include <errno.h>
#include <thread>
#include <chrono>

#ifdef _WIN32
#include <windows.h>
#else
#include <sys/select.h>
#include <sys/time.h>
#endif

namespace AnalysisRobot {
namespace Balance {

BalanceDriver::BalanceDriver()
#ifdef _WIN32
    : m_serialHandle(INVALID_HANDLE_VALUE)
#else
    : m_serialFd(-1)
#endif
    , m_status(BalanceStatus::DISCONNECTED) {
}

BalanceDriver::~BalanceDriver() {
    disconnect();
}

bool BalanceDriver::connect(const BalanceConfig& config) {
    if (m_status == BalanceStatus::CONNECTED) {
        return true;
    }

    m_config = config;

    // 打开串口
    if (!openSerialPort()) {
        return false;
    }

    // 配置串口参数
    if (!configureSerialPort()) {
        closeSerialPort();
        return false;
    }

    setStatus(BalanceStatus::CONNECTED, "Connected successfully");
    return true;
}

void BalanceDriver::disconnect() {
    if (m_status != BalanceStatus::DISCONNECTED) {
        closeSerialPort();
        setStatus(BalanceStatus::DISCONNECTED, "Disconnected");
    }
}

WeightReading BalanceDriver::readWeight() {
    WeightReading result;

    if (m_status != BalanceStatus::CONNECTED) {
        result.errorMsg = "Device not connected";
        return result;
    }

    setStatus(BalanceStatus::READING, "Reading weight...");

    // 构建读取重量的MODBUS请求
    auto request = buildReadWeightRequest();

    // 发送请求
    if (!sendModbusRequest(request)) {
        result.errorMsg = m_lastError;
        setStatus(BalanceStatus::CONNECTED, "Read failed");
        return result;
    }

    // 接收响应
    std::vector<uint8_t> response;
    int receivedBytes = receiveModbusResponse(response, 9); // 期望9字节响应

    if (receivedBytes != 9) {
        result.errorMsg = "Invalid response length: " + std::to_string(receivedBytes);
        setStatus(BalanceStatus::CONNECTED, "Read failed");
        return result;
    }

    // 解析响应
    if (!parseWeightResponse(response, result)) {
        setStatus(BalanceStatus::CONNECTED, "Read failed");
        return result;
    }

    setStatus(BalanceStatus::CONNECTED, "Read successful");
    return result;
}

bool BalanceDriver::tare() {
    if (m_status != BalanceStatus::CONNECTED) {
        setError("Device not connected");
        return false;
    }

    // 构建去皮的MODBUS请求
    auto request = buildTareRequest();

    // 发送请求
    if (!sendModbusRequest(request)) {
        return false;
    }

    // 接收响应
    std::vector<uint8_t> response;
    int receivedBytes = receiveModbusResponse(response, 8); // 期望8字节响应

    if (receivedBytes != 8) {
        setError("Invalid tare response length: " + std::to_string(receivedBytes));
        return false;
    }

    // 验证CRC
    if (!verifyCRC16(response.data(), response.size())) {
        setError("Tare response CRC verification failed");
        return false;
    }

    return true;
}

void BalanceDriver::setStatusCallback(StatusCallback callback) {
    m_statusCallback = callback;
}

BalanceStatus BalanceDriver::getStatus() const {
    return m_status;
}

std::string BalanceDriver::getLastError() const {
    return m_lastError;
}

void BalanceDriver::setStatus(BalanceStatus status, const std::string& message) {
    m_status = status;
    if (m_statusCallback) {
        m_statusCallback(status, message);
    }
}

void BalanceDriver::setError(const std::string& error) {
    m_lastError = error;
    LOG(ERROR) << "BalanceDriver Error: " << error;
}

double BalanceDriver::convertRawToWeight(uint32_t rawValue) {
    // 根据协议文档：内码值 / 10000.0
    // 示例：0x00208966 → 2,132,326 → 213.2326g
    return static_cast<double>(rawValue) / 10000.0;
}

#ifdef _WIN32
bool BalanceDriver::openSerialPort() {
    std::string portName = "\\\\.\\" + m_config.serialPort;

    m_serialHandle = CreateFileA(
        portName.c_str(),
        GENERIC_READ | GENERIC_WRITE,
        0,
        NULL,
        OPEN_EXISTING,
        0,
        NULL
    );

    if (m_serialHandle == INVALID_HANDLE_VALUE) {
        setError("Failed to open serial port: " + m_config.serialPort);
        return false;
    }

    return true;
}

void BalanceDriver::closeSerialPort() {
    if (m_serialHandle != INVALID_HANDLE_VALUE) {
        CloseHandle(m_serialHandle);
        m_serialHandle = INVALID_HANDLE_VALUE;
    }
}

bool BalanceDriver::configureSerialPort() {
    DCB dcb = {0};
    dcb.DCBlength = sizeof(DCB);

    if (!GetCommState(m_serialHandle, &dcb)) {
        setError("Failed to get comm state");
        return false;
    }

    dcb.BaudRate = m_config.baudRate;
    dcb.ByteSize = m_config.dataBits;
    dcb.StopBits = (m_config.stopBits == 1) ? ONESTOPBIT : TWOSTOPBITS;

    switch (m_config.parity) {
        case 'N': case 'n': dcb.Parity = NOPARITY; break;
        case 'E': case 'e': dcb.Parity = EVENPARITY; break;
        case 'O': case 'o': dcb.Parity = ODDPARITY; break;
        default: dcb.Parity = NOPARITY; break;
    }

    dcb.fParity = (dcb.Parity != NOPARITY);

    if (!SetCommState(m_serialHandle, &dcb)) {
        setError("Failed to set comm state");
        return false;
    }

    // 设置超时
    COMMTIMEOUTS timeouts = {0};
    timeouts.ReadIntervalTimeout = 50;
    timeouts.ReadTotalTimeoutConstant = m_config.responseTimeout;
    timeouts.ReadTotalTimeoutMultiplier = 10;
    timeouts.WriteTotalTimeoutConstant = 1000;
    timeouts.WriteTotalTimeoutMultiplier = 10;

    if (!SetCommTimeouts(m_serialHandle, &timeouts)) {
        setError("Failed to set comm timeouts");
        return false;
    }

    return true;
}

bool BalanceDriver::sendModbusRequest(const std::vector<uint8_t>& request) {
    DWORD bytesWritten;
    if (!WriteFile(m_serialHandle, request.data(), request.size(), &bytesWritten, NULL)) {
        setError("Failed to write to serial port");
        return false;
    }

    if (bytesWritten != request.size()) {
        setError("Incomplete write to serial port");
        return false;
    }

    return true;
}

int BalanceDriver::receiveModbusResponse(std::vector<uint8_t>& response, int expectedLength) {
    response.resize(expectedLength);
    DWORD bytesRead = 0;
    DWORD totalBytesRead = 0;

    auto startTime = std::chrono::steady_clock::now();
    auto timeout = std::chrono::milliseconds(m_config.responseTimeout);

    while (totalBytesRead < expectedLength) {
        if (!ReadFile(m_serialHandle, response.data() + totalBytesRead,
                     expectedLength - totalBytesRead, &bytesRead, NULL)) {
            setError("Failed to read from serial port");
            return -1;
        }

        totalBytesRead += bytesRead;

        if (std::chrono::steady_clock::now() - startTime > timeout) {
            setError("Read timeout");
            return totalBytesRead;
        }

        if (bytesRead == 0) {
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
        }
    }

    response.resize(totalBytesRead);
    return totalBytesRead;
}

#else
// Linux implementation
bool BalanceDriver::openSerialPort() {
    m_serialFd = open(m_config.serialPort.c_str(), O_RDWR | O_NOCTTY | O_SYNC);

    if (m_serialFd < 0) {
        setError("Failed to open serial port: " + m_config.serialPort);
        return false;
    }

    return true;
}

void BalanceDriver::closeSerialPort() {
    if (m_serialFd >= 0) {
        close(m_serialFd);
        m_serialFd = -1;
    }
}

bool BalanceDriver::configureSerialPort() {
    struct termios tty;

    if (tcgetattr(m_serialFd, &tty) != 0) {
        setError("Failed to get terminal attributes");
        return false;
    }

    // 设置波特率
    speed_t speed;
    switch (m_config.baudRate) {
        case 9600: speed = B9600; break;
        case 19200: speed = B19200; break;
        case 38400: speed = B38400; break;
        case 57600: speed = B57600; break;
        case 115200: speed = B115200; break;
        default: speed = B9600; break;
    }

    cfsetospeed(&tty, speed);
    cfsetispeed(&tty, speed);

    // 设置数据位
    tty.c_cflag &= ~CSIZE;
    switch (m_config.dataBits) {
        case 5: tty.c_cflag |= CS5; break;
        case 6: tty.c_cflag |= CS6; break;
        case 7: tty.c_cflag |= CS7; break;
        case 8: tty.c_cflag |= CS8; break;
        default: tty.c_cflag |= CS8; break;
    }

    // 设置停止位
    if (m_config.stopBits == 2) {
        tty.c_cflag |= CSTOPB;
    } else {
        tty.c_cflag &= ~CSTOPB;
    }

    // 设置校验位
    switch (m_config.parity) {
        case 'N': case 'n':
            tty.c_cflag &= ~PARENB;
            break;
        case 'E': case 'e':
            tty.c_cflag |= PARENB;
            tty.c_cflag &= ~PARODD;
            break;
        case 'O': case 'o':
            tty.c_cflag |= PARENB;
            tty.c_cflag |= PARODD;
            break;
        default:
            tty.c_cflag &= ~PARENB;
            break;
    }

    // 其他设置
    tty.c_cflag |= CREAD | CLOCAL;
    tty.c_lflag &= ~(ICANON | ECHO | ECHOE | ISIG);
    tty.c_iflag &= ~(IXON | IXOFF | IXANY);
    tty.c_oflag &= ~OPOST;

    // 设置超时
    tty.c_cc[VMIN] = 0;
    tty.c_cc[VTIME] = m_config.responseTimeout / 100; // 以0.1秒为单位

    if (tcsetattr(m_serialFd, TCSANOW, &tty) != 0) {
        setError("Failed to set terminal attributes");
        return false;
    }

    return true;
}

bool BalanceDriver::sendModbusRequest(const std::vector<uint8_t>& request) {
    ssize_t bytesWritten = write(m_serialFd, request.data(), request.size());

    if (bytesWritten < 0) {
        setError("Failed to write to serial port");
        return false;
    }

    if (static_cast<size_t>(bytesWritten) != request.size()) {
        setError("Incomplete write to serial port");
        return false;
    }

    return true;
}

int BalanceDriver::receiveModbusResponse(std::vector<uint8_t>& response, int expectedLength) {
    response.resize(expectedLength);
    int totalBytesRead = 0;

    auto startTime = std::chrono::steady_clock::now();
    auto timeout = std::chrono::milliseconds(m_config.responseTimeout);

    while (totalBytesRead < expectedLength) {
        fd_set readfds;
        FD_ZERO(&readfds);
        FD_SET(m_serialFd, &readfds);

        struct timeval tv;
        tv.tv_sec = 0;
        tv.tv_usec = 10000; // 10ms

        int selectResult = select(m_serialFd + 1, &readfds, NULL, NULL, &tv);

        if (selectResult > 0 && FD_ISSET(m_serialFd, &readfds)) {
            ssize_t bytesRead = read(m_serialFd, response.data() + totalBytesRead,
                                   expectedLength - totalBytesRead);

            if (bytesRead < 0) {
                setError("Failed to read from serial port");
                return -1;
            }

            totalBytesRead += bytesRead;
        }

        if (std::chrono::steady_clock::now() - startTime > timeout) {
            setError("Read timeout");
            return totalBytesRead;
        }
    }

    response.resize(totalBytesRead);
    return totalBytesRead;
}

#endif