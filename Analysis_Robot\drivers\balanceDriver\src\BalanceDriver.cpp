#include "glog.h"
#include "BalanceDriver.h"
#include <iomanip>
#include <sstream>
#include <cstring>
#include <errno.h>
#include <thread>
#include <chrono>

#ifdef _WIN32
#include <windows.h>
#else
#include <sys/select.h>
#include <sys/time.h>
#endif

namespace AnalysisRobot {
namespace Balance {

BalanceDriver::BalanceDriver() 
#ifdef _WIN32
    : m_serialHandle(INVALID_HANDLE_VALUE)
#else
    : m_serialFd(-1)
#endif
    , m_status(BalanceStatus::DISCONNECTED) {
}

BalanceDriver::~BalanceDriver() {
    disconnect();
}

bool BalanceDriver::connect(const BalanceConfig& config) {
    if (m_status == BalanceStatus::CONNECTED) {
        return true;
    }
    
    m_config = config;
    
    // 打开串口
    if (!openSerialPort()) {
        return false;
    }
    
    // 配置串口参数
    if (!configureSerialPort()) {
        closeSerialPort();
        return false;
    }
    
    setStatus(BalanceStatus::CONNECTED, "Connected successfully");
    return true;
}

void BalanceDriver::disconnect() {
    if (m_status != BalanceStatus::DISCONNECTED) {
        closeSerialPort();
        setStatus(BalanceStatus::DISCONNECTED, "Disconnected");
    }
}

WeightReading BalanceDriver::readWeight() {
    WeightReading result;
    
    if (m_status != BalanceStatus::CONNECTED) {
        result.errorMsg = "Device not connected";
        return result;
    }
    
    setStatus(BalanceStatus::READING, "Reading weight...");
    
    // 构建读取重量的MODBUS请求
    auto request = buildReadWeightRequest();
    
    // 发送请求
    if (!sendModbusRequest(request)) {
        result.errorMsg = m_lastError;
        setStatus(BalanceStatus::CONNECTED, "Read failed");
        return result;
    }
    
    // 接收响应
    std::vector<uint8_t> response;
    int receivedBytes = receiveModbusResponse(response, 9); // 期望9字节响应
    
    if (receivedBytes != 9) {
        result.errorMsg = "Invalid response length: " + std::to_string(receivedBytes);
        setStatus(BalanceStatus::CONNECTED, "Read failed");
        return result;
    }
    
    // 解析响应
    if (!parseWeightResponse(response, result)) {
        setStatus(BalanceStatus::CONNECTED, "Read failed");
        return result;
    }
    
    setStatus(BalanceStatus::CONNECTED, "Read successful");
    return result;
}

bool BalanceDriver::tare() {
    if (m_status != BalanceStatus::CONNECTED) {
        setError("Device not connected");
        return false;
    }
    
    // 构建去皮的MODBUS请求
    auto request = buildTareRequest();
    
    // 发送请求
    if (!sendModbusRequest(request)) {
        return false;
    }
    
    // 接收响应
    std::vector<uint8_t> response;
    int receivedBytes = receiveModbusResponse(response, 8); // 期望8字节响应
    
    if (receivedBytes != 8) {
        setError("Invalid tare response length: " + std::to_string(receivedBytes));
        return false;
    }
    
    // 验证CRC
    if (!verifyCRC16(response.data(), response.size())) {
        setError("Tare response CRC verification failed");
        return false;
    }
    
    return true;
}

void BalanceDriver::setStatusCallback(StatusCallback callback) {
    m_statusCallback = callback;
}

BalanceStatus BalanceDriver::getStatus() const {
    return m_status;
}

std::string BalanceDriver::getLastError() const {
    return m_lastError;
}

void BalanceDriver::setStatus(BalanceStatus status, const std::string& message) {
    m_status = status;
    if (m_statusCallback) {
        m_statusCallback(status, message);
    }
}

void BalanceDriver::setError(const std::string& error) {
    m_lastError = error;
    LOG(ERROR) << "BalanceDriver Error: " << error;
}

double BalanceDriver::convertRawToWeight(uint32_t rawValue) {
    // 根据协议文档：内码值 / 10000.0
    // 示例：0x00208966 → 2,132,326 → 213.2326g
    return static_cast<double>(rawValue) / 10000.0;
}

#ifdef _WIN32
bool BalanceDriver::openSerialPort() {
    std::string portName = "\\\\.\\" + m_config.serialPort;

    m_serialHandle = CreateFileA(
        portName.c_str(),
        GENERIC_READ | GENERIC_WRITE,
        0,                    // 不共享
        NULL,                 // 默认安全属性
        OPEN_EXISTING,        // 打开现有设备
        FILE_ATTRIBUTE_NORMAL, // 普通文件属性
        NULL                  // 无模板文件
    );

    if (m_serialHandle == INVALID_HANDLE_VALUE) {
        DWORD error = GetLastError();
        std::string errorMsg = "Failed to open serial port: " + m_config.serialPort +
                              ", error code: " + std::to_string(error);

        // 添加常见错误的说明
        switch (error) {
            case ERROR_FILE_NOT_FOUND:
                errorMsg += " (Port not found)";
                break;
            case ERROR_ACCESS_DENIED:
                errorMsg += " (Access denied - port may be in use)";
                break;
            case ERROR_INVALID_NAME:
                errorMsg += " (Invalid port name)";
                break;
        }

        setError(errorMsg);
        return false;
    }

    // 清除串口缓冲区
    PurgeComm(m_serialHandle, PURGE_RXCLEAR | PURGE_TXCLEAR | PURGE_RXABORT | PURGE_TXABORT);

    return true;
}

void BalanceDriver::closeSerialPort() {
    if (m_serialHandle != INVALID_HANDLE_VALUE) {
        CloseHandle(m_serialHandle);
        m_serialHandle = INVALID_HANDLE_VALUE;
    }
}

bool BalanceDriver::configureSerialPort() {
    DCB dcb;
    memset(&dcb, 0, sizeof(DCB));
    dcb.DCBlength = sizeof(DCB);

    if (!GetCommState(m_serialHandle, &dcb)) {
        DWORD error = GetLastError();
        setError("Failed to get comm state, error code: " + std::to_string(error));
        return false;
    }

    // 调试输出当前DCB设置
    LOG(INFO) << "Current DCB settings:";
    LOG(INFO) << "  BaudRate: " << dcb.BaudRate;
    LOG(INFO) << "  ByteSize: " << (int)dcb.ByteSize;
    LOG(INFO) << "  Parity: " << (int)dcb.Parity;
    LOG(INFO) << "  StopBits: " << (int)dcb.StopBits;

    // 重新初始化DCB结构
    memset(&dcb, 0, sizeof(DCB));
    dcb.DCBlength = sizeof(DCB);

    // 设置基本参数
    dcb.BaudRate = m_config.baudRate;
    dcb.ByteSize = m_config.dataBits;
    dcb.StopBits = (m_config.stopBits == 1) ? ONESTOPBIT : TWOSTOPBITS;

    // 设置校验位
    switch (m_config.parity) {
        case 'N': case 'n':
            dcb.Parity = NOPARITY;
            dcb.fParity = FALSE;
            break;
        case 'E': case 'e':
            dcb.Parity = EVENPARITY;
            dcb.fParity = TRUE;
            break;
        case 'O': case 'o':
            dcb.Parity = ODDPARITY;
            dcb.fParity = TRUE;
            break;
        default:
            dcb.Parity = NOPARITY;
            dcb.fParity = FALSE;
            break;
    }

    // 设置流控制
    dcb.fOutxCtsFlow = FALSE;       // 禁用CTS流控制
    dcb.fOutxDsrFlow = FALSE;       // 禁用DSR流控制
    dcb.fDtrControl = DTR_CONTROL_ENABLE;  // 启用DTR
    dcb.fRtsControl = RTS_CONTROL_ENABLE;  // 启用RTS
    dcb.fOutX = FALSE;              // 禁用XON/XOFF输出流控制
    dcb.fInX = FALSE;               // 禁用XON/XOFF输入流控制
    dcb.fErrorChar = FALSE;         // 禁用错误字符替换
    dcb.fNull = FALSE;              // 禁用NULL字节丢弃
    dcb.fAbortOnError = FALSE;      // 错误时不中止读写操作
    dcb.fBinary = TRUE;             // 二进制模式

    // 调试输出新的DCB设置
    LOG(INFO) << "New DCB settings:";
    LOG(INFO) << "  BaudRate: " << dcb.BaudRate;
    LOG(INFO) << "  ByteSize: " << (int)dcb.ByteSize;
    LOG(INFO) << "  Parity: " << (int)dcb.Parity;
    LOG(INFO) << "  StopBits: " << (int)dcb.StopBits;
    LOG(INFO) << "  fBinary: " << dcb.fBinary;
    LOG(INFO) << "  fParity: " << dcb.fParity;

    if (!SetCommState(m_serialHandle, &dcb)) {
        DWORD error = GetLastError();
        setError("Failed to set comm state, error code: " + std::to_string(error));
        return false;
    }
    
    // 设置超时
    COMMTIMEOUTS timeouts = {0};
    timeouts.ReadIntervalTimeout = 50;
    timeouts.ReadTotalTimeoutConstant = m_config.responseTimeout;
    timeouts.ReadTotalTimeoutMultiplier = 10;
    timeouts.WriteTotalTimeoutConstant = 1000;
    timeouts.WriteTotalTimeoutMultiplier = 10;
    
    if (!SetCommTimeouts(m_serialHandle, &timeouts)) {
        setError("Failed to set comm timeouts");
        return false;
    }
    
    return true;
}

bool BalanceDriver::sendModbusRequest(const std::vector<uint8_t>& request) {
    DWORD bytesWritten;
    if (!WriteFile(m_serialHandle, request.data(), request.size(), &bytesWritten, NULL)) {
        setError("Failed to write to serial port");
        return false;
    }
    
    if (bytesWritten != request.size()) {
        setError("Incomplete write to serial port");
        return false;
    }
    
    return true;
}

int BalanceDriver::receiveModbusResponse(std::vector<uint8_t>& response, int expectedLength) {
    response.resize(expectedLength);
    DWORD bytesRead = 0;
    DWORD totalBytesRead = 0;
    
    auto startTime = std::chrono::steady_clock::now();
    auto timeout = std::chrono::milliseconds(m_config.responseTimeout);
    
    while (totalBytesRead < expectedLength) {
        if (!ReadFile(m_serialHandle, response.data() + totalBytesRead, 
                     expectedLength - totalBytesRead, &bytesRead, NULL)) {
            setError("Failed to read from serial port");
            return -1;
        }
        
        totalBytesRead += bytesRead;
        
        if (std::chrono::steady_clock::now() - startTime > timeout) {
            setError("Read timeout");
            return totalBytesRead;
        }
        
        if (bytesRead == 0) {
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
        }
    }
    
    response.resize(totalBytesRead);
    return totalBytesRead;
}

#else
// 非Windows平台暂不支持
bool BalanceDriver::openSerialPort() {
    setError("Non-Windows platforms not supported");
    return false;
}

void BalanceDriver::closeSerialPort() {
}

bool BalanceDriver::configureSerialPort() {
    return false;
}

bool BalanceDriver::sendModbusRequest(const std::vector<uint8_t>& request) {
    return false;
}

int BalanceDriver::receiveModbusResponse(std::vector<uint8_t>& response, int expectedLength) {
    return -1;
}
#endif

// CRC16计算表
static const uint16_t crc16_table[256] = {
    0x0000, 0xC0C1, 0xC181, 0x0140, 0xC301, 0x03C0, 0x0280, 0xC241,
    0xC601, 0x06C0, 0x0780, 0xC741, 0x0500, 0xC5C1, 0xC481, 0x0440,
    0xCC01, 0x0CC0, 0x0D80, 0xCD41, 0x0F00, 0xCFC1, 0xCE81, 0x0E40,
    0x0A00, 0xCAC1, 0xCB81, 0x0B40, 0xC901, 0x09C0, 0x0880, 0xC841,
    0xD801, 0x18C0, 0x1980, 0xD941, 0x1B00, 0xDBC1, 0xDA81, 0x1A40,
    0x1E00, 0xDEC1, 0xDF81, 0x1F40, 0xDD01, 0x1DC0, 0x1C80, 0xDC41,
    0x1400, 0xD4C1, 0xD581, 0x1540, 0xD701, 0x17C0, 0x1680, 0xD641,
    0xD201, 0x12C0, 0x1380, 0xD341, 0x1100, 0xD1C1, 0xD081, 0x1040,
    0xF001, 0x30C0, 0x3180, 0xF141, 0x3300, 0xF3C1, 0xF281, 0x3240,
    0x3600, 0xF6C1, 0xF781, 0x3740, 0xF501, 0x35C0, 0x3480, 0xF441,
    0x3C00, 0xFCC1, 0xFD81, 0x3D40, 0xFF01, 0x3FC0, 0x3E80, 0xFE41,
    0xFA01, 0x3AC0, 0x3B80, 0xFB41, 0x3900, 0xF9C1, 0xF881, 0x3840,
    0x2800, 0xE8C1, 0xE981, 0x2940, 0xEB01, 0x2BC0, 0x2A80, 0xEA41,
    0xEE01, 0x2EC0, 0x2F80, 0xEF41, 0x2D00, 0xEDC1, 0xEC81, 0x2C40,
    0xE401, 0x24C0, 0x2580, 0xE541, 0x2700, 0xE7C1, 0xE681, 0x2640,
    0x2200, 0xE2C1, 0xE381, 0x2340, 0xE101, 0x21C0, 0x2080, 0xE041,
    0xA001, 0x60C0, 0x6180, 0xA141, 0x6300, 0xA3C1, 0xA281, 0x6240,
    0x6600, 0xA6C1, 0xA781, 0x6740, 0xA501, 0x65C0, 0x6480, 0xA441,
    0x6C00, 0xACC1, 0xAD81, 0x6D40, 0xAF01, 0x6FC0, 0x6E80, 0xAE41,
    0xAA01, 0x6AC0, 0x6B80, 0xAB41, 0x6900, 0xA9C1, 0xA881, 0x6840,
    0x7800, 0xB8C1, 0xB981, 0x7940, 0xBB01, 0x7BC0, 0x7A80, 0xBA41,
    0xBE01, 0x7EC0, 0x7F80, 0xBF41, 0x7D00, 0xBDC1, 0xBC81, 0x7C40,
    0xB401, 0x74C0, 0x7580, 0xB541, 0x7700, 0xB7C1, 0xB681, 0x7640,
    0x7200, 0xB2C1, 0xB381, 0x7340, 0xB101, 0x71C0, 0x7080, 0xB041,
    0x5000, 0x90C1, 0x9181, 0x5140, 0x9301, 0x53C0, 0x5280, 0x9241,
    0x9601, 0x56C0, 0x5780, 0x9741, 0x5500, 0x95C1, 0x9481, 0x5440,
    0x9C01, 0x5CC0, 0x5D80, 0x9D41, 0x5F00, 0x9FC1, 0x9E81, 0x5E40,
    0x5A00, 0x9AC1, 0x9B81, 0x5B40, 0x9901, 0x59C0, 0x5880, 0x9841,
    0x8801, 0x48C0, 0x4980, 0x8941, 0x4B00, 0x8BC1, 0x8A81, 0x4A40,
    0x4E00, 0x8EC1, 0x8F81, 0x4F40, 0x8D01, 0x4DC0, 0x4C80, 0x8C41,
    0x4400, 0x84C1, 0x8581, 0x4540, 0x8701, 0x47C0, 0x4680, 0x8641,
    0x8201, 0x42C0, 0x4380, 0x8341, 0x4100, 0x81C1, 0x8081, 0x4040
};

uint16_t BalanceDriver::calculateCRC16(const uint8_t* data, int length) {
    uint16_t crc = 0xFFFF;

    for (int i = 0; i < length; i++) {
        uint8_t index = (crc ^ data[i]) & 0xFF;
        crc = (crc >> 8) ^ crc16_table[index];
    }

    return crc;
}

bool BalanceDriver::verifyCRC16(const uint8_t* data, int length) {
    if (length < 3) {
        return false;
    }

    // 计算除了最后两个字节（CRC）之外的数据的CRC
    uint16_t calculatedCRC = calculateCRC16(data, length - 2);

    // 提取接收到的CRC（低字节在前，高字节在后）
    uint16_t receivedCRC = data[length - 2] | (data[length - 1] << 8);

    return calculatedCRC == receivedCRC;
}

std::vector<uint8_t> BalanceDriver::buildReadWeightRequest() {
    // 根据协议文档：01 03 00 00 00 02 C4 0B
    // 从机地址：01
    // 功能码：03（读保持寄存器）
    // 起始地址：0000（高字节在前）
    // 寄存器数量：0002（读取2个寄存器）
    // CRC16：C4 0B（低字节在前）

    std::vector<uint8_t> request = {
        static_cast<uint8_t>(m_config.slaveId),  // 从机地址
        0x03,                                     // 功能码
        0x00, 0x00,                              // 起始地址
        0x00, 0x02                               // 寄存器数量
    };

    // 计算CRC16
    uint16_t crc = calculateCRC16(request.data(), request.size());

    // 添加CRC（低字节在前）
    request.push_back(crc & 0xFF);
    request.push_back((crc >> 8) & 0xFF);

    return request;
}

std::vector<uint8_t> BalanceDriver::buildTareRequest() {
    // 根据协议文档：01 06 00 06 00 01 A8 0B
    // 从机地址：01
    // 功能码：06（写单个寄存器）
    // 寄存器地址：0006（去皮寄存器）
    // 寄存器值：0001（去皮命令）
    // CRC16：A8 0B

    std::vector<uint8_t> request = {
        static_cast<uint8_t>(m_config.slaveId),  // 从机地址
        0x06,                                     // 功能码
        0x00, 0x06,                              // 寄存器地址（0x0006）
        0x00, 0x01                               // 寄存器值
    };

    // 计算CRC16
    uint16_t crc = calculateCRC16(request.data(), request.size());

    // 添加CRC（低字节在前）
    request.push_back(crc & 0xFF);
    request.push_back((crc >> 8) & 0xFF);

    return request;
}

bool BalanceDriver::parseWeightResponse(const std::vector<uint8_t>& response, WeightReading& result) {
    // 验证响应长度
    if (response.size() != 9) {
        result.errorMsg = "Invalid response length: " + std::to_string(response.size());
        return false;
    }

    // 验证从机地址
    if (response[0] != m_config.slaveId) {
        result.errorMsg = "Invalid slave address in response";
        return false;
    }

    // 验证功能码
    if (response[1] != 0x03) {
        result.errorMsg = "Invalid function code in response";
        return false;
    }

    // 验证数据长度
    if (response[2] != 0x04) {
        result.errorMsg = "Invalid data length in response";
        return false;
    }

    // 验证CRC
    if (!verifyCRC16(response.data(), response.size())) {
        result.errorMsg = "CRC verification failed";
        return false;
    }

    // 解析重量数据
    // 根据协议文档示例：01 03 04 00 20 89 66 1C 43
    // 数据部分：00 20 89 66
    // 拼接为：0x00208966
    uint32_t rawValue = (static_cast<uint32_t>(response[3]) << 24) |
                        (static_cast<uint32_t>(response[4]) << 16) |
                        (static_cast<uint32_t>(response[5]) << 8) |
                        static_cast<uint32_t>(response[6]);

    result.rawValue = rawValue;
    result.weight = convertRawToWeight(rawValue);
    result.success = true;

    return true;
}

} // namespace Balance
} // namespace AnalysisRobot
