<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="BackendCodeEditorMiscSettings">
    <option name="/Default/Housekeeping/FeatureSuggestion/FeatureSuggestionManager/DisabledSuggesters/=SwitchToGoToActionSuggester/@EntryIndexedValue" value="true" type="bool" />
    <option name="/Default/Housekeeping/GlobalSettingsUpgraded/IsUpgraded/@EntryValue" value="true" type="bool" />
    <option name="/Default/RiderDebugger/RiderRestoreDecompile/RestoreDecompileSetting/@EntryValue" value="false" type="bool" />
  </component>
  <component name="CMakePresetLoader">{
  &quot;useNewFormat&quot;: true
}</component>
  <component name="CMakeProjectFlavorService">
    <option name="flavorId" value="CMakePlainProjectFlavor" />
  </component>
  <component name="CMakeReloadState">
    <option name="reloaded" value="true" />
  </component>
  <component name="CMakeRunConfigurationManager">
    <generated>
      <config projectName="Project" targetName="Testtest_fileutil" />
      <config projectName="Project" targetName="Testtest_license_manager" />
      <config projectName="Project" targetName="fuxicommon" />
      <config projectName="Project" targetName="MJServer_RefactorApp" />
      <config projectName="Project" targetName="MJServer_RefactorTestphase1_test" />
      <config projectName="Project" targetName="hardwaredriverOpcDa" />
      <config projectName="Project" targetName="Analysis_RobotdriversaixsDriver" />
      <config projectName="Project" targetName="hardwaredriversocket" />
      <config projectName="Project" targetName="hardwaredriverLabelPrinter" />
      <config projectName="Project" targetName="toolverify_calibration" />
      <config projectName="Project" targetName="Analysis_RobotalgorithmspouringControl" />
      <config projectName="Project" targetName="Testtest_abb_socket" />
      <config projectName="Project" targetName="MJServer_RefactorTestsimple_abb_client" />
      <config projectName="Project" targetName="Analysis_RobottestbalanceDriverTest" />
      <config projectName="Project" targetName="Testtest_event_listener" />
      <config projectName="Project" targetName="hardwaredrivermodbus" />
      <config projectName="Project" targetName="Analysis_RobotdriversplcDriver" />
      <config projectName="Project" targetName="RoboticLaserMarkingTestabbsocket" />
      <config projectName="Project" targetName="toolhandeyecal" />
      <config projectName="Project" targetName="hardwaredriverAuboDriver" />
      <config projectName="Project" targetName="Analysis_RobotdriversbalanceDriver" />
      <config projectName="Project" targetName="RoboticLaserMarkingTestlaserUI" />
      <config projectName="Project" targetName="toolcameraCalibrator" />
      <config projectName="Project" targetName="Testtest_json" />
      <config projectName="Project" targetName="Analysis_RobotdriversheatingMagneticStirrerDriver" />
      <config projectName="Project" targetName="Testtest_sqlite" />
      <config projectName="Project" targetName="RoboticLaserMarkingRFIDDriver" />
      <config projectName="Project" targetName="toolhandeyecaluipath" />
      <config projectName="Project" targetName="Testtest_csv" />
      <config projectName="Project" targetName="RoboticLaserMarkingUI" />
      <config projectName="Project" targetName="hardwaredriverserial" />
      <config projectName="Project" targetName="Testtest_config_manager" />
      <config projectName="Project" targetName="Testtest_network" />
      <config projectName="Project" targetName="RoboticLaserMarkinglaserDriver" />
      <config projectName="Project" targetName="hardwaredriverMettlerBalance" />
      <config projectName="Project" targetName="toolhandeyecaluipathAuto" />
      <config projectName="Project" targetName="Analysis_RobotdriversmoistureAnalyzerDriver" />
      <config projectName="Project" targetName="hardwaredriverabbRobotDriver" />
      <config projectName="Project" targetName="RoboticLaserMarkingAbbDriver" />
      <config projectName="Project" targetName="Testtest_xml" />
      <config projectName="Project" targetName="toolcaltest" />
      <config projectName="Project" targetName="Analysis_RobottestheatingMagneticStirrerDriver" />
      <config projectName="Project" targetName="Analysis_RobotApp" />
      <config projectName="Project" targetName="Testtest_executor" />
      <config projectName="Project" targetName="fuxicore" />
      <config projectName="Project" targetName="test_micro_dosing" />
      <config projectName="Project" targetName="toolhandeyecaltest" />
      <config projectName="Project" targetName="Testtest_license_ui" />
      <config projectName="Project" targetName="Analysis_RobotdriversrestInterfaceDriver" />
      <config projectName="Project" targetName="Testtest_twoaixsrobot" />
      <config projectName="Project" targetName="Testtest_taskflow" />
      <config projectName="Project" targetName="Analysis_RobotalgorithmstcpPositionMaintain" />
      <config projectName="Project" targetName="RoboticLaserMarkingTestlaser" />
      <config projectName="Project" targetName="RoboticLaserMarkinglaserDriverSim" />
      <config projectName="Project" targetName="Testtest_fa2204n_balance" />
      <config projectName="Project" targetName="hardwaredriverAuboArcsDriver" />
      <config projectName="Project" targetName="Testtest_fa2204n_balance_basic" />
      <config projectName="Project" targetName="RoboticLaserMarkingTestrfidserver" />
      <config projectName="Project" targetName="hardwaredriveragilerobotDriver" />
      <config projectName="Project" targetName="RoboticLaserMarkingTestrfiddriver" />
      <config projectName="Project" targetName="MJServerAPP" />
      <config projectName="Project" targetName="toolcommunication" />
      <config projectName="Project" targetName="Testtest_service_container" />
      <config projectName="Project" targetName="hardwaredriverElectricGripperDriver" />
      <config projectName="Project" targetName="Analysis_RobotalgorithmscoordinateTransform" />
      <config projectName="Project" targetName="Testtest_socket" />
      <config projectName="Project" targetName="toolcalbuild" />
      <config projectName="Project" targetName="Analysis_RobotdriversrobotDriver" />
      <config projectName="Project" targetName="Testtest_serial" />
      <config projectName="Project" targetName="Testtest_executor_context" />
      <config projectName="Project" targetName="hardwaredriverusbcamera" />
      <config projectName="Project" targetName="MJServer_RefactorTestsimple_feeder_client" />
      <config projectName="Project" targetName="Analysis_RobottestheaterApiTest" />
      <config projectName="Project" targetName="RoboticLaserMarkingLicenseGenerator" />
      <config projectName="Project" targetName="hardwaredriverfairinoDriver" />
      <config projectName="Project" targetName="MJServer_RefactorLibrary" />
      <config projectName="Project" targetName="hardwaredriverHikVisionCamera" />
      <config projectName="Project" targetName="toolhandeyecaluihandeyecalui" />
      <config projectName="Project" targetName="hardwaredriverjunduoHandDriver" />
      <config projectName="Project" targetName="hardwaredriverOpcUa" />
    </generated>
  </component>
  <component name="CMakeSettings">
    <configurations>
      <configuration PROFILE_NAME="Debug" ENABLED="true" CONFIG_NAME="Debug" />
    </configurations>
  </component>
  <component name="ChangeListManager">
    <list default="true" id="74a86faa-6bfd-4a7c-8c90-cf323737aac7" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="ExecutionTargetManager" SELECTED_TARGET="CMakeBuildProfile:Debug" />
  <component name="HighlightingSettingsPerFile">
    <setting file="file://C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/VC/Tools/MSVC/14.29.30133/include/string" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://C:/opt/abbrws/include/abb_librws/rws_rapid.h" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://C:/opt/auboarcs/include/aubo/aubo_api.h" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://C:/opt/auboarcs/include/aubo/math.h" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://C:/opt/auboarcs/include/aubo/register_control.h" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://C:/opt/auboarcs/include/aubo/robot/force_control.h" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://C:/opt/auboarcs/include/aubo/robot/io_control.h" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://C:/opt/auboarcs/include/aubo/robot/motion_control.h" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://C:/opt/auboarcs/include/aubo/robot/robot_algorithm.h" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://C:/opt/auboarcs/include/aubo/robot/robot_config.h" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://C:/opt/auboarcs/include/aubo/robot/robot_manage.h" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://C:/opt/auboarcs/include/aubo/robot/robot_state.h" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://C:/opt/auboarcs/include/aubo/robot_interface.h" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://C:/opt/auboarcs/include/aubo/runtime_machine.h" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://C:/opt/auboarcs/include/aubo/sync_move.h" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://C:/opt/auboarcs/include/aubo/system_info.h" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://C:/opt/robwork-21.12/include/robwork-21.12/rw/loaders/xml/XMLPathLoader.hpp" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Test/test_abb_socket/src/main.cpp" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Test/test_twoaixsrobot/src/PouringRobotController.cpp" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/fuxicore/include/SystemMonitor.h" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/hardwaredriver/abbRobotDriver/include/AbbRobotDriver.h" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/hardwaredriver/abbRobotDriver/src/AbbRobotDriver.cpp" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/hardwaredriver/abbRobotDriver/src/AbbRwsWrapper.cpp" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/hardwaredriver/modbus/include/modbusTcp.h" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/tool/cameraCalibrator/src/CameraCalibrator.cpp" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/tool/handeyecal/src/CameraCalibrator.cpp" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/tool/handeyecal/src/handeyecal.cpp" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/tool/handeyecaltest/src/handeyecaltest.cpp" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/tool/handeyecaluipathAuto/src/CalibrationUI.cpp" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/tool/handeyecaluipathAuto/src/PathManager.cpp" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="ProjectApplicationVersion">
    <option name="ide" value="CLion" />
    <option name="majorVersion" value="2025" />
    <option name="minorVersion" value="1.3" />
    <option name="productBranch" value="Classic" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="2zGQeF8Coo7mIh1BowGkmIvtf0d" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;C/C++ File.test_heating_magnetic_stirrer.cpp.executor&quot;: &quot;Run&quot;,
    &quot;CMake Application.Analysis_RobotApp.executor&quot;: &quot;Run&quot;,
    &quot;CMake Application.Analysis_RobottestbalanceDriverTest.executor&quot;: &quot;Run&quot;,
    &quot;CMake Application.Analysis_RobottestheaterApiTest.executor&quot;: &quot;Run&quot;,
    &quot;CMake Application.Analysis_RobottestheatingMagneticStirrerDriver.executor&quot;: &quot;Run&quot;,
    &quot;CMake Application.Analysis_Robottestnative_stirrer_test.executor&quot;: &quot;Run&quot;,
    &quot;CMake Application.Testtest_abb_socket.executor&quot;: &quot;Run&quot;,
    &quot;CMake Application.Testtest_twoaixsrobot.executor&quot;: &quot;Run&quot;,
    &quot;CMake Application.toolcalbuild.executor&quot;: &quot;Run&quot;,
    &quot;CMake Application.toolcaltest.executor&quot;: &quot;Run&quot;,
    &quot;CMake Application.toolhandeyecal.executor&quot;: &quot;Run&quot;,
    &quot;CMake Application.toolhandeyecaltest.executor&quot;: &quot;Run&quot;,
    &quot;CMake Application.toolhandeyecaluipathAuto.executor&quot;: &quot;Run&quot;,
    &quot;CMake 应用程序.RoboticLaserMarkingUI.executor&quot;: &quot;Run&quot;,
    &quot;CMake 应用程序.Testtest_config_manager.executor&quot;: &quot;Run&quot;,
    &quot;CMake 应用程序.Testtest_event_listener.executor&quot;: &quot;Run&quot;,
    &quot;CMake 应用程序.Testtest_executor.executor&quot;: &quot;Run&quot;,
    &quot;CMake 应用程序.Testtest_executor_context.executor&quot;: &quot;Run&quot;,
    &quot;CMake 应用程序.Testtest_license_manager.executor&quot;: &quot;Run&quot;,
    &quot;CMake 应用程序.Testtest_license_ui.executor&quot;: &quot;Run&quot;,
    &quot;CMake 应用程序.Testtest_network.executor&quot;: &quot;Run&quot;,
    &quot;CMake 应用程序.Testtest_serial.executor&quot;: &quot;Run&quot;,
    &quot;CMake 应用程序.Testtest_socket.executor&quot;: &quot;Run&quot;,
    &quot;CMake 应用程序.Testtest_sqlite.executor&quot;: &quot;Run&quot;,
    &quot;CMake 应用程序.Testtest_taskflow.executor&quot;: &quot;Run&quot;,
    &quot;Google Test.HeatingMagneticStirrerDriverTest.SetProgramStepsValid.executor&quot;: &quot;Run&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.RadMigrateCodeStyle&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.cidr.known.project.marker&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.readMode.enableVisualFormatting&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.west.config.association.type.startup.service&quot;: &quot;true&quot;,
    &quot;cf.first.check.clang-format&quot;: &quot;false&quot;,
    &quot;cidr.known.project.marker&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/newfuxios&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;CMakeSettings&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\newfuxios\Analysis_Robot\test\heatingMagneticStirrerDriver\src" />
    </key>
  </component>
  <component name="RunManager" selected="CMake Application.Analysis_RobottestbalanceDriverTest">
    <configuration name="Analysis_RobotApp" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="Analysis_RobotApp" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="Project" RUN_TARGET_NAME="Analysis_RobotApp">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="Analysis_RobotalgorithmscoordinateTransform" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="Analysis_RobotalgorithmscoordinateTransform" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="Analysis_RobotalgorithmspouringControl" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="Analysis_RobotalgorithmspouringControl" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="Analysis_RobotalgorithmstcpPositionMaintain" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="Analysis_RobotalgorithmstcpPositionMaintain" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="Analysis_RobotdriversaixsDriver" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="Analysis_RobotdriversaixsDriver" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="Analysis_RobotdriversbalanceDriver" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="Analysis_RobotdriversbalanceDriver" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="Analysis_RobotdriversheatingMagneticStirrerDriver" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="Analysis_RobotdriversheatingMagneticStirrerDriver" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="Analysis_RobotdriversmoistureAnalyzerDriver" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="Analysis_RobotdriversmoistureAnalyzerDriver" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="Analysis_RobotdriversplcDriver" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="Analysis_RobotdriversplcDriver" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="Analysis_RobotdriversrestInterfaceDriver" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="Analysis_RobotdriversrestInterfaceDriver" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="Analysis_RobotdriversrobotDriver" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="Analysis_RobotdriversrobotDriver" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="Analysis_RobottestbalanceDriverTest" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="Analysis_RobottestbalanceDriverTest" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="Project" RUN_TARGET_NAME="Analysis_RobottestbalanceDriverTest">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="Analysis_RobottestheaterApiTest" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="Analysis_RobottestheaterApiTest" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="Project" RUN_TARGET_NAME="Analysis_RobottestheaterApiTest">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="Analysis_RobottestheatingMagneticStirrerDriver" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="Analysis_RobottestheatingMagneticStirrerDriver" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="Project" RUN_TARGET_NAME="Analysis_RobottestheatingMagneticStirrerDriver">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="MJServerAPP" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="MJServerAPP" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="Project" RUN_TARGET_NAME="MJServerAPP">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="MJServer_RefactorApp" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="MJServer_RefactorApp" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="Project" RUN_TARGET_NAME="MJServer_RefactorApp">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="MJServer_RefactorLibrary" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="MJServer_RefactorLibrary" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="MJServer_RefactorTestphase1_test" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="MJServer_RefactorTestphase1_test" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="Project" RUN_TARGET_NAME="MJServer_RefactorTestphase1_test">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="MJServer_RefactorTestsimple_abb_client" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="MJServer_RefactorTestsimple_abb_client" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="Project" RUN_TARGET_NAME="MJServer_RefactorTestsimple_abb_client">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="MJServer_RefactorTestsimple_feeder_client" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="MJServer_RefactorTestsimple_feeder_client" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="Project" RUN_TARGET_NAME="MJServer_RefactorTestsimple_feeder_client">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="RoboticLaserMarkingAbbDriver" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="RoboticLaserMarkingAbbDriver" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="RoboticLaserMarkingLicenseGenerator" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="RoboticLaserMarkingLicenseGenerator" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="Project" RUN_TARGET_NAME="RoboticLaserMarkingLicenseGenerator">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="RoboticLaserMarkingRFIDDriver" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="RoboticLaserMarkingRFIDDriver" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="RoboticLaserMarkingTestabbsocket" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="RoboticLaserMarkingTestabbsocket" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="Project" RUN_TARGET_NAME="RoboticLaserMarkingTestabbsocket">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="RoboticLaserMarkingTestlaser" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="RoboticLaserMarkingTestlaser" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="Project" RUN_TARGET_NAME="RoboticLaserMarkingTestlaser">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="RoboticLaserMarkingTestlaserUI" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="RoboticLaserMarkingTestlaserUI" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="Project" RUN_TARGET_NAME="RoboticLaserMarkingTestlaserUI">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="RoboticLaserMarkingTestrfiddriver" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="RoboticLaserMarkingTestrfiddriver" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="Project" RUN_TARGET_NAME="RoboticLaserMarkingTestrfiddriver">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="RoboticLaserMarkingTestrfidserver" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="RoboticLaserMarkingTestrfidserver" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="Project" RUN_TARGET_NAME="RoboticLaserMarkingTestrfidserver">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="RoboticLaserMarkingUI" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="RoboticLaserMarkingUI" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="Project" RUN_TARGET_NAME="RoboticLaserMarkingUI">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="RoboticLaserMarkinglaserDriver" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="RoboticLaserMarkinglaserDriver" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="RoboticLaserMarkinglaserDriverSim" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="RoboticLaserMarkinglaserDriverSim" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="Testtest_abb_socket" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="Testtest_abb_socket" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="Project" RUN_TARGET_NAME="Testtest_abb_socket">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="Testtest_config_manager" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="Testtest_config_manager" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="Project" RUN_TARGET_NAME="Testtest_config_manager">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="Testtest_csv" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="Testtest_csv" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="Project" RUN_TARGET_NAME="Testtest_csv">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="Testtest_event_listener" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="Testtest_event_listener" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="Project" RUN_TARGET_NAME="Testtest_event_listener">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="Testtest_executor" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="Testtest_executor" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="Project" RUN_TARGET_NAME="Testtest_executor">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="Testtest_executor_context" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="Testtest_executor_context" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="Project" RUN_TARGET_NAME="Testtest_executor_context">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="Testtest_fa2204n_balance" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="Testtest_fa2204n_balance" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="Project" RUN_TARGET_NAME="Testtest_fa2204n_balance">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="Testtest_fa2204n_balance_basic" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="Testtest_fa2204n_balance_basic" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="Project" RUN_TARGET_NAME="Testtest_fa2204n_balance_basic">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="Testtest_fileutil" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="Testtest_fileutil" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="Project" RUN_TARGET_NAME="Testtest_fileutil">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="Testtest_json" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="Testtest_json" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="Project" RUN_TARGET_NAME="Testtest_json">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="Testtest_license_manager" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="Testtest_license_manager" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="Project" RUN_TARGET_NAME="Testtest_license_manager">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="Testtest_license_ui" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="Testtest_license_ui" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="Project" RUN_TARGET_NAME="Testtest_license_ui">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="Testtest_network" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="Testtest_network" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="Project" RUN_TARGET_NAME="Testtest_network">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="Testtest_serial" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="Testtest_serial" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="Project" RUN_TARGET_NAME="Testtest_serial">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="Testtest_service_container" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="Testtest_service_container" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="Project" RUN_TARGET_NAME="Testtest_service_container">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="Testtest_socket" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="Testtest_socket" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="Project" RUN_TARGET_NAME="Testtest_socket">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="Testtest_sqlite" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="Testtest_sqlite" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="Project" RUN_TARGET_NAME="Testtest_sqlite">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="Testtest_taskflow" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="Testtest_taskflow" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="Project" RUN_TARGET_NAME="Testtest_taskflow">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="Testtest_twoaixsrobot" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="Testtest_twoaixsrobot" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="Project" RUN_TARGET_NAME="Testtest_twoaixsrobot">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="Testtest_xml" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="Testtest_xml" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="Project" RUN_TARGET_NAME="Testtest_xml">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="fuxicommon" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="fuxicommon" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="fuxicore" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="fuxicore" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="hardwaredriverAuboArcsDriver" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="hardwaredriverAuboArcsDriver" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="hardwaredriverAuboDriver" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="hardwaredriverAuboDriver" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="hardwaredriverElectricGripperDriver" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="hardwaredriverElectricGripperDriver" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="hardwaredriverHikVisionCamera" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="hardwaredriverHikVisionCamera" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="hardwaredriverLabelPrinter" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="hardwaredriverLabelPrinter" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="hardwaredriverMettlerBalance" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="hardwaredriverMettlerBalance" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="hardwaredriverOpcDa" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="hardwaredriverOpcDa" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="hardwaredriverOpcUa" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="hardwaredriverOpcUa" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="hardwaredriverabbRobotDriver" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="hardwaredriverabbRobotDriver" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="hardwaredriveragilerobotDriver" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="hardwaredriveragilerobotDriver" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="hardwaredriverfairinoDriver" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="hardwaredriverfairinoDriver" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="hardwaredriverjunduoHandDriver" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="hardwaredriverjunduoHandDriver" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="hardwaredrivermodbus" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="hardwaredrivermodbus" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="hardwaredriverserial" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="hardwaredriverserial" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="hardwaredriversocket" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="hardwaredriversocket" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="hardwaredriverusbcamera" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="hardwaredriverusbcamera" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="test_micro_dosing" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="test_micro_dosing" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="Project" RUN_TARGET_NAME="test_micro_dosing">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="toolcalbuild" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="toolcalbuild" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="Project" RUN_TARGET_NAME="toolcalbuild">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="toolcaltest" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="toolcaltest" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="Project" RUN_TARGET_NAME="toolcaltest">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="toolcameraCalibrator" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="toolcameraCalibrator" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="toolcommunication" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="toolcommunication" CONFIG_NAME="Debug">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="toolhandeyecal" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="toolhandeyecal" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="Project" RUN_TARGET_NAME="toolhandeyecal">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="toolhandeyecaltest" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="toolhandeyecaltest" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="Project" RUN_TARGET_NAME="toolhandeyecaltest">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="toolhandeyecaluihandeyecalui" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="toolhandeyecaluihandeyecalui" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="Project" RUN_TARGET_NAME="toolhandeyecaluihandeyecalui">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="toolhandeyecaluipath" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="toolhandeyecaluipath" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="Project" RUN_TARGET_NAME="toolhandeyecaluipath">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="toolhandeyecaluipathAuto" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="toolhandeyecaluipathAuto" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="Project" RUN_TARGET_NAME="toolhandeyecaluipathAuto">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="toolverify_calibration" type="CMakeRunConfiguration" factoryName="Application" REDIRECT_INPUT="false" ELEVATE="false" USE_EXTERNAL_CONSOLE="false" EMULATE_TERMINAL="false" PASS_PARENT_ENVS_2="true" PROJECT_NAME="Project" TARGET_NAME="toolverify_calibration" CONFIG_NAME="Debug" RUN_TARGET_PROJECT_NAME="Project" RUN_TARGET_NAME="toolverify_calibration">
      <method v="2">
        <option name="com.jetbrains.cidr.execution.CidrBuildBeforeRunTaskProvider$BuildBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="CMake Application.RoboticLaserMarkingAbbDriver" />
      <item itemvalue="CMake Application.Analysis_RobotApp" />
      <item itemvalue="CMake Application.Analysis_RobottestbalanceDriverTest" />
      <item itemvalue="CMake Application.Analysis_RobottestheaterApiTest" />
      <item itemvalue="CMake Application.Analysis_RobottestheatingMagneticStirrerDriver" />
      <item itemvalue="CMake Application.MJServer_RefactorApp" />
      <item itemvalue="CMake Application.MJServer_RefactorTestphase1_test" />
      <item itemvalue="CMake Application.MJServer_RefactorTestsimple_abb_client" />
      <item itemvalue="CMake Application.MJServer_RefactorTestsimple_feeder_client" />
      <item itemvalue="CMake Application.MJServerAPP" />
      <item itemvalue="CMake Application.RoboticLaserMarkingLicenseGenerator" />
      <item itemvalue="CMake Application.RoboticLaserMarkingRFIDDriver" />
      <item itemvalue="CMake Application.RoboticLaserMarkingTestabbsocket" />
      <item itemvalue="CMake Application.RoboticLaserMarkingTestlaserUI" />
      <item itemvalue="CMake Application.RoboticLaserMarkingTestlaser" />
      <item itemvalue="CMake Application.RoboticLaserMarkingTestrfiddriver" />
      <item itemvalue="CMake Application.RoboticLaserMarkingTestrfidserver" />
      <item itemvalue="CMake Application.RoboticLaserMarkingUI" />
      <item itemvalue="CMake Application.RoboticLaserMarkinglaserDriverSim" />
      <item itemvalue="CMake Application.RoboticLaserMarkinglaserDriver" />
      <item itemvalue="CMake Application.Testtest_abb_socket" />
      <item itemvalue="CMake Application.Testtest_config_manager" />
      <item itemvalue="CMake Application.Testtest_csv" />
      <item itemvalue="CMake Application.Testtest_event_listener" />
      <item itemvalue="CMake Application.Testtest_executor" />
      <item itemvalue="CMake Application.Testtest_executor_context" />
      <item itemvalue="CMake Application.Testtest_fa2204n_balance" />
      <item itemvalue="CMake Application.Testtest_fa2204n_balance_basic" />
      <item itemvalue="CMake Application.Testtest_fileutil" />
      <item itemvalue="CMake Application.Testtest_json" />
      <item itemvalue="CMake Application.Testtest_license_manager" />
      <item itemvalue="CMake Application.Testtest_license_ui" />
      <item itemvalue="CMake Application.Testtest_network" />
      <item itemvalue="CMake Application.Testtest_serial" />
      <item itemvalue="CMake Application.Testtest_service_container" />
      <item itemvalue="CMake Application.Testtest_socket" />
      <item itemvalue="CMake Application.Testtest_sqlite" />
      <item itemvalue="CMake Application.Testtest_taskflow" />
      <item itemvalue="CMake Application.Testtest_twoaixsrobot" />
      <item itemvalue="CMake Application.Testtest_xml" />
      <item itemvalue="CMake Application.fuxicommon" />
      <item itemvalue="CMake Application.fuxicore" />
      <item itemvalue="CMake Application.hardwaredriverAuboArcsDriver" />
      <item itemvalue="CMake Application.hardwaredriverAuboDriver" />
      <item itemvalue="CMake Application.hardwaredriverElectricGripperDriver" />
      <item itemvalue="CMake Application.hardwaredriverHikVisionCamera" />
      <item itemvalue="CMake Application.hardwaredriverLabelPrinter" />
      <item itemvalue="CMake Application.hardwaredriverMettlerBalance" />
      <item itemvalue="CMake Application.hardwaredriverOpcDa" />
      <item itemvalue="CMake Application.hardwaredriverOpcUa" />
      <item itemvalue="CMake Application.hardwaredriverabbRobotDriver" />
      <item itemvalue="CMake Application.hardwaredriveragilerobotDriver" />
      <item itemvalue="CMake Application.hardwaredriverfairinoDriver" />
      <item itemvalue="CMake Application.hardwaredriverjunduoHandDriver" />
      <item itemvalue="CMake Application.hardwaredrivermodbus" />
      <item itemvalue="CMake Application.hardwaredriverserial" />
      <item itemvalue="CMake Application.hardwaredriversocket" />
      <item itemvalue="CMake Application.hardwaredriverusbcamera" />
      <item itemvalue="CMake Application.MJServer_RefactorLibrary" />
      <item itemvalue="CMake Application.toolcalbuild" />
      <item itemvalue="CMake Application.toolcaltest" />
      <item itemvalue="CMake Application.toolcameraCalibrator" />
      <item itemvalue="CMake Application.toolcommunication" />
      <item itemvalue="CMake Application.test_micro_dosing" />
      <item itemvalue="CMake Application.toolhandeyecal" />
      <item itemvalue="CMake Application.toolhandeyecaltest" />
      <item itemvalue="CMake Application.toolhandeyecaluihandeyecalui" />
      <item itemvalue="CMake Application.toolhandeyecaluipathAuto" />
      <item itemvalue="CMake Application.toolhandeyecaluipath" />
      <item itemvalue="CMake Application.toolverify_calibration" />
      <item itemvalue="CMake Application.Analysis_RobotalgorithmscoordinateTransform" />
      <item itemvalue="CMake Application.Analysis_RobotalgorithmspouringControl" />
      <item itemvalue="CMake Application.Analysis_RobotalgorithmstcpPositionMaintain" />
      <item itemvalue="CMake Application.Analysis_RobotdriversaixsDriver" />
      <item itemvalue="CMake Application.Analysis_RobotdriversbalanceDriver" />
      <item itemvalue="CMake Application.Analysis_RobotdriversheatingMagneticStirrerDriver" />
      <item itemvalue="CMake Application.Analysis_RobotdriversmoistureAnalyzerDriver" />
      <item itemvalue="CMake Application.Analysis_RobotdriversplcDriver" />
      <item itemvalue="CMake Application.Analysis_RobotdriversrestInterfaceDriver" />
      <item itemvalue="CMake Application.Analysis_RobotdriversrobotDriver" />
    </list>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="74a86faa-6bfd-4a7c-8c90-cf323737aac7" name="更改" comment="" />
      <created>1751355023240</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751355023240</updated>
      <workItem from="1751355024339" duration="6567000" />
      <workItem from="1751416116271" duration="6873000" />
      <workItem from="1751424708619" duration="4625000" />
      <workItem from="1751435945985" duration="9934000" />
      <workItem from="1751503889240" duration="1387000" />
      <workItem from="1751505299084" duration="288000" />
      <workItem from="1751505607571" duration="2031000" />
      <workItem from="1751507716057" duration="644000" />
      <workItem from="1751508461205" duration="495000" />
      <workItem from="1751509033322" duration="88000" />
      <workItem from="1751509213170" duration="2909000" />
      <workItem from="1751513436588" duration="1504000" />
      <workItem from="1751522828092" duration="1477000" />
      <workItem from="1751531354072" duration="21000" />
      <workItem from="1751589746719" duration="6000" />
      <workItem from="1751592854823" duration="544000" />
      <workItem from="1751593412500" duration="59000" />
      <workItem from="1751593481474" duration="2724000" />
      <workItem from="1751597022360" duration="3249000" />
      <workItem from="1751609280651" duration="1861000" />
      <workItem from="1751848351323" duration="2969000" />
      <workItem from="1752105814465" duration="6437000" />
      <workItem from="1752171952122" duration="631000" />
      <workItem from="1752213848087" duration="431000" />
      <workItem from="1752221714074" duration="289000" />
      <workItem from="1752453474640" duration="311000" />
      <workItem from="1752742180499" duration="636000" />
      <workItem from="1753166325787" duration="10000" />
      <workItem from="1754527252867" duration="563000" />
      <workItem from="1754546348965" duration="990000" />
      <workItem from="1754549481145" duration="5063000" />
      <workItem from="1754964119645" duration="3000" />
      <workItem from="1754965566803" duration="2000" />
      <workItem from="1754965592770" duration="4221000" />
      <workItem from="1754979281277" duration="5621000" />
      <workItem from="1755048418037" duration="1371000" />
      <workItem from="1755050657429" duration="1648000" />
      <workItem from="1755068067785" duration="2242000" />
      <workItem from="1755071282062" duration="4565000" />
      <workItem from="1755132500608" duration="9688000" />
      <workItem from="1755151393526" duration="6392000" />
      <workItem from="1755249423288" duration="667000" />
      <workItem from="1755481384391" duration="1522000" />
      <workItem from="1755483488879" duration="515000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VCPKGProject">
    <isAutomaticCheckingOnLaunch value="false" />
    <isAutomaticFoundErrors value="true" />
    <isAutomaticReloadCMake value="true" />
  </component>
</project>