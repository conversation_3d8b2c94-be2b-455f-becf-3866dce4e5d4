cmake_minimum_required(VERSION 3.16)
project(BalanceDriverTest)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置输出目录
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)

# 查找依赖包
find_package(PkgConfig REQUIRED)
pkg_check_modules(MODBUS REQUIRED libmodbus)

# 包含目录
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/../../drivers/balanceDriver/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../../common/include
    ${MODBUS_INCLUDE_DIRS}
)

# 链接目录
link_directories(
    ${MODBUS_LIBRARY_DIRS}
)

# 源文件
set(SOURCES
    src/main.cpp
    src/BalanceTest.cpp
    ../../drivers/balanceDriver/src/BalanceDriver.cpp
)

# 创建可执行文件
add_executable(${PROJECT_NAME} ${SOURCES})

# 链接库
target_link_libraries(${PROJECT_NAME}
    ${MODBUS_LIBRARIES}
    pthread
)

# 编译选项
target_compile_options(${PROJECT_NAME} PRIVATE ${MODBUS_CFLAGS_OTHER})

# 设置编译定义
target_compile_definitions(${PROJECT_NAME} PRIVATE
    _GNU_SOURCE
)

# Windows特定设置
if(WIN32)
    target_link_libraries(${PROJECT_NAME} ws2_32)
endif()
