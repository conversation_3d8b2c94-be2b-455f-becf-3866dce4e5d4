{"artifacts": [{"path": "D:/newfuxios/install/x64-install/dev/bin/toolhandeyecal.exe"}, {"path": "D:/newfuxios/install/x64-install/dev/bin/toolhandeyecal.pdb"}], "backtrace": 3, "backtraceGraph": {"commands": ["add_executable", "include", "target_link_libraries", "add_unique_libraries", "set_property", "find_package", "boost_find_component", "add_definitions", "include_directories"], "files": ["builder/cmake/executable.cmake", "tool/handeyecal/CMakeLists.txt", "builder/cmake/common.cmake", "builder/cmake/library.cmake", "hardwaredriver/HikVisionCamera/CMakeLists.txt", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_filesystem-1.78.0/boost_filesystem-config.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/Boost-1.78.0/BoostConfig.cmake", "C:/Program Files/JetBrains/CLion 2025.1.3/bin/cmake/win/x64/share/cmake-3.31/Modules/FindBoost.cmake", "builder/cmake/add_boost.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_thread-1.78.0/boost_thread-config.cmake", "fuxicommon/CMakeLists.txt", "hardwaredriver/AuboArcsDriver/CMakeLists.txt", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_filesystem-1.78.0/libboost_filesystem-variant-vc142-mt-gd-x64-1_78-static.cmake", "C:/opt/robwork-21.12/robwork-21.12/cmake/RobWorkConfig.cmake", "builder/cmake/add_robwork.cmake", "builder/cmake/add_eigen.cmake", "builder/cmake/add_opencv.cmake", "builder/cmake/add_glog.cmake", "builder/cmake/add_aubo.cmake"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 2, "parent": 0}, {"file": 0, "parent": 1}, {"command": 0, "file": 0, "line": 71, "parent": 2}, {"command": 3, "file": 0, "line": 77, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 4}, {"file": 4}, {"command": 1, "file": 4, "line": 3, "parent": 6}, {"file": 3, "parent": 7}, {"command": 3, "file": 3, "line": 78, "parent": 8}, {"command": 2, "file": 3, "line": 53, "parent": 9}, {"command": 1, "file": 3, "line": 1, "parent": 8}, {"file": 2, "parent": 11}, {"command": 1, "file": 2, "line": 81, "parent": 12}, {"file": 8, "parent": 13}, {"command": 5, "file": 8, "line": 33, "parent": 14}, {"file": 7, "parent": 15}, {"command": 5, "file": 7, "line": 610, "parent": 16}, {"file": 6, "parent": 17}, {"command": 6, "file": 6, "line": 262, "parent": 18}, {"command": 5, "file": 6, "line": 141, "parent": 19}, {"file": 5, "parent": 20}, {"command": 4, "file": 5, "line": 103, "parent": 21}, {"command": 6, "file": 6, "line": 262, "parent": 18}, {"command": 5, "file": 6, "line": 141, "parent": 23}, {"file": 9, "parent": 24}, {"command": 4, "file": 9, "line": 103, "parent": 25}, {"command": 3, "file": 3, "line": 84, "parent": 8}, {"command": 2, "file": 3, "line": 53, "parent": 27}, {"command": 3, "file": 3, "line": 84, "parent": 8}, {"command": 2, "file": 3, "line": 53, "parent": 29}, {"command": 3, "file": 3, "line": 84, "parent": 8}, {"command": 2, "file": 3, "line": 53, "parent": 31}, {"command": 3, "file": 3, "line": 84, "parent": 8}, {"command": 2, "file": 3, "line": 53, "parent": 33}, {"file": 10}, {"command": 1, "file": 10, "line": 3, "parent": 35}, {"file": 3, "parent": 36}, {"command": 3, "file": 3, "line": 78, "parent": 37}, {"command": 2, "file": 3, "line": 53, "parent": 38}, {"command": 1, "file": 3, "line": 1, "parent": 37}, {"file": 2, "parent": 40}, {"command": 1, "file": 2, "line": 81, "parent": 41}, {"file": 8, "parent": 42}, {"command": 5, "file": 8, "line": 33, "parent": 43}, {"file": 7, "parent": 44}, {"command": 5, "file": 7, "line": 610, "parent": 45}, {"file": 6, "parent": 46}, {"command": 6, "file": 6, "line": 262, "parent": 47}, {"command": 5, "file": 6, "line": 141, "parent": 48}, {"file": 5, "parent": 49}, {"command": 4, "file": 5, "line": 103, "parent": 50}, {"command": 6, "file": 6, "line": 262, "parent": 47}, {"command": 5, "file": 6, "line": 141, "parent": 52}, {"file": 9, "parent": 53}, {"command": 4, "file": 9, "line": 103, "parent": 54}, {"command": 3, "file": 3, "line": 84, "parent": 37}, {"command": 2, "file": 3, "line": 53, "parent": 56}, {"command": 3, "file": 3, "line": 84, "parent": 37}, {"command": 2, "file": 3, "line": 53, "parent": 58}, {"command": 3, "file": 3, "line": 84, "parent": 37}, {"command": 2, "file": 3, "line": 53, "parent": 60}, {"command": 3, "file": 3, "line": 84, "parent": 37}, {"command": 2, "file": 3, "line": 53, "parent": 62}, {"file": 11}, {"command": 1, "file": 11, "line": 3, "parent": 64}, {"file": 3, "parent": 65}, {"command": 3, "file": 3, "line": 78, "parent": 66}, {"command": 2, "file": 3, "line": 53, "parent": 67}, {"command": 1, "file": 3, "line": 1, "parent": 66}, {"file": 2, "parent": 69}, {"command": 1, "file": 2, "line": 81, "parent": 70}, {"file": 8, "parent": 71}, {"command": 5, "file": 8, "line": 33, "parent": 72}, {"file": 7, "parent": 73}, {"command": 5, "file": 7, "line": 610, "parent": 74}, {"file": 6, "parent": 75}, {"command": 6, "file": 6, "line": 262, "parent": 76}, {"command": 5, "file": 6, "line": 141, "parent": 77}, {"file": 5, "parent": 78}, {"command": 4, "file": 5, "line": 103, "parent": 79}, {"command": 6, "file": 6, "line": 262, "parent": 76}, {"command": 5, "file": 6, "line": 141, "parent": 81}, {"file": 9, "parent": 82}, {"command": 4, "file": 9, "line": 103, "parent": 83}, {"command": 3, "file": 3, "line": 84, "parent": 66}, {"command": 2, "file": 3, "line": 53, "parent": 85}, {"command": 3, "file": 3, "line": 84, "parent": 66}, {"command": 2, "file": 3, "line": 53, "parent": 87}, {"command": 3, "file": 3, "line": 84, "parent": 66}, {"command": 2, "file": 3, "line": 53, "parent": 89}, {"command": 3, "file": 3, "line": 84, "parent": 66}, {"command": 2, "file": 3, "line": 53, "parent": 91}, {"command": 1, "file": 0, "line": 1, "parent": 2}, {"file": 2, "parent": 93}, {"command": 1, "file": 2, "line": 81, "parent": 94}, {"file": 8, "parent": 95}, {"command": 5, "file": 8, "line": 33, "parent": 96}, {"file": 7, "parent": 97}, {"command": 5, "file": 7, "line": 610, "parent": 98}, {"file": 6, "parent": 99}, {"command": 6, "file": 6, "line": 262, "parent": 100}, {"command": 5, "file": 6, "line": 141, "parent": 101}, {"file": 5, "parent": 102}, {"command": 1, "file": 5, "line": 53, "parent": 103}, {"file": 12, "parent": 104}, {"command": 4, "file": 12, "line": 104, "parent": 105}, {"command": 4, "file": 5, "line": 103, "parent": 103}, {"command": 6, "file": 6, "line": 262, "parent": 100}, {"command": 5, "file": 6, "line": 141, "parent": 108}, {"file": 9, "parent": 109}, {"command": 4, "file": 9, "line": 103, "parent": 110}, {"command": 1, "file": 2, "line": 81, "parent": 94}, {"file": 14, "parent": 112}, {"command": 5, "file": 14, "line": 18, "parent": 113}, {"file": 13, "parent": 114}, {"command": 7, "file": 13, "line": 310, "parent": 115}, {"command": 8, "file": 2, "line": 54, "parent": 94}, {"command": 1, "file": 2, "line": 81, "parent": 94}, {"file": 15, "parent": 118}, {"command": 8, "file": 15, "line": 30, "parent": 119}, {"command": 1, "file": 2, "line": 81, "parent": 94}, {"file": 16, "parent": 121}, {"command": 8, "file": 16, "line": 23, "parent": 122}, {"command": 1, "file": 2, "line": 81, "parent": 94}, {"file": 17, "parent": 124}, {"command": 8, "file": 17, "line": 16, "parent": 125}, {"command": 1, "file": 2, "line": 81, "parent": 94}, {"file": 18, "parent": 127}, {"command": 8, "file": 18, "line": 8, "parent": 128}, {"command": 8, "file": 14, "line": 21, "parent": 113}, {"command": 8, "file": 8, "line": 40, "parent": 96}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc -EHa -bigobj /MP -openmp /MDd /Zi /Ob0 /Od /RTC1 -std:c++20"}], "defines": [{"backtrace": 116, "define": "BIND_FORTRAN_LOWERCASE_UNDERSCORE"}, {"backtrace": 5, "define": "BOOST_ATOMIC_NO_LIB"}, {"backtrace": 5, "define": "BOOST_CHRONO_NO_LIB"}, {"backtrace": 5, "define": "BOOST_DATE_TIME_NO_LIB"}, {"backtrace": 5, "define": "BOOST_FILESYSTEM_NO_LIB"}, {"backtrace": 5, "define": "BOOST_IOSTREAMS_NO_LIB"}, {"backtrace": 5, "define": "BOOST_PROGRAM_OPTIONS_NO_LIB"}, {"backtrace": 5, "define": "BOOST_SERIALIZATION_NO_LIB"}, {"backtrace": 5, "define": "BOOST_SYSTEM_NO_LIB"}, {"backtrace": 5, "define": "BOOST_THREAD_NO_LIB"}, {"backtrace": 116, "define": "MSVC_AMD64"}, {"backtrace": 116, "define": "NOMINMAX"}, {"backtrace": 5, "define": "QT_CORE_LIB"}, {"backtrace": 5, "define": "QT_GUI_LIB"}, {"backtrace": 5, "define": "QT_SQL_LIB"}, {"backtrace": 5, "define": "QT_WIDGETS_LIB"}, {"backtrace": 5, "define": "RTTR_DLL"}, {"backtrace": 116, "define": "WIN32_LEAN_AND_MEAN"}, {"backtrace": 116, "define": "_CRT_SECURE_NO_DEPRECATE"}, {"backtrace": 116, "define": "_CRT_SECURE_NO_WARNINGS"}, {"backtrace": 116, "define": "_SCL_SECURE_NO_WARNINGS"}, {"backtrace": 116, "define": "_WIN32_WINNT=0x0501"}], "includes": [{"backtrace": 117, "path": "D:/newfuxios/tool/handeyecal/include"}, {"backtrace": 120, "path": "C:/opt/PCL/3rdParty/Eigen/eigen3"}, {"backtrace": 123, "path": "C:/opt/opencv/build/include"}, {"backtrace": 126, "path": "C:/opt/glog/include"}, {"backtrace": 129, "path": "C:/opt/aubo/include"}, {"backtrace": 130, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/eigen3"}, {"backtrace": 130, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../../include/robwork-21.12"}, {"backtrace": 130, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/rwyaobi"}, {"backtrace": 130, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/rwpqp"}, {"backtrace": 130, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/qhull/src"}, {"backtrace": 130, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/csgjs/src"}, {"backtrace": 130, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/zlib"}, {"backtrace": 130, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/fcl/fcl/include"}, {"backtrace": 130, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/assimp/include"}, {"backtrace": 130, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext"}, {"backtrace": 5, "path": "D:/newfuxios/hardwaredriver/HikVisionCamera/include"}, {"backtrace": 5, "path": "C:/opt/hikvision/include"}, {"backtrace": 5, "path": "D:/newfuxios/hardwaredriver/AuboArcsDriver/include"}, {"backtrace": 5, "path": "C:/opt/auboarcs/include"}, {"backtrace": 5, "path": "D:/newfuxios/fuxicommon/include"}, {"backtrace": 5, "path": "C:/opt/openssl/include"}, {"backtrace": 131, "isSystem": true, "path": "C:/opt/PCL/3rdParty/Boost/include/boost-1_78"}, {"backtrace": 5, "isSystem": true, "path": "C:/opt/rttr/include"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/./mkspecs/win32-msvc"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSql"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets"}], "language": "CXX", "languageStandard": {"backtraces": [5, 5], "standard": "20"}, "sourceIndexes": [0, 1, 2]}], "dependencies": [{"backtrace": 5, "id": "fuxicommon::@58335e9a86196d0a97e7"}, {"backtrace": 5, "id": "hardwaredriverHikVisionCamera::@bc252bb14595a0f09d26"}, {"backtrace": 5, "id": "hardwaredriverAuboArcsDriver::@fd5e493b37d2bab880d9"}], "id": "toolhandeyecal::@d7390e83b7e4f79f633d", "link": {"commandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc -EHa -bigobj /MP -openmp /MDd /Zi /Ob0 /Od /RTC1", "role": "flags"}, {"fragment": "/machine:x64                                                                                                                                                                                                                                                                         /debug /INCREMENTAL /subsystem:console", "role": "flags"}, {"backtrace": 5, "fragment": "hardwaredriver\\HikVisionCamera\\hardwaredriverHikVisionCamera.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\yaobi.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\pqp.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\fcl.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\RobWork\\static\\sdurw_qhull.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\RobWork\\static\\sdurw_csgjs.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_assimp.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_unzip.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_algorithms.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_pathplanners.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_pathoptimization.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_simulation.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_opengl.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_assembly.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_task.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_calibration.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_csg.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_control.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_proximitystrategies.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_core.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_common.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_math.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "opengl32.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "glu32.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_filesystem-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_regex-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_regex-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_serialization-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_serialization-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_system-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_system-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_thread-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_thread-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_program_options-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_program_options-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_chrono-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_chrono-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_atomic-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_atomic-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_test_exec_monitor-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_test_exec_monitor-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_unit_test_framework-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_unit_test_framework-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\aubo\\lib\\libserviceinterface.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "hardwaredriver\\AuboArcsDriver\\hardwaredriverAuboArcsDriver.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\glog\\lib\\glogd.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\opencv\\build\\x64\\vc16\\lib\\opencv_world4110d.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\opencv\\build\\x64\\vc16\\lib\\opencv_img_hash4110d.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_system-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_filesystem-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_date_time-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_iostreams-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_serialization-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_thread-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_program_options-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "fuxicommon\\fuxicommon.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "C:\\opt\\hikvision\\lib\\MvCameraControl.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_system-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_filesystem-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 22, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_atomic-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_date_time-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_iostreams-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_serialization-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_thread-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 26, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_chrono-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_program_options-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "C:\\opt\\rttr\\lib\\rttr_core_d.lib", "role": "libraries"}, {"backtrace": 28, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Sqld.lib", "role": "libraries"}, {"backtrace": 30, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Widgetsd.lib", "role": "libraries"}, {"backtrace": 32, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Guid.lib", "role": "libraries"}, {"backtrace": 34, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Cored.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\yaobi.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\pqp.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\fcl.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\RobWork\\static\\sdurw_qhull.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\RobWork\\static\\sdurw_csgjs.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_assimp.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_unzip.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_algorithms.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_pathplanners.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_pathoptimization.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_simulation.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_opengl.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_assembly.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_task.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_calibration.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_csg.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_control.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_proximitystrategies.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_core.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_common.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_math.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "opengl32.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "glu32.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_filesystem-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_regex-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_regex-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_serialization-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_serialization-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_system-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_system-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_thread-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_thread-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_program_options-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_program_options-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_chrono-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_chrono-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_atomic-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_atomic-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_test_exec_monitor-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_test_exec_monitor-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_unit_test_framework-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_unit_test_framework-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\opencv\\build\\x64\\vc16\\lib\\opencv_world4110d.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\opencv\\build\\x64\\vc16\\lib\\opencv_img_hash4110d.lib", "role": "libraries"}, {"backtrace": 39, "fragment": "C:\\opt\\xerces-c\\lib\\xerces-c_3D.lib", "role": "libraries"}, {"backtrace": 39, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_system-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 39, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_filesystem-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 51, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_atomic-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 39, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_date_time-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 39, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_iostreams-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 39, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_serialization-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 39, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_thread-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 55, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_chrono-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 39, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_program_options-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 39, "fragment": "C:\\opt\\openssl\\lib\\libssl.lib", "role": "libraries"}, {"backtrace": 39, "fragment": "C:\\opt\\openssl\\lib\\libcrypto.lib", "role": "libraries"}, {"backtrace": 39, "fragment": "C:\\opt\\openssl\\lib\\jsoncpp.lib", "role": "libraries"}, {"backtrace": 39, "fragment": "C:\\opt\\openssl\\lib\\libcurl.lib", "role": "libraries"}, {"backtrace": 39, "fragment": "C:\\opt\\openssl\\lib\\zlib.lib", "role": "libraries"}, {"backtrace": 39, "fragment": "ws2_32.lib", "role": "libraries"}, {"backtrace": 39, "fragment": "crypt32.lib", "role": "libraries"}, {"backtrace": 39, "fragment": "advapi32.lib", "role": "libraries"}, {"backtrace": 39, "fragment": "user32.lib", "role": "libraries"}, {"backtrace": 39, "fragment": "C:\\opt\\rttr\\lib\\rttr_core_d.lib", "role": "libraries"}, {"backtrace": 57, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Sqld.lib", "role": "libraries"}, {"backtrace": 59, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Widgetsd.lib", "role": "libraries"}, {"backtrace": 61, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Guid.lib", "role": "libraries"}, {"backtrace": 63, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Cored.lib", "role": "libraries"}, {"backtrace": 68, "fragment": "C:\\opt\\auboarcs\\lib\\aubo_sdkd.lib", "role": "libraries"}, {"backtrace": 68, "fragment": "C:\\opt\\auboarcs\\lib\\robot_proxyd.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\glog\\lib\\glogd.lib", "role": "libraries"}, {"backtrace": 68, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_system-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 68, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_filesystem-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 80, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_atomic-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 68, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_date_time-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 68, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_iostreams-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 68, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_serialization-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 68, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_thread-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 84, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_chrono-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 68, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_program_options-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 68, "fragment": "C:\\opt\\rttr\\lib\\rttr_core_d.lib", "role": "libraries"}, {"backtrace": 86, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Sqld.lib", "role": "libraries"}, {"backtrace": 88, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Widgetsd.lib", "role": "libraries"}, {"backtrace": 90, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Guid.lib", "role": "libraries"}, {"backtrace": 92, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Cored.lib", "role": "libraries"}, {"backtrace": 106, "fragment": "bcrypt.lib", "role": "libraries"}, {"backtrace": 106, "fragment": "synchronization.lib", "role": "libraries"}, {"backtrace": 107, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_atomic-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 111, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_chrono-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "toolhandeyecal", "nameOnDisk": "toolhandeyecal.exe", "paths": {"build": "tool/handeyecal", "source": "tool/handeyecal"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [3, 4]}], "sources": [{"backtrace": 3, "compileGroupIndex": 0, "path": "tool/handeyecal/src/CameraCalibrator.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "tool/handeyecal/src/HandEyeCalibrator.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "tool/handeyecal/src/handeyecal.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "tool/handeyecal/include/CameraCalibrator.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "tool/handeyecal/include/HandEyeCalibrator.h", "sourceGroupIndex": 1}], "type": "EXECUTABLE"}