{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "C:/Program Files/JetBrains/CLion 2025.1.3/bin/cmake/win/x64/bin/cmake.exe", "cpack": "C:/Program Files/JetBrains/CLion 2025.1.3/bin/cmake/win/x64/bin/cpack.exe", "ctest": "C:/Program Files/JetBrains/CLion 2025.1.3/bin/cmake/win/x64/bin/ctest.exe", "root": "C:/Program Files/JetBrains/CLion 2025.1.3/bin/cmake/win/x64/share/cmake-3.31"}, "version": {"isDirty": false, "major": 3, "minor": 31, "patch": 6, "string": "3.31.6", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-b5bd15cb05ed572e6efd.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, {"jsonFile": "cache-v2-4be60293dff304bb9fd0.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-f9949ac30731072b0440.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, {"jsonFile": "toolchains-v1-b4eb4112b14958d53140.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"cache-v2": {"jsonFile": "cache-v2-4be60293dff304bb9fd0.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-f9949ac30731072b0440.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, "codemodel-v2": {"jsonFile": "codemodel-v2-b5bd15cb05ed572e6efd.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, "toolchains-v1": {"jsonFile": "toolchains-v1-b4eb4112b14958d53140.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}}}