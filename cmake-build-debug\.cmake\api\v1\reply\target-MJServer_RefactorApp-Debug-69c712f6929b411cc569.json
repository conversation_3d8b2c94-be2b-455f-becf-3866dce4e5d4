{"artifacts": [{"path": "D:/newfuxios/install/x64-install/dev/bin/MJServer_RefactorApp.exe"}, {"path": "D:/newfuxios/install/x64-install/dev/bin/MJServer_RefactorApp.pdb"}], "backtrace": 3, "backtraceGraph": {"commands": ["add_executable", "include", "target_link_libraries", "add_unique_libraries", "set_property", "find_package", "boost_find_component", "ADD_DEFINITIONS", "include_directories"], "files": ["builder/cmake/executable.cmake", "MJServer_Refactor/App/CMakeLists.txt", "builder/cmake/common.cmake", "builder/cmake/library.cmake", "fuxicommon/CMakeLists.txt", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_filesystem-1.78.0/libboost_filesystem-variant-vc142-mt-gd-x64-1_78-static.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_filesystem-1.78.0/boost_filesystem-config.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/Boost-1.78.0/BoostConfig.cmake", "C:/Program Files/JetBrains/CLion 2025.1.3/bin/cmake/win/x64/share/cmake-3.31/Modules/FindBoost.cmake", "builder/cmake/add_boost.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_thread-1.78.0/boost_thread-config.cmake", "MJServer_Refactor/Library/CMakeLists.txt", "builder/cmake/add_libmodbus.cmake"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 3, "parent": 0}, {"file": 0, "parent": 1}, {"command": 0, "file": 0, "line": 26, "parent": 2}, {"command": 3, "file": 0, "line": 30, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 4}, {"command": 3, "file": 0, "line": 35, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 6}, {"command": 3, "file": 0, "line": 35, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 8}, {"command": 2, "file": 0, "line": 46, "parent": 2}, {"file": 4}, {"command": 1, "file": 4, "line": 3, "parent": 11}, {"file": 3, "parent": 12}, {"command": 3, "file": 3, "line": 78, "parent": 13}, {"command": 2, "file": 3, "line": 53, "parent": 14}, {"command": 1, "file": 3, "line": 1, "parent": 13}, {"file": 2, "parent": 16}, {"command": 1, "file": 2, "line": 81, "parent": 17}, {"file": 9, "parent": 18}, {"command": 5, "file": 9, "line": 33, "parent": 19}, {"file": 8, "parent": 20}, {"command": 5, "file": 8, "line": 610, "parent": 21}, {"file": 7, "parent": 22}, {"command": 6, "file": 7, "line": 262, "parent": 23}, {"command": 5, "file": 7, "line": 141, "parent": 24}, {"file": 6, "parent": 25}, {"command": 1, "file": 6, "line": 53, "parent": 26}, {"file": 5, "parent": 27}, {"command": 4, "file": 5, "line": 104, "parent": 28}, {"command": 4, "file": 6, "line": 103, "parent": 26}, {"command": 6, "file": 7, "line": 262, "parent": 23}, {"command": 5, "file": 7, "line": 141, "parent": 31}, {"file": 10, "parent": 32}, {"command": 4, "file": 10, "line": 103, "parent": 33}, {"command": 3, "file": 3, "line": 84, "parent": 13}, {"command": 2, "file": 3, "line": 53, "parent": 35}, {"command": 3, "file": 3, "line": 84, "parent": 13}, {"command": 2, "file": 3, "line": 53, "parent": 37}, {"command": 3, "file": 3, "line": 84, "parent": 13}, {"command": 2, "file": 3, "line": 53, "parent": 39}, {"command": 3, "file": 3, "line": 84, "parent": 13}, {"command": 2, "file": 3, "line": 53, "parent": 41}, {"file": 11}, {"command": 1, "file": 11, "line": 3, "parent": 43}, {"file": 3, "parent": 44}, {"command": 3, "file": 3, "line": 78, "parent": 45}, {"command": 2, "file": 3, "line": 53, "parent": 46}, {"command": 3, "file": 3, "line": 84, "parent": 45}, {"command": 2, "file": 3, "line": 53, "parent": 48}, {"command": 3, "file": 3, "line": 84, "parent": 45}, {"command": 2, "file": 3, "line": 53, "parent": 50}, {"command": 3, "file": 3, "line": 84, "parent": 45}, {"command": 2, "file": 3, "line": 53, "parent": 52}, {"command": 3, "file": 3, "line": 84, "parent": 45}, {"command": 2, "file": 3, "line": 53, "parent": 54}, {"command": 3, "file": 0, "line": 35, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 56}, {"command": 3, "file": 0, "line": 35, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 58}, {"command": 7, "file": 0, "line": 17, "parent": 2}, {"command": 7, "file": 0, "line": 13, "parent": 2}, {"command": 7, "file": 0, "line": 16, "parent": 2}, {"command": 7, "file": 0, "line": 14, "parent": 2}, {"command": 7, "file": 0, "line": 15, "parent": 2}, {"command": 1, "file": 0, "line": 1, "parent": 2}, {"file": 2, "parent": 65}, {"command": 8, "file": 2, "line": 54, "parent": 66}, {"command": 1, "file": 2, "line": 81, "parent": 66}, {"file": 12, "parent": 68}, {"command": 8, "file": 12, "line": 8, "parent": 69}, {"command": 8, "file": 0, "line": 34, "parent": 2}, {"command": 8, "file": 0, "line": 34, "parent": 2}, {"command": 8, "file": 0, "line": 34, "parent": 2}, {"command": 8, "file": 0, "line": 34, "parent": 2}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc /MDd /Zi /Ob0 /Od /RTC1 -std:c++20"}], "defines": [{"backtrace": 5, "define": "BOOST_ATOMIC_NO_LIB"}, {"backtrace": 5, "define": "BOOST_CHRONO_NO_LIB"}, {"backtrace": 5, "define": "BOOST_DATE_TIME_NO_LIB"}, {"backtrace": 5, "define": "BOOST_FILESYSTEM_NO_LIB"}, {"backtrace": 5, "define": "BOOST_IOSTREAMS_NO_LIB"}, {"backtrace": 5, "define": "BOOST_PROGRAM_OPTIONS_NO_LIB"}, {"backtrace": 5, "define": "BOOST_SERIALIZATION_NO_LIB"}, {"backtrace": 5, "define": "BOOST_SYSTEM_NO_LIB"}, {"backtrace": 5, "define": "BOOST_THREAD_NO_LIB"}, {"backtrace": 60, "define": "QT_CORE_LIB"}, {"backtrace": 61, "define": "QT_DISABLE_DEPRECATED_BEFORE=0"}, {"backtrace": 62, "define": "QT_GUI_LIB"}, {"backtrace": 63, "define": "QT_NO_DEBUG"}, {"backtrace": 5, "define": "QT_SQL_LIB"}, {"backtrace": 64, "define": "QT_WIDGETS_LIB"}, {"backtrace": 5, "define": "RTTR_DLL"}], "includes": [{"backtrace": 0, "path": "D:/newfuxios/cmake-build-debug/MJServer_Refactor/App/MJServer_RefactorApp_autogen/include"}, {"backtrace": 67, "path": "D:/newfuxios/MJServer_Refactor/App/include"}, {"backtrace": 70, "path": "C:/opt/libmodbus/include"}, {"backtrace": 5, "path": "D:/newfuxios/MJServer_Refactor/Library/include"}, {"backtrace": 5, "path": "C:/opt/jsoncpp/include"}, {"backtrace": 5, "path": "D:/newfuxios/fuxicommon/include"}, {"backtrace": 5, "path": "C:/opt/openssl/include"}, {"backtrace": 5, "path": "C:/opt/glog/include"}, {"backtrace": 71, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include"}, {"backtrace": 71, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore"}, {"backtrace": 71, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/./mkspecs/win32-msvc"}, {"backtrace": 72, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui"}, {"backtrace": 72, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE"}, {"backtrace": 73, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSql"}, {"backtrace": 74, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets"}, {"backtrace": 5, "isSystem": true, "path": "C:/opt/PCL/3rdParty/Boost/include/boost-1_78"}, {"backtrace": 5, "isSystem": true, "path": "C:/opt/rttr/include"}], "language": "CXX", "languageStandard": {"backtraces": [5, 5], "standard": "20"}, "sourceIndexes": [0, 1, 2]}], "dependencies": [{"backtrace": 5, "id": "fuxicommon::@58335e9a86196d0a97e7"}, {"backtrace": 5, "id": "MJServer_RefactorLibrary::@8670365571700e12b583"}, {"backtrace": 0, "id": "MJServer_RefactorApp_autogen::@f8e8ceb61b4f8c6d7034"}], "id": "MJServer_RefactorApp::@f8e8ceb61b4f8c6d7034", "link": {"commandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc /MDd /Zi /Ob0 /Od /RTC1", "role": "flags"}, {"fragment": "/machine:x64                                                                                                                                                                                                                                                                         /debug /INCREMENTAL /subsystem:console /machine:x64", "role": "flags"}, {"backtrace": 5, "fragment": "MJServer_Refactor\\Library\\MJServer_RefactorLibrary.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\libmodbus\\lib\\modbus.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "fuxicommon\\fuxicommon.lib", "role": "libraries"}, {"backtrace": 7, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Sqld.lib", "role": "libraries"}, {"backtrace": 9, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Widgetsd.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "ws2_32.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "kernel32.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "user32.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "gdi32.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "winspool.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "shell32.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "ole32.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "oleaut32.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "uuid.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "comdlg32.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "advapi32.lib", "role": "libraries"}, {"backtrace": 15, "fragment": "C:\\opt\\opencv\\build\\x64\\vc16\\lib\\opencv_world4110d.lib", "role": "libraries"}, {"backtrace": 15, "fragment": "C:\\opt\\opencv\\build\\x64\\vc16\\lib\\opencv_img_hash4110d.lib", "role": "libraries"}, {"backtrace": 15, "fragment": "C:\\opt\\xerces-c\\lib\\xerces-c_3D.lib", "role": "libraries"}, {"backtrace": 15, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_system-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 15, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_filesystem-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 29, "fragment": "bcrypt.lib", "role": "libraries"}, {"backtrace": 29, "fragment": "synchronization.lib", "role": "libraries"}, {"backtrace": 30, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_atomic-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 15, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_date_time-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 15, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_iostreams-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 15, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_serialization-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 15, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_thread-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 34, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_chrono-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 15, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_program_options-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 15, "fragment": "C:\\opt\\openssl\\lib\\libssl.lib", "role": "libraries"}, {"backtrace": 15, "fragment": "C:\\opt\\openssl\\lib\\libcrypto.lib", "role": "libraries"}, {"backtrace": 15, "fragment": "C:\\opt\\openssl\\lib\\jsoncpp.lib", "role": "libraries"}, {"backtrace": 15, "fragment": "C:\\opt\\openssl\\lib\\libcurl.lib", "role": "libraries"}, {"backtrace": 15, "fragment": "C:\\opt\\openssl\\lib\\zlib.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "ws2_32.lib", "role": "libraries"}, {"backtrace": 15, "fragment": "crypt32.lib", "role": "libraries"}, {"backtrace": 15, "fragment": "C:\\opt\\rttr\\lib\\rttr_core_d.lib", "role": "libraries"}, {"backtrace": 15, "fragment": "C:\\opt\\glog\\lib\\glogd.lib", "role": "libraries"}, {"backtrace": 36, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Sqld.lib", "role": "libraries"}, {"backtrace": 38, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Widgetsd.lib", "role": "libraries"}, {"backtrace": 40, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Guid.lib", "role": "libraries"}, {"backtrace": 42, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Cored.lib", "role": "libraries"}, {"backtrace": 47, "fragment": "C:\\opt\\jsoncpp\\lib\\jsoncpp.lib", "role": "libraries"}, {"backtrace": 47, "fragment": "C:\\opt\\jsoncpp\\lib\\jsoncpp_static.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\libmodbus\\lib\\modbus.lib", "role": "libraries"}, {"backtrace": 49, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Sqld.lib", "role": "libraries"}, {"backtrace": 51, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Widgetsd.lib", "role": "libraries"}, {"backtrace": 53, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Guid.lib", "role": "libraries"}, {"backtrace": 55, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Cored.lib", "role": "libraries"}, {"backtrace": 57, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Guid.lib", "role": "libraries"}, {"backtrace": 59, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Cored.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "user32.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "advapi32.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "MJServer_RefactorApp", "nameOnDisk": "MJServer_RefactorApp.exe", "paths": {"build": "MJServer_Refactor/App", "source": "MJServer_Refactor/App"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [3]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "cmake-build-debug/MJServer_Refactor/App/MJServer_RefactorApp_autogen/mocs_compilation.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "MJServer_Refactor/App/src/main.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "MJServer_Refactor/App/src/main_window.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "MJServer_Refactor/App/src/main_window.h", "sourceGroupIndex": 1}], "type": "EXECUTABLE"}