{"artifacts": [{"path": "D:/newfuxios/install/x64-install/dev/bin/toolhandeyecaluipath.exe"}, {"path": "D:/newfuxios/install/x64-install/dev/bin/toolhandeyecaluipath.pdb"}], "backtrace": 3, "backtraceGraph": {"commands": ["add_executable", "include", "target_link_libraries", "add_unique_libraries", "set_property", "find_package", "boost_find_component", "add_definitions", "ADD_DEFINITIONS", "include_directories"], "files": ["builder/cmake/executable.cmake", "tool/handeyecaluipath/CMakeLists.txt", "builder/cmake/common.cmake", "builder/cmake/library.cmake", "hardwaredriver/HikVisionCamera/CMakeLists.txt", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_filesystem-1.78.0/boost_filesystem-config.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/Boost-1.78.0/BoostConfig.cmake", "C:/Program Files/JetBrains/CLion 2025.1.3/bin/cmake/win/x64/share/cmake-3.31/Modules/FindBoost.cmake", "builder/cmake/add_boost.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_thread-1.78.0/boost_thread-config.cmake", "fuxicommon/CMakeLists.txt", "hardwaredriver/AuboArcsDriver/CMakeLists.txt", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_filesystem-1.78.0/libboost_filesystem-variant-vc142-mt-gd-x64-1_78-static.cmake", "C:/opt/robwork-21.12/robwork-21.12/cmake/RobWorkConfig.cmake", "builder/cmake/add_robwork.cmake", "builder/cmake/add_eigen.cmake", "builder/cmake/add_opencv.cmake", "builder/cmake/add_glog.cmake", "builder/cmake/add_aubo.cmake"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 3, "parent": 0}, {"file": 0, "parent": 1}, {"command": 0, "file": 0, "line": 26, "parent": 2}, {"command": 3, "file": 0, "line": 30, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 4}, {"command": 3, "file": 0, "line": 35, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 6}, {"command": 3, "file": 0, "line": 35, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 8}, {"command": 2, "file": 0, "line": 46, "parent": 2}, {"file": 4}, {"command": 1, "file": 4, "line": 3, "parent": 11}, {"file": 3, "parent": 12}, {"command": 3, "file": 3, "line": 78, "parent": 13}, {"command": 2, "file": 3, "line": 53, "parent": 14}, {"command": 1, "file": 3, "line": 1, "parent": 13}, {"file": 2, "parent": 16}, {"command": 1, "file": 2, "line": 81, "parent": 17}, {"file": 8, "parent": 18}, {"command": 5, "file": 8, "line": 33, "parent": 19}, {"file": 7, "parent": 20}, {"command": 5, "file": 7, "line": 610, "parent": 21}, {"file": 6, "parent": 22}, {"command": 6, "file": 6, "line": 262, "parent": 23}, {"command": 5, "file": 6, "line": 141, "parent": 24}, {"file": 5, "parent": 25}, {"command": 4, "file": 5, "line": 103, "parent": 26}, {"command": 6, "file": 6, "line": 262, "parent": 23}, {"command": 5, "file": 6, "line": 141, "parent": 28}, {"file": 9, "parent": 29}, {"command": 4, "file": 9, "line": 103, "parent": 30}, {"command": 3, "file": 3, "line": 84, "parent": 13}, {"command": 2, "file": 3, "line": 53, "parent": 32}, {"command": 3, "file": 3, "line": 84, "parent": 13}, {"command": 2, "file": 3, "line": 53, "parent": 34}, {"command": 3, "file": 3, "line": 84, "parent": 13}, {"command": 2, "file": 3, "line": 53, "parent": 36}, {"command": 3, "file": 3, "line": 84, "parent": 13}, {"command": 2, "file": 3, "line": 53, "parent": 38}, {"file": 10}, {"command": 1, "file": 10, "line": 3, "parent": 40}, {"file": 3, "parent": 41}, {"command": 3, "file": 3, "line": 78, "parent": 42}, {"command": 2, "file": 3, "line": 53, "parent": 43}, {"command": 1, "file": 3, "line": 1, "parent": 42}, {"file": 2, "parent": 45}, {"command": 1, "file": 2, "line": 81, "parent": 46}, {"file": 8, "parent": 47}, {"command": 5, "file": 8, "line": 33, "parent": 48}, {"file": 7, "parent": 49}, {"command": 5, "file": 7, "line": 610, "parent": 50}, {"file": 6, "parent": 51}, {"command": 6, "file": 6, "line": 262, "parent": 52}, {"command": 5, "file": 6, "line": 141, "parent": 53}, {"file": 5, "parent": 54}, {"command": 4, "file": 5, "line": 103, "parent": 55}, {"command": 6, "file": 6, "line": 262, "parent": 52}, {"command": 5, "file": 6, "line": 141, "parent": 57}, {"file": 9, "parent": 58}, {"command": 4, "file": 9, "line": 103, "parent": 59}, {"command": 3, "file": 3, "line": 84, "parent": 42}, {"command": 2, "file": 3, "line": 53, "parent": 61}, {"command": 3, "file": 3, "line": 84, "parent": 42}, {"command": 2, "file": 3, "line": 53, "parent": 63}, {"command": 3, "file": 3, "line": 84, "parent": 42}, {"command": 2, "file": 3, "line": 53, "parent": 65}, {"command": 3, "file": 3, "line": 84, "parent": 42}, {"command": 2, "file": 3, "line": 53, "parent": 67}, {"file": 11}, {"command": 1, "file": 11, "line": 3, "parent": 69}, {"file": 3, "parent": 70}, {"command": 3, "file": 3, "line": 78, "parent": 71}, {"command": 2, "file": 3, "line": 53, "parent": 72}, {"command": 1, "file": 3, "line": 1, "parent": 71}, {"file": 2, "parent": 74}, {"command": 1, "file": 2, "line": 81, "parent": 75}, {"file": 8, "parent": 76}, {"command": 5, "file": 8, "line": 33, "parent": 77}, {"file": 7, "parent": 78}, {"command": 5, "file": 7, "line": 610, "parent": 79}, {"file": 6, "parent": 80}, {"command": 6, "file": 6, "line": 262, "parent": 81}, {"command": 5, "file": 6, "line": 141, "parent": 82}, {"file": 5, "parent": 83}, {"command": 4, "file": 5, "line": 103, "parent": 84}, {"command": 6, "file": 6, "line": 262, "parent": 81}, {"command": 5, "file": 6, "line": 141, "parent": 86}, {"file": 9, "parent": 87}, {"command": 4, "file": 9, "line": 103, "parent": 88}, {"command": 3, "file": 3, "line": 84, "parent": 71}, {"command": 2, "file": 3, "line": 53, "parent": 90}, {"command": 3, "file": 3, "line": 84, "parent": 71}, {"command": 2, "file": 3, "line": 53, "parent": 92}, {"command": 3, "file": 3, "line": 84, "parent": 71}, {"command": 2, "file": 3, "line": 53, "parent": 94}, {"command": 3, "file": 3, "line": 84, "parent": 71}, {"command": 2, "file": 3, "line": 53, "parent": 96}, {"command": 1, "file": 0, "line": 1, "parent": 2}, {"file": 2, "parent": 98}, {"command": 1, "file": 2, "line": 81, "parent": 99}, {"file": 8, "parent": 100}, {"command": 5, "file": 8, "line": 33, "parent": 101}, {"file": 7, "parent": 102}, {"command": 5, "file": 7, "line": 610, "parent": 103}, {"file": 6, "parent": 104}, {"command": 6, "file": 6, "line": 262, "parent": 105}, {"command": 5, "file": 6, "line": 141, "parent": 106}, {"file": 5, "parent": 107}, {"command": 1, "file": 5, "line": 53, "parent": 108}, {"file": 12, "parent": 109}, {"command": 4, "file": 12, "line": 104, "parent": 110}, {"command": 4, "file": 5, "line": 103, "parent": 108}, {"command": 6, "file": 6, "line": 262, "parent": 105}, {"command": 5, "file": 6, "line": 141, "parent": 113}, {"file": 9, "parent": 114}, {"command": 4, "file": 9, "line": 103, "parent": 115}, {"command": 3, "file": 0, "line": 35, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 117}, {"command": 3, "file": 0, "line": 35, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 119}, {"command": 1, "file": 2, "line": 81, "parent": 99}, {"file": 14, "parent": 121}, {"command": 5, "file": 14, "line": 18, "parent": 122}, {"file": 13, "parent": 123}, {"command": 7, "file": 13, "line": 310, "parent": 124}, {"command": 8, "file": 0, "line": 17, "parent": 2}, {"command": 8, "file": 0, "line": 13, "parent": 2}, {"command": 8, "file": 0, "line": 16, "parent": 2}, {"command": 8, "file": 0, "line": 14, "parent": 2}, {"command": 8, "file": 0, "line": 15, "parent": 2}, {"command": 9, "file": 2, "line": 54, "parent": 99}, {"command": 1, "file": 2, "line": 81, "parent": 99}, {"file": 15, "parent": 132}, {"command": 9, "file": 15, "line": 30, "parent": 133}, {"command": 1, "file": 2, "line": 81, "parent": 99}, {"file": 16, "parent": 135}, {"command": 9, "file": 16, "line": 23, "parent": 136}, {"command": 1, "file": 2, "line": 81, "parent": 99}, {"file": 17, "parent": 138}, {"command": 9, "file": 17, "line": 16, "parent": 139}, {"command": 1, "file": 2, "line": 81, "parent": 99}, {"file": 18, "parent": 141}, {"command": 9, "file": 18, "line": 8, "parent": 142}, {"command": 9, "file": 14, "line": 21, "parent": 122}, {"command": 9, "file": 8, "line": 40, "parent": 101}, {"command": 9, "file": 0, "line": 34, "parent": 2}, {"command": 9, "file": 0, "line": 34, "parent": 2}, {"command": 9, "file": 0, "line": 34, "parent": 2}, {"command": 9, "file": 0, "line": 34, "parent": 2}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc -EHa -bigobj /MP -openmp /MDd /Zi /Ob0 /Od /RTC1 -std:c++20"}], "defines": [{"backtrace": 125, "define": "BIND_FORTRAN_LOWERCASE_UNDERSCORE"}, {"backtrace": 5, "define": "BOOST_ATOMIC_NO_LIB"}, {"backtrace": 5, "define": "BOOST_CHRONO_NO_LIB"}, {"backtrace": 5, "define": "BOOST_DATE_TIME_NO_LIB"}, {"backtrace": 5, "define": "BOOST_FILESYSTEM_NO_LIB"}, {"backtrace": 5, "define": "BOOST_IOSTREAMS_NO_LIB"}, {"backtrace": 5, "define": "BOOST_PROGRAM_OPTIONS_NO_LIB"}, {"backtrace": 5, "define": "BOOST_SERIALIZATION_NO_LIB"}, {"backtrace": 5, "define": "BOOST_SYSTEM_NO_LIB"}, {"backtrace": 5, "define": "BOOST_THREAD_NO_LIB"}, {"backtrace": 125, "define": "MSVC_AMD64"}, {"backtrace": 125, "define": "NOMINMAX"}, {"backtrace": 126, "define": "QT_CORE_LIB"}, {"backtrace": 127, "define": "QT_DISABLE_DEPRECATED_BEFORE=0"}, {"backtrace": 128, "define": "QT_GUI_LIB"}, {"backtrace": 129, "define": "QT_NO_DEBUG"}, {"backtrace": 5, "define": "QT_SQL_LIB"}, {"backtrace": 130, "define": "QT_WIDGETS_LIB"}, {"backtrace": 5, "define": "RTTR_DLL"}, {"backtrace": 125, "define": "WIN32_LEAN_AND_MEAN"}, {"backtrace": 125, "define": "_CRT_SECURE_NO_DEPRECATE"}, {"backtrace": 125, "define": "_CRT_SECURE_NO_WARNINGS"}, {"backtrace": 125, "define": "_SCL_SECURE_NO_WARNINGS"}, {"backtrace": 125, "define": "_WIN32_WINNT=0x0501"}], "includes": [{"backtrace": 0, "path": "D:/newfuxios/cmake-build-debug/tool/handeyecaluipath/toolhandeyecaluipath_autogen/include"}, {"backtrace": 131, "path": "D:/newfuxios/tool/handeyecaluipath/include"}, {"backtrace": 134, "path": "C:/opt/PCL/3rdParty/Eigen/eigen3"}, {"backtrace": 137, "path": "C:/opt/opencv/build/include"}, {"backtrace": 140, "path": "C:/opt/glog/include"}, {"backtrace": 143, "path": "C:/opt/aubo/include"}, {"backtrace": 144, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/eigen3"}, {"backtrace": 144, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../../include/robwork-21.12"}, {"backtrace": 144, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/rwyaobi"}, {"backtrace": 144, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/rwpqp"}, {"backtrace": 144, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/qhull/src"}, {"backtrace": 144, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/csgjs/src"}, {"backtrace": 144, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/zlib"}, {"backtrace": 144, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/fcl/fcl/include"}, {"backtrace": 144, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext/assimp/include"}, {"backtrace": 144, "path": "C:/opt/robwork-21.12/robwork-21.12/cmake/../ext"}, {"backtrace": 5, "path": "D:/newfuxios/hardwaredriver/HikVisionCamera/include"}, {"backtrace": 5, "path": "C:/opt/hikvision/include"}, {"backtrace": 5, "path": "D:/newfuxios/hardwaredriver/AuboArcsDriver/include"}, {"backtrace": 5, "path": "C:/opt/auboarcs/include"}, {"backtrace": 5, "path": "D:/newfuxios/fuxicommon/include"}, {"backtrace": 5, "path": "C:/opt/openssl/include"}, {"backtrace": 145, "isSystem": true, "path": "C:/opt/PCL/3rdParty/Boost/include/boost-1_78"}, {"backtrace": 146, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include"}, {"backtrace": 146, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore"}, {"backtrace": 146, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/./mkspecs/win32-msvc"}, {"backtrace": 147, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui"}, {"backtrace": 147, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE"}, {"backtrace": 148, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSql"}, {"backtrace": 149, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets"}, {"backtrace": 5, "isSystem": true, "path": "C:/opt/rttr/include"}], "language": "CXX", "languageStandard": {"backtraces": [5, 5], "standard": "20"}, "sourceIndexes": [0, 1, 3, 5, 7, 9, 11, 13, 15]}], "dependencies": [{"backtrace": 5, "id": "fuxicommon::@58335e9a86196d0a97e7"}, {"backtrace": 5, "id": "hardwaredriverHikVisionCamera::@bc252bb14595a0f09d26"}, {"backtrace": 5, "id": "hardwaredriverAuboArcsDriver::@fd5e493b37d2bab880d9"}, {"backtrace": 0, "id": "toolhandeyecaluipath_autogen::@f8ebbc87f7fac77328c8"}], "id": "toolhandeyecaluipath::@f8ebbc87f7fac77328c8", "link": {"commandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc -EHa -bigobj /MP -openmp /MDd /Zi /Ob0 /Od /RTC1", "role": "flags"}, {"fragment": "/machine:x64                                                                                                                                                                                                                                                                         /debug /INCREMENTAL /subsystem:console /machine:x64", "role": "flags"}, {"backtrace": 5, "fragment": "hardwaredriver\\HikVisionCamera\\hardwaredriverHikVisionCamera.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\yaobi.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\pqp.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\fcl.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\RobWork\\static\\sdurw_qhull.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\RobWork\\static\\sdurw_csgjs.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_assimp.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_unzip.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_algorithms.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_pathplanners.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_pathoptimization.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_simulation.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_opengl.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_assembly.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_task.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_calibration.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_csg.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_control.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_proximitystrategies.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_core.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_common.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_math.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "opengl32.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "glu32.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_filesystem-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_regex-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_regex-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_serialization-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_serialization-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_system-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_system-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_thread-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_thread-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_program_options-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_program_options-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_chrono-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_chrono-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_atomic-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_atomic-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_test_exec_monitor-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_test_exec_monitor-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_unit_test_framework-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_unit_test_framework-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\aubo\\lib\\libserviceinterface.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "hardwaredriver\\AuboArcsDriver\\hardwaredriverAuboArcsDriver.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\glog\\lib\\glogd.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\opencv\\build\\x64\\vc16\\lib\\opencv_world4110d.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\opencv\\build\\x64\\vc16\\lib\\opencv_img_hash4110d.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_system-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_filesystem-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_date_time-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_iostreams-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_serialization-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_thread-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_program_options-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "fuxicommon\\fuxicommon.lib", "role": "libraries"}, {"backtrace": 7, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Sqld.lib", "role": "libraries"}, {"backtrace": 9, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Widgetsd.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "ws2_32.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "kernel32.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "user32.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "gdi32.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "winspool.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "shell32.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "ole32.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "oleaut32.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "uuid.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "comdlg32.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "advapi32.lib", "role": "libraries"}, {"backtrace": 15, "fragment": "C:\\opt\\hikvision\\lib\\MvCameraControl.lib", "role": "libraries"}, {"backtrace": 15, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_system-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 15, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_filesystem-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 27, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_atomic-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 15, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_date_time-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 15, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_iostreams-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 15, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_serialization-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 15, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_thread-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 31, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_chrono-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 15, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_program_options-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 15, "fragment": "C:\\opt\\rttr\\lib\\rttr_core_d.lib", "role": "libraries"}, {"backtrace": 33, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Sqld.lib", "role": "libraries"}, {"backtrace": 35, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Widgetsd.lib", "role": "libraries"}, {"backtrace": 37, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Guid.lib", "role": "libraries"}, {"backtrace": 39, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Cored.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\yaobi.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\pqp.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\fcl.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\RobWork\\static\\sdurw_qhull.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\RobWork\\static\\sdurw_csgjs.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_assimp.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_unzip.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_algorithms.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_pathplanners.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_pathoptimization.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_simulation.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_opengl.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_assembly.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_task.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_calibration.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_csg.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_control.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_proximitystrategies.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_core.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_common.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\sdurw_math.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "opengl32.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "glu32.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_filesystem-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_regex-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_regex-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_serialization-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_serialization-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_system-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_system-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_thread-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_thread-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_program_options-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_program_options-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_chrono-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_chrono-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_atomic-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_atomic-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_test_exec_monitor-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_test_exec_monitor-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_unit_test_framework-vc142-mt-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\robwork-21.12\\lib\\libboost_unit_test_framework-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\opencv\\build\\x64\\vc16\\lib\\opencv_world4110d.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\opencv\\build\\x64\\vc16\\lib\\opencv_img_hash4110d.lib", "role": "libraries"}, {"backtrace": 44, "fragment": "C:\\opt\\xerces-c\\lib\\xerces-c_3D.lib", "role": "libraries"}, {"backtrace": 44, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_system-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 44, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_filesystem-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 56, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_atomic-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 44, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_date_time-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 44, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_iostreams-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 44, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_serialization-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 44, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_thread-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 60, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_chrono-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 44, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_program_options-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 44, "fragment": "C:\\opt\\openssl\\lib\\libssl.lib", "role": "libraries"}, {"backtrace": 44, "fragment": "C:\\opt\\openssl\\lib\\libcrypto.lib", "role": "libraries"}, {"backtrace": 44, "fragment": "C:\\opt\\openssl\\lib\\jsoncpp.lib", "role": "libraries"}, {"backtrace": 44, "fragment": "C:\\opt\\openssl\\lib\\libcurl.lib", "role": "libraries"}, {"backtrace": 44, "fragment": "C:\\opt\\openssl\\lib\\zlib.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "ws2_32.lib", "role": "libraries"}, {"backtrace": 44, "fragment": "crypt32.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "user32.lib", "role": "libraries"}, {"backtrace": 10, "fragment": "advapi32.lib", "role": "libraries"}, {"backtrace": 44, "fragment": "C:\\opt\\rttr\\lib\\rttr_core_d.lib", "role": "libraries"}, {"backtrace": 62, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Sqld.lib", "role": "libraries"}, {"backtrace": 64, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Widgetsd.lib", "role": "libraries"}, {"backtrace": 66, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Guid.lib", "role": "libraries"}, {"backtrace": 68, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Cored.lib", "role": "libraries"}, {"backtrace": 73, "fragment": "C:\\opt\\auboarcs\\lib\\aubo_sdkd.lib", "role": "libraries"}, {"backtrace": 73, "fragment": "C:\\opt\\auboarcs\\lib\\robot_proxyd.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\glog\\lib\\glogd.lib", "role": "libraries"}, {"backtrace": 73, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_system-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 73, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_filesystem-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 85, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_atomic-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 73, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_date_time-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 73, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_iostreams-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 73, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_serialization-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 73, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_thread-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 89, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_chrono-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 73, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_program_options-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 73, "fragment": "C:\\opt\\rttr\\lib\\rttr_core_d.lib", "role": "libraries"}, {"backtrace": 91, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Sqld.lib", "role": "libraries"}, {"backtrace": 93, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Widgetsd.lib", "role": "libraries"}, {"backtrace": 95, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Guid.lib", "role": "libraries"}, {"backtrace": 97, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Cored.lib", "role": "libraries"}, {"backtrace": 111, "fragment": "bcrypt.lib", "role": "libraries"}, {"backtrace": 111, "fragment": "synchronization.lib", "role": "libraries"}, {"backtrace": 112, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_atomic-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 116, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_chrono-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 118, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Guid.lib", "role": "libraries"}, {"backtrace": 120, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Cored.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "toolhandeyecaluipath", "nameOnDisk": "toolhandeyecaluipath.exe", "paths": {"build": "tool/handeyecaluipath", "source": "tool/handeyecaluipath"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 3, 5, 7, 9, 11, 13, 15]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [2, 4, 6, 8, 10, 12, 14]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "cmake-build-debug/tool/handeyecaluipath/toolhandeyecaluipath_autogen/mocs_compilation.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "tool/handeyecaluipath/src/CalibrationUI.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "tool/handeyecaluipath/src/CalibrationUI.h", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "tool/handeyecaluipath/src/KalmanFilter3D.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "tool/handeyecaluipath/src/KalmanFilter3D.h", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "tool/handeyecaluipath/src/MainWindow.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "tool/handeyecaluipath/src/MainWindow.h", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "tool/handeyecaluipath/src/PathManager.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "tool/handeyecaluipath/src/PathManager.h", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "tool/handeyecaluipath/src/TemplateManager.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "tool/handeyecaluipath/src/TemplateManager.h", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "tool/handeyecaluipath/src/TrackingManager.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "tool/handeyecaluipath/src/TrackingManager.h", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "tool/handeyecaluipath/src/TrackingUI.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "tool/handeyecaluipath/src/TrackingUI.h", "sourceGroupIndex": 1}, {"backtrace": 3, "compileGroupIndex": 0, "path": "tool/handeyecaluipath/src/main.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}