{"archive": {"commandFragments": [{"fragment": "/machine:x64", "role": "flags"}]}, "artifacts": [{"path": "Analysis_Robot/algorithms/pouringControl/Analysis_RobotalgorithmspouringControl.lib"}], "backtrace": 3, "backtraceGraph": {"commands": ["add_library", "include", "target_link_libraries", "add_unique_libraries", "include_directories"], "files": ["builder/cmake/library.cmake", "Analysis_Robot/algorithms/pouringControl/CMakeLists.txt", "builder/cmake/common.cmake", "builder/cmake/add_glog.cmake", "builder/cmake/add_boost.cmake"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 2, "parent": 0}, {"file": 0, "parent": 1}, {"command": 0, "file": 0, "line": 88, "parent": 2}, {"command": 3, "file": 0, "line": 90, "parent": 2}, {"command": 2, "file": 0, "line": 53, "parent": 4}, {"command": 1, "file": 0, "line": 1, "parent": 2}, {"file": 2, "parent": 6}, {"command": 4, "file": 2, "line": 54, "parent": 7}, {"command": 1, "file": 2, "line": 81, "parent": 7}, {"file": 3, "parent": 9}, {"command": 4, "file": 3, "line": 16, "parent": 10}, {"command": 1, "file": 2, "line": 81, "parent": 7}, {"file": 4, "parent": 12}, {"command": 4, "file": 4, "line": 40, "parent": 13}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc /MDd /Zi /Ob0 /Od /RTC1 -std:c++20"}], "defines": [{"backtrace": 5, "define": "BOOST_ATOMIC_NO_LIB"}, {"backtrace": 5, "define": "BOOST_CHRONO_NO_LIB"}, {"backtrace": 5, "define": "BOOST_DATE_TIME_NO_LIB"}, {"backtrace": 5, "define": "BOOST_FILESYSTEM_NO_LIB"}, {"backtrace": 5, "define": "BOOST_IOSTREAMS_NO_LIB"}, {"backtrace": 5, "define": "BOOST_PROGRAM_OPTIONS_NO_LIB"}, {"backtrace": 5, "define": "BOOST_SERIALIZATION_NO_LIB"}, {"backtrace": 5, "define": "BOOST_SYSTEM_NO_LIB"}, {"backtrace": 5, "define": "BOOST_THREAD_NO_LIB"}, {"backtrace": 5, "define": "QT_CORE_LIB"}, {"backtrace": 5, "define": "QT_GUI_LIB"}, {"backtrace": 5, "define": "QT_SQL_LIB"}, {"backtrace": 5, "define": "QT_WIDGETS_LIB"}, {"backtrace": 5, "define": "RTTR_DLL"}], "includes": [{"backtrace": 8, "path": "D:/newfuxios/Analysis_Robot/algorithms/pouringControl/include"}, {"backtrace": 11, "path": "C:/opt/glog/include"}, {"backtrace": 5, "path": "D:/newfuxios/Analysis_Robot/algorithms/coordinateTransform/include"}, {"backtrace": 5, "path": "D:/newfuxios/fuxicommon/include"}, {"backtrace": 5, "path": "C:/opt/openssl/include"}, {"backtrace": 5, "path": "D:/newfuxios/Analysis_Robot/algorithms/tcpPositionMaintain/include"}, {"backtrace": 5, "path": "D:/newfuxios/Analysis_Robot/drivers/aixsDriver/include"}, {"backtrace": 5, "path": "D:/newfuxios/Analysis_Robot/drivers/balanceDriver/include"}, {"backtrace": 5, "path": "C:/opt/libmodbus/include"}, {"backtrace": 14, "isSystem": true, "path": "C:/opt/PCL/3rdParty/Boost/include/boost-1_78"}, {"backtrace": 5, "isSystem": true, "path": "C:/opt/rttr/include"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/./mkspecs/win32-msvc"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSql"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets"}], "language": "CXX", "languageStandard": {"backtraces": [5, 5], "standard": "20"}, "sourceIndexes": [0]}], "dependencies": [{"backtrace": 5, "id": "Analysis_RobotdriversaixsDriver::@19f706e88e1d43a9565c"}, {"backtrace": 5, "id": "Analysis_RobotdriversbalanceDriver::@92995a2f85961e8f5b16"}, {"backtrace": 5, "id": "Analysis_RobotalgorithmscoordinateTransform::@e0567cd60ef58755dd5b"}, {"backtrace": 5, "id": "Analysis_RobotalgorithmstcpPositionMaintain::@96a57770f6c6f4e493b3"}, {"backtrace": 5, "id": "fuxicommon::@58335e9a86196d0a97e7"}], "id": "Analysis_RobotalgorithmspouringControl::@07001b74ee4af3db8a6e", "name": "Analysis_RobotalgorithmspouringControl", "nameOnDisk": "Analysis_RobotalgorithmspouringControl.lib", "paths": {"build": "Analysis_Robot/algorithms/pouringControl", "source": "Analysis_Robot/algorithms/pouringControl"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1]}], "sources": [{"backtrace": 3, "compileGroupIndex": 0, "path": "Analysis_Robot/algorithms/pouringControl/src/PouringControllerAlgorithms.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "Analysis_Robot/algorithms/pouringControl/include/PouringControllerAlgorithms.h", "sourceGroupIndex": 1}], "type": "STATIC_LIBRARY"}