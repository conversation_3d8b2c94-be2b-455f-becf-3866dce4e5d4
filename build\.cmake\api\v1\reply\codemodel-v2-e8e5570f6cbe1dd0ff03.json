{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79], "hasInstallRule": true, "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [1, 130]}, {"build": "fuxicommon", "hasInstallRule": true, "jsonFile": "directory-fuxicommon-Debug-45b01aad2fc6492274cb.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 1, "source": "fuxicommon", "targetIndexes": [5, 131]}, {"build": "Analysis_Robot/algorithms/pouringControl", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.algorithms.pouringControl-Debug-b6a4a096167825bdc477.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 2, "source": "Analysis_Robot/algorithms/pouringControl", "targetIndexes": [8, 82]}, {"build": "Analysis_Robot/drivers/aixsDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.aixsDriver-Debug-acad4faf4f883a05b16c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 3, "source": "Analysis_Robot/drivers/aixsDriver", "targetIndexes": [0, 84]}, {"build": "Analysis_Robot/drivers/plcDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.plcDriver-Debug-1785bcd9213080dbd037.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 4, "source": "Analysis_Robot/drivers/plcDriver", "targetIndexes": [14, 88]}, {"build": "Analysis_Robot/drivers/balanceDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.balanceDriver-Debug-bd88b671d17006b62362.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 5, "source": "Analysis_Robot/drivers/balanceDriver", "targetIndexes": [17, 85]}, {"build": "Analysis_Robot/drivers/heatingMagneticStirrerDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.heatingMagneticStirrerDriver-Debug-58f3ac9f2a5c9e15f72a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 6, "source": "Analysis_Robot/drivers/heatingMagneticStirrerDriver", "targetIndexes": [20, 86]}, {"build": "Analysis_Robot/drivers/moistureAnalyzerDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.moistureAnalyzerDriver-Debug-013e15aafee981d8281c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 7, "source": "Analysis_Robot/drivers/moistureAnalyzerDriver", "targetIndexes": [23, 87]}, {"build": "Analysis_Robot/drivers/robotDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.robotDriver-Debug-2c83fb91a80b187dc6d1.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 8, "source": "Analysis_Robot/drivers/robotDriver", "targetIndexes": [26, 90]}, {"build": "Analysis_Robot/drivers/restInterfaceDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.restInterfaceDriver-Debug-32537dba61e43ea53bc5.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 9, "source": "Analysis_Robot/drivers/restInterfaceDriver", "targetIndexes": [29, 89]}, {"build": "Analysis_Robot/App", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.App-Debug-e2ede17c1187489a25e9.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 10, "source": "Analysis_Robot/App", "targetIndexes": [32, 80]}, {"build": "Analysis_Robot/algorithms/coordinateTransform", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.algorithms.coordinateTransform-Debug-0dc862cc663c92448147.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 11, "source": "Analysis_Robot/algorithms/coordinateTransform", "targetIndexes": [35, 81]}, {"build": "Analysis_Robot/algorithms/tcpPositionMaintain", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.algorithms.tcpPositionMaintain-Debug-14eb9bf209fc848d31d1.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 12, "source": "Analysis_Robot/algorithms/tcpPositionMaintain", "targetIndexes": [38, 83]}, {"build": "Analysis_Robot/test/heaterApiTest", "jsonFile": "directory-Analysis_Robot.test.heaterApiTest-Debug-b999f5688c4067f4e68d.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 13, "source": "Analysis_Robot/test/heaterApiTest", "targetIndexes": [41, 91]}, {"build": "Analysis_Robot/test/heatingMagneticStirrerDriver", "jsonFile": "directory-Analysis_Robot.test.heatingMagneticStirrerDriver-Debug-e5cbd850de4545ce6cdf.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 14, "source": "Analysis_Robot/test/heatingMagneticStirrerDriver", "targetIndexes": [44, 92]}, {"build": "MJServer/APP", "hasInstallRule": true, "jsonFile": "directory-MJServer.APP-Debug-9a0005353ac4d44a8cb8.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 15, "source": "MJServer/APP", "targetIndexes": [47, 93]}, {"build": "MJServer_Refactor/Library", "hasInstallRule": true, "jsonFile": "directory-MJServer_Refactor.Library-Debug-9ec6a9fe118a63a425b3.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 16, "source": "MJServer_Refactor/Library", "targetIndexes": [50, 95]}, {"build": "MJServer_Refactor/App", "jsonFile": "directory-MJServer_Refactor.App-Debug-25c2817210280de79843.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 17, "source": "MJServer_Refactor/App", "targetIndexes": [53, 94]}, {"build": "MJServer_Refactor/Test/phase1_test", "jsonFile": "directory-MJServer_Refactor.Test.phase1_test-Debug-0a76c9c92d103a235c64.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 18, "source": "MJServer_Refactor/Test/phase1_test", "targetIndexes": [56, 96]}, {"build": "MJServer_Refactor/Test/simple_abb_client", "jsonFile": "directory-MJServer_Refactor.Test.simple_abb_client-Debug-d55604710c3a3fb8f0bb.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 19, "source": "MJServer_Refactor/Test/simple_abb_client", "targetIndexes": [59, 97]}, {"build": "MJServer_Refactor/Test/simple_feeder_client", "jsonFile": "directory-MJServer_Refactor.Test.simple_feeder_client-Debug-615291d1f0d9de4ac87c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 20, "source": "MJServer_Refactor/Test/simple_feeder_client", "targetIndexes": [62, 98]}, {"build": "RoboticLaserMarking/AbbDriver", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.AbbDriver-Debug-6a2fe08206f932672311.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 21, "source": "RoboticLaserMarking/AbbDriver", "targetIndexes": [65, 99]}, {"build": "RoboticLaserMarking/LicenseGenerator", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.LicenseGenerator-Debug-5d8bc3a4c1c58bab6f7a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 22, "source": "RoboticLaserMarking/LicenseGenerator", "targetIndexes": [68, 100]}, {"build": "RoboticLaserMarking/RFIDDriver", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.RFIDDriver-Debug-ce034b8fd7255dd94677.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 23, "source": "RoboticLaserMarking/RFIDDriver", "targetIndexes": [71, 101]}, {"build": "RoboticLaserMarking/laserDriver", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.laserDriver-Debug-50ac02b6f964d67969b6.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 24, "source": "RoboticLaserMarking/laserDriver", "targetIndexes": [74, 108]}, {"build": "RoboticLaserMarking/Test/abbsocket", "jsonFile": "directory-RoboticLaserMarking.Test.abbsocket-Debug-c54e19bdb9ae31bded65.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 25, "source": "RoboticLaserMarking/Test/abbsocket", "targetIndexes": [77, 102]}, {"build": "RoboticLaserMarking/Test/laser", "jsonFile": "directory-RoboticLaserMarking.Test.laser-Debug-fda96428a10838b20da0.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 26, "source": "RoboticLaserMarking/Test/laser", "targetIndexes": [6, 103]}, {"build": "RoboticLaserMarking/Test/laserUI", "jsonFile": "directory-RoboticLaserMarking.Test.laserUI-Debug-906cc9e5b37f734dfdd8.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 27, "source": "RoboticLaserMarking/Test/laserUI", "targetIndexes": [10, 104]}, {"build": "RoboticLaserMarking/Test/rfiddriver", "jsonFile": "directory-RoboticLaserMarking.Test.rfiddriver-Debug-f51044958bce4e484373.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 28, "source": "RoboticLaserMarking/Test/rfiddriver", "targetIndexes": [19, 105]}, {"build": "RoboticLaserMarking/Test/rfidserver", "jsonFile": "directory-RoboticLaserMarking.Test.rfidserver-Debug-7e022394fa2631804126.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 29, "source": "RoboticLaserMarking/Test/rfidserver", "targetIndexes": [28, 106]}, {"build": "RoboticLaserMarking/UI", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.UI-Debug-a0a826d45ad2ea8615b1.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 30, "source": "RoboticLaserMarking/UI", "targetIndexes": [37, 107]}, {"build": "RoboticLaserMarking/laserDriverSim", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.laserDriverSim-Debug-9fa894fbfa57d970660d.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 31, "source": "RoboticLaserMarking/laserDriverSim", "targetIndexes": [46, 109]}, {"build": "fuxicore", "hasInstallRule": true, "jsonFile": "directory-fuxicore-Debug-6019081bb93cc97e5bd3.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 32, "source": "fuxicore", "targetIndexes": [55, 132]}, {"build": "hardwaredriver/abbRobotDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.abbRobotDriver-Debug-6dd56340ab873b7947eb.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 33, "source": "hardwaredriver/abbRobotDriver", "targetIndexes": [64, 141]}, {"build": "Test/test_abb_socket", "jsonFile": "directory-Test.test_abb_socket-Debug-b83d4bb5753115673b1e.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 34, "source": "Test/test_abb_socket", "targetIndexes": [73, 110]}, {"build": "Test/test_config_manager", "jsonFile": "directory-Test.test_config_manager-Debug-b1e58fcc0034795c6ae3.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 35, "source": "Test/test_config_manager", "targetIndexes": [7, 111]}, {"build": "Test/test_csv", "jsonFile": "directory-Test.test_csv-Debug-46cd2d2264edf5d7592e.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 36, "source": "Test/test_csv", "targetIndexes": [34, 112]}, {"build": "Test/test_event_listener", "jsonFile": "directory-Test.test_event_listener-Debug-87efd742cb5ddc812bd7.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 37, "source": "Test/test_event_listener", "targetIndexes": [61, 113]}, {"build": "Test/test_executor", "jsonFile": "directory-Test.test_executor-Debug-a98e8731e17c62507f58.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 38, "source": "Test/test_executor", "targetIndexes": [25, 114]}, {"build": "Test/test_executor_context", "jsonFile": "directory-Test.test_executor_context-Debug-7ab477d5e8562815cbb7.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 39, "source": "Test/test_executor_context", "targetIndexes": [79, 115]}, {"build": "Test/test_fa2204n_balance", "jsonFile": "directory-Test.test_fa2204n_balance-Debug-7560d87111b5ff21f33f.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 40, "source": "Test/test_fa2204n_balance", "targetIndexes": [52, 116]}, {"build": "Test/test_fa2204n_balance_basic", "jsonFile": "directory-Test.test_fa2204n_balance_basic-Debug-c5e19bbf14befd24dc6e.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 41, "source": "Test/test_fa2204n_balance_basic", "targetIndexes": [70, 117]}, {"build": "Test/test_fileutil", "jsonFile": "directory-Test.test_fileutil-Debug-6a4234fe4fd9c32b60c0.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 42, "source": "Test/test_fileutil", "targetIndexes": [43, 118]}, {"build": "Test/test_json", "jsonFile": "directory-Test.test_json-Debug-70316fee5b76b6255c9c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 43, "source": "Test/test_json", "targetIndexes": [16, 119]}, {"build": "Test/test_license_manager", "jsonFile": "directory-Test.test_license_manager-Debug-adf854e580c4cdcb7fc5.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 44, "source": "Test/test_license_manager", "targetIndexes": [76, 120]}, {"build": "Test/test_license_ui", "hasInstallRule": true, "jsonFile": "directory-Test.test_license_ui-Debug-17e8c4b69d69a26aaf2a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 45, "source": "Test/test_license_ui", "targetIndexes": [67, 121]}, {"build": "Test/test_micro_dosing", "jsonFile": "directory-Test.test_micro_dosing-Debug-5e830cdb711d4e75b3ab.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 46, "source": "Test/test_micro_dosing", "targetIndexes": [58, 149]}, {"build": "Test/test_network", "jsonFile": "directory-Test.test_network-Debug-802d04b39615713bdaa5.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 47, "source": "Test/test_network", "targetIndexes": [49, 122]}, {"build": "Test/test_serial", "jsonFile": "directory-Test.test_serial-Debug-1539ef84150cf55d007b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 48, "source": "Test/test_serial", "targetIndexes": [40, 123]}, {"build": "Test/test_service_container", "jsonFile": "directory-Test.test_service_container-Debug-3c607165866062790453.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 49, "source": "Test/test_service_container", "targetIndexes": [31, 124]}, {"build": "Test/test_socket", "jsonFile": "directory-Test.test_socket-Debug-7646e8df0e04f7d933fc.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 50, "source": "Test/test_socket", "targetIndexes": [11, 125]}, {"build": "Test/test_sqlite", "jsonFile": "directory-Test.test_sqlite-Debug-a3f7e251b42c8d6e85bd.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 51, "source": "Test/test_sqlite", "targetIndexes": [13, 126]}, {"build": "Test/test_taskflow", "jsonFile": "directory-Test.test_taskflow-Debug-338290a1d2d2c191692b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 52, "source": "Test/test_taskflow", "targetIndexes": [4, 127]}, {"build": "Test/test_twoaixsrobot", "jsonFile": "directory-Test.test_twoaixsrobot-Debug-829343d8fb5c82be5c03.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 53, "source": "Test/test_twoaixsrobot", "targetIndexes": [78, 128]}, {"build": "Test/test_xml", "jsonFile": "directory-Test.test_xml-Debug-91dd4b4ea7db1ac593a4.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 54, "source": "Test/test_xml", "targetIndexes": [75, 129]}, {"build": "hardwaredriver/AuboArcsDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.AuboArcsDriver-Debug-c8464a5a4e222d8a4324.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 55, "source": "hardwaredriver/AuboArcsDriver", "targetIndexes": [72, 133]}, {"build": "hardwaredriver/AuboDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.AuboDriver-Debug-f6ef18b990c62843287d.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 56, "source": "hardwaredriver/AuboDriver", "targetIndexes": [69, 134]}, {"build": "hardwaredriver/ElectricGripperDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.ElectricGripperDriver-Debug-9050ee0aa574d02a13c1.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 57, "source": "hardwaredriver/ElectricGripperDriver", "targetIndexes": [66, 135]}, {"build": "hardwaredriver/HikVisionCamera", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.HikVisionCamera-Debug-2f15954957700dc82692.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 58, "source": "hardwaredriver/HikVisionCamera", "targetIndexes": [63, 136]}, {"build": "hardwaredriver/LabelPrinter", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.LabelPrinter-Debug-a98e344906e45e035c7a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 59, "source": "hardwaredriver/LabelPrinter", "targetIndexes": [60, 137]}, {"build": "hardwaredriver/MettlerBalance", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.MettlerBalance-Debug-f65a38351f7de3288dc7.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 60, "source": "hardwaredriver/MettlerBalance", "targetIndexes": [57, 138]}, {"build": "hardwaredriver/OpcDa", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.OpcDa-Debug-5c7bc29dbda15a9b8ae4.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 61, "source": "hardwaredriver/OpcDa", "targetIndexes": [54, 139]}, {"build": "hardwaredriver/OpcUa", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.OpcUa-Debug-36fae1fe277182987806.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 62, "source": "hardwaredriver/OpcUa", "targetIndexes": [51, 140]}, {"build": "hardwaredriver/socket", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.socket-Debug-b084aeaa98c55ca54242.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 63, "source": "hardwaredriver/socket", "targetIndexes": [48, 147]}, {"build": "hardwaredriver/agilerobotDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.agilerobotDriver-Debug-be0c33e6eb56c2575bd9.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 64, "source": "hardwaredriver/agilerobotDriver", "targetIndexes": [45, 142]}, {"build": "hardwaredriver/fairinoDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.fairinoDriver-Debug-b638d5fdff8da064311b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 65, "source": "hardwaredriver/fairinoDriver", "targetIndexes": [3, 143]}, {"build": "hardwaredriver/junduoHandDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.junduoHandDriver-Debug-e7024192621c226dc16f.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 66, "source": "hardwaredriver/junduoHandDriver", "targetIndexes": [2, 144]}, {"build": "hardwaredriver/modbus", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.modbus-Debug-1f7cf0a90d4506a41f5a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 67, "source": "hardwaredriver/modbus", "targetIndexes": [9, 145]}, {"build": "hardwaredriver/serial", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.serial-Debug-40c48bd4e0de0af8828e.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 68, "source": "hardwaredriver/serial", "targetIndexes": [12, 146]}, {"build": "hardwaredriver/usbcamera", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.usbcamera-Debug-968bee8cafdf7e5f9ccf.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 69, "source": "hardwaredriver/usbcamera", "targetIndexes": [30, 148]}, {"build": "tool/calbuild", "jsonFile": "directory-tool.calbuild-Debug-9edb4e157f9b6e54846b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 70, "source": "tool/calbuild", "targetIndexes": [15, 150]}, {"build": "tool/cameraCalibrator", "hasInstallRule": true, "jsonFile": "directory-tool.cameraCalibrator-Debug-a861825f388659f4f715.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 71, "source": "tool/cameraCalibrator", "targetIndexes": [18, 152]}, {"build": "tool/caltest", "hasInstallRule": true, "jsonFile": "directory-tool.caltest-Debug-63fb02a9aaef7a0c834c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 72, "source": "tool/caltest", "targetIndexes": [21, 151]}, {"build": "tool/communication", "hasInstallRule": true, "jsonFile": "directory-tool.communication-Debug-324152a549fff44395ce.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 73, "source": "tool/communication", "targetIndexes": [22, 153]}, {"build": "tool/handeyecal", "hasInstallRule": true, "jsonFile": "directory-tool.handeyecal-Debug-77ea94e986eab795f767.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 74, "source": "tool/handeyecal", "targetIndexes": [24, 154]}, {"build": "tool/handeyecaltest", "jsonFile": "directory-tool.handeyecaltest-Debug-f9f22629594fc4ab2dbc.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 75, "source": "tool/handeyecaltest", "targetIndexes": [27, 155]}, {"build": "tool/handeyecalui/handeyecalui", "jsonFile": "directory-tool.handeyecalui.handeyecalui-Debug-1fafdc8136eaae57e66b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 76, "source": "tool/handeyecalui/handeyecalui", "targetIndexes": [33, 156]}, {"build": "tool/handeyecaluipath", "jsonFile": "directory-tool.handeyecaluipath-Debug-e479629856c728efc292.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 77, "source": "tool/handeyecaluipath", "targetIndexes": [36, 157]}, {"build": "tool/handeyecaluipathAuto", "jsonFile": "directory-tool.handeyecaluipathAuto-Debug-0a177976965c3cc59f22.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 78, "source": "tool/handeyecaluipathAuto", "targetIndexes": [39, 158]}, {"build": "tool/verify_calibration", "jsonFile": "directory-tool.verify_calibration-Debug-2d3946f9054860a13e8b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 79, "source": "tool/verify_calibration", "targetIndexes": [42, 159]}], "name": "Debug", "projects": [{"childIndexes": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79], "directoryIndexes": [0], "name": "Project", "targetIndexes": [1, 130]}, {"directoryIndexes": [1], "name": "fuxicommon", "parentIndex": 0, "targetIndexes": [5, 131]}, {"directoryIndexes": [2], "name": "Analysis_RobotalgorithmspouringControl", "parentIndex": 0, "targetIndexes": [8, 82]}, {"directoryIndexes": [3], "name": "Analysis_RobotdriversaixsDriver", "parentIndex": 0, "targetIndexes": [0, 84]}, {"directoryIndexes": [4], "name": "Analysis_RobotdriversplcDriver", "parentIndex": 0, "targetIndexes": [14, 88]}, {"directoryIndexes": [5], "name": "Analysis_RobotdriversbalanceDriver", "parentIndex": 0, "targetIndexes": [17, 85]}, {"directoryIndexes": [6], "name": "Analysis_RobotdriversheatingMagneticStirrerDriver", "parentIndex": 0, "targetIndexes": [20, 86]}, {"directoryIndexes": [7], "name": "Analysis_RobotdriversmoistureAnalyzerDriver", "parentIndex": 0, "targetIndexes": [23, 87]}, {"directoryIndexes": [8], "name": "Analysis_RobotdriversrobotDriver", "parentIndex": 0, "targetIndexes": [26, 90]}, {"directoryIndexes": [9], "name": "Analysis_RobotdriversrestInterfaceDriver", "parentIndex": 0, "targetIndexes": [29, 89]}, {"directoryIndexes": [10], "name": "Analysis_RobotApp", "parentIndex": 0, "targetIndexes": [32, 80]}, {"directoryIndexes": [11], "name": "Analysis_RobotalgorithmscoordinateTransform", "parentIndex": 0, "targetIndexes": [35, 81]}, {"directoryIndexes": [12], "name": "Analysis_RobotalgorithmstcpPositionMaintain", "parentIndex": 0, "targetIndexes": [38, 83]}, {"directoryIndexes": [13], "name": "Analysis_RobottestheaterApiTest", "parentIndex": 0, "targetIndexes": [41, 91]}, {"directoryIndexes": [14], "name": "Analysis_RobottestheatingMagneticStirrerDriver", "parentIndex": 0, "targetIndexes": [44, 92]}, {"directoryIndexes": [15], "name": "MJServerAPP", "parentIndex": 0, "targetIndexes": [47, 93]}, {"directoryIndexes": [16], "name": "MJServer_RefactorLibrary", "parentIndex": 0, "targetIndexes": [50, 95]}, {"directoryIndexes": [17], "name": "MJServer_RefactorApp", "parentIndex": 0, "targetIndexes": [53, 94]}, {"directoryIndexes": [18], "name": "MJServer_RefactorTestphase1_test", "parentIndex": 0, "targetIndexes": [56, 96]}, {"directoryIndexes": [19], "name": "MJServer_RefactorTestsimple_abb_client", "parentIndex": 0, "targetIndexes": [59, 97]}, {"directoryIndexes": [20], "name": "MJServer_RefactorTestsimple_feeder_client", "parentIndex": 0, "targetIndexes": [62, 98]}, {"directoryIndexes": [21], "name": "RoboticLaserMarkingAbbDriver", "parentIndex": 0, "targetIndexes": [65, 99]}, {"directoryIndexes": [22], "name": "RoboticLaserMarkingLicenseGenerator", "parentIndex": 0, "targetIndexes": [68, 100]}, {"directoryIndexes": [23], "name": "RoboticLaserMarkingRFIDDriver", "parentIndex": 0, "targetIndexes": [71, 101]}, {"directoryIndexes": [24], "name": "RoboticLaserMarkinglaserDriver", "parentIndex": 0, "targetIndexes": [74, 108]}, {"directoryIndexes": [25], "name": "RoboticLaserMarkingTestabbsocket", "parentIndex": 0, "targetIndexes": [77, 102]}, {"directoryIndexes": [26], "name": "RoboticLaserMarkingTestlaser", "parentIndex": 0, "targetIndexes": [6, 103]}, {"directoryIndexes": [27], "name": "RoboticLaserMarkingTestlaserUI", "parentIndex": 0, "targetIndexes": [10, 104]}, {"directoryIndexes": [28], "name": "RoboticLaserMarkingTestrfiddriver", "parentIndex": 0, "targetIndexes": [19, 105]}, {"directoryIndexes": [29], "name": "RoboticLaserMarkingTestrfidserver", "parentIndex": 0, "targetIndexes": [28, 106]}, {"directoryIndexes": [30], "name": "RoboticLaserMarkingUI", "parentIndex": 0, "targetIndexes": [37, 107]}, {"directoryIndexes": [31], "name": "RoboticLaserMarkinglaserDriverSim", "parentIndex": 0, "targetIndexes": [46, 109]}, {"directoryIndexes": [32], "name": "fuxicore", "parentIndex": 0, "targetIndexes": [55, 132]}, {"directoryIndexes": [33], "name": "hardwaredriverabbRobotDriver", "parentIndex": 0, "targetIndexes": [64, 141]}, {"directoryIndexes": [34], "name": "Testtest_abb_socket", "parentIndex": 0, "targetIndexes": [73, 110]}, {"directoryIndexes": [35], "name": "Testtest_config_manager", "parentIndex": 0, "targetIndexes": [7, 111]}, {"directoryIndexes": [36], "name": "Testtest_csv", "parentIndex": 0, "targetIndexes": [34, 112]}, {"directoryIndexes": [37], "name": "Testtest_event_listener", "parentIndex": 0, "targetIndexes": [61, 113]}, {"directoryIndexes": [38], "name": "Testtest_executor", "parentIndex": 0, "targetIndexes": [25, 114]}, {"directoryIndexes": [39], "name": "Testtest_executor_context", "parentIndex": 0, "targetIndexes": [79, 115]}, {"directoryIndexes": [40], "name": "Testtest_fa2204n_balance", "parentIndex": 0, "targetIndexes": [52, 116]}, {"directoryIndexes": [41], "name": "Testtest_fa2204n_balance_basic", "parentIndex": 0, "targetIndexes": [70, 117]}, {"directoryIndexes": [42], "name": "Testtest_fileutil", "parentIndex": 0, "targetIndexes": [43, 118]}, {"directoryIndexes": [43], "name": "Testtest_json", "parentIndex": 0, "targetIndexes": [16, 119]}, {"directoryIndexes": [44], "name": "Testtest_license_manager", "parentIndex": 0, "targetIndexes": [76, 120]}, {"directoryIndexes": [45], "name": "Testtest_license_ui", "parentIndex": 0, "targetIndexes": [67, 121]}, {"directoryIndexes": [46], "name": "test_micro_dosing", "parentIndex": 0, "targetIndexes": [58, 149]}, {"directoryIndexes": [47], "name": "Testtest_network", "parentIndex": 0, "targetIndexes": [49, 122]}, {"directoryIndexes": [48], "name": "Testtest_serial", "parentIndex": 0, "targetIndexes": [40, 123]}, {"directoryIndexes": [49], "name": "Testtest_service_container", "parentIndex": 0, "targetIndexes": [31, 124]}, {"directoryIndexes": [50], "name": "Testtest_socket", "parentIndex": 0, "targetIndexes": [11, 125]}, {"directoryIndexes": [51], "name": "Testtest_sqlite", "parentIndex": 0, "targetIndexes": [13, 126]}, {"directoryIndexes": [52], "name": "Testtest_taskflow", "parentIndex": 0, "targetIndexes": [4, 127]}, {"directoryIndexes": [53], "name": "Testtest_twoaixsrobot", "parentIndex": 0, "targetIndexes": [78, 128]}, {"directoryIndexes": [54], "name": "Testtest_xml", "parentIndex": 0, "targetIndexes": [75, 129]}, {"directoryIndexes": [55], "name": "hardwaredriverAuboArcsDriver", "parentIndex": 0, "targetIndexes": [72, 133]}, {"directoryIndexes": [56], "name": "hardwaredriverAuboDriver", "parentIndex": 0, "targetIndexes": [69, 134]}, {"directoryIndexes": [57], "name": "hardwaredriverElectricGripperDriver", "parentIndex": 0, "targetIndexes": [66, 135]}, {"directoryIndexes": [58], "name": "hardwaredriverHikVisionCamera", "parentIndex": 0, "targetIndexes": [63, 136]}, {"directoryIndexes": [59], "name": "hardwaredriverLabelPrinter", "parentIndex": 0, "targetIndexes": [60, 137]}, {"directoryIndexes": [60], "name": "hardwaredriverMettlerBalance", "parentIndex": 0, "targetIndexes": [57, 138]}, {"directoryIndexes": [61], "name": "hardwaredriverOpcDa", "parentIndex": 0, "targetIndexes": [54, 139]}, {"directoryIndexes": [62], "name": "hardwaredriverOpcUa", "parentIndex": 0, "targetIndexes": [51, 140]}, {"directoryIndexes": [63], "name": "hardwaredriversocket", "parentIndex": 0, "targetIndexes": [48, 147]}, {"directoryIndexes": [64], "name": "hardwaredriveragilerobotDriver", "parentIndex": 0, "targetIndexes": [45, 142]}, {"directoryIndexes": [65], "name": "hardwaredriverfairinoDriver", "parentIndex": 0, "targetIndexes": [3, 143]}, {"directoryIndexes": [66], "name": "hardwaredriverjunduoHandDriver", "parentIndex": 0, "targetIndexes": [2, 144]}, {"directoryIndexes": [67], "name": "hardwaredrivermodbus", "parentIndex": 0, "targetIndexes": [9, 145]}, {"directoryIndexes": [68], "name": "hardwaredriverserial", "parentIndex": 0, "targetIndexes": [12, 146]}, {"directoryIndexes": [69], "name": "hardwaredriverusbcamera", "parentIndex": 0, "targetIndexes": [30, 148]}, {"directoryIndexes": [70], "name": "toolcalbuild", "parentIndex": 0, "targetIndexes": [15, 150]}, {"directoryIndexes": [71], "name": "toolcameraCalibrator", "parentIndex": 0, "targetIndexes": [18, 152]}, {"directoryIndexes": [72], "name": "toolcaltest", "parentIndex": 0, "targetIndexes": [21, 151]}, {"directoryIndexes": [73], "name": "toolcommunication", "parentIndex": 0, "targetIndexes": [22, 153]}, {"directoryIndexes": [74], "name": "toolhandeyecal", "parentIndex": 0, "targetIndexes": [24, 154]}, {"directoryIndexes": [75], "name": "toolhandeyecaltest", "parentIndex": 0, "targetIndexes": [27, 155]}, {"directoryIndexes": [76], "name": "toolhandeyecaluihandeyecalui", "parentIndex": 0, "targetIndexes": [33, 156]}, {"directoryIndexes": [77], "name": "toolhandeyecaluipath", "parentIndex": 0, "targetIndexes": [36, 157]}, {"directoryIndexes": [78], "name": "toolhandeyecaluipathAuto", "parentIndex": 0, "targetIndexes": [39, 158]}, {"directoryIndexes": [79], "name": "toolverify_calibration", "parentIndex": 0, "targetIndexes": [42, 159]}], "targets": [{"directoryIndex": 3, "id": "ALL_BUILD::@19f706e88e1d43a9565c", "jsonFile": "target-ALL_BUILD-Debug-85da0b39f38e7483b20e.json", "name": "ALL_BUILD", "projectIndex": 3}, {"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Debug-342279978964b07ce418.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 66, "id": "ALL_BUILD::@a89b79f2a82dbe076976", "jsonFile": "target-ALL_BUILD-Debug-40a7d901ed8e10a0c7f5.json", "name": "ALL_BUILD", "projectIndex": 66}, {"directoryIndex": 65, "id": "ALL_BUILD::@8a675cd9715b77cebac5", "jsonFile": "target-ALL_BUILD-Debug-2e630e3e3d96ead79510.json", "name": "ALL_BUILD", "projectIndex": 65}, {"directoryIndex": 52, "id": "ALL_BUILD::@39116d767a15e3a891df", "jsonFile": "target-ALL_BUILD-Debug-a7eeeef2e82f92727250.json", "name": "ALL_BUILD", "projectIndex": 52}, {"directoryIndex": 1, "id": "ALL_BUILD::@58335e9a86196d0a97e7", "jsonFile": "target-ALL_BUILD-Debug-0841e45febd559571c13.json", "name": "ALL_BUILD", "projectIndex": 1}, {"directoryIndex": 26, "id": "ALL_BUILD::@fef5c779e5dd4e5a5c7c", "jsonFile": "target-ALL_BUILD-Debug-68f406d96290d6b7df07.json", "name": "ALL_BUILD", "projectIndex": 26}, {"directoryIndex": 35, "id": "ALL_BUILD::@d885fae1c443095a1db7", "jsonFile": "target-ALL_BUILD-Debug-2569c2c10fad6870c72b.json", "name": "ALL_BUILD", "projectIndex": 35}, {"directoryIndex": 2, "id": "ALL_BUILD::@07001b74ee4af3db8a6e", "jsonFile": "target-ALL_BUILD-Debug-a93df49a17068820adac.json", "name": "ALL_BUILD", "projectIndex": 2}, {"directoryIndex": 67, "id": "ALL_BUILD::@2ef1e00ba9bcf504e7b3", "jsonFile": "target-ALL_BUILD-Debug-29cfabbfeabe66f20220.json", "name": "ALL_BUILD", "projectIndex": 67}, {"directoryIndex": 27, "id": "ALL_BUILD::@7e6cec28b989a66fe139", "jsonFile": "target-ALL_BUILD-Debug-07b7923a48da597b2076.json", "name": "ALL_BUILD", "projectIndex": 27}, {"directoryIndex": 50, "id": "ALL_BUILD::@6ff24ad6fb1c26ae0a57", "jsonFile": "target-ALL_BUILD-Debug-5e2fbceb8d24c84e42cf.json", "name": "ALL_BUILD", "projectIndex": 50}, {"directoryIndex": 68, "id": "ALL_BUILD::@e813d8aa5825a18a8390", "jsonFile": "target-ALL_BUILD-Debug-19c438a9df50cace71d5.json", "name": "ALL_BUILD", "projectIndex": 68}, {"directoryIndex": 51, "id": "ALL_BUILD::@e4ccddf7a4c29adf5bd6", "jsonFile": "target-ALL_BUILD-Debug-119fa9189d354d4060ac.json", "name": "ALL_BUILD", "projectIndex": 51}, {"directoryIndex": 4, "id": "ALL_BUILD::@97966baa9ab9c14a9bcf", "jsonFile": "target-ALL_BUILD-Debug-5339ba61d8ad5be7b663.json", "name": "ALL_BUILD", "projectIndex": 4}, {"directoryIndex": 70, "id": "ALL_BUILD::@a167bea24520843f7e43", "jsonFile": "target-ALL_BUILD-Debug-da1d6472f79bdb30833d.json", "name": "ALL_BUILD", "projectIndex": 70}, {"directoryIndex": 43, "id": "ALL_BUILD::@46d5ce0aeb8e42b7284d", "jsonFile": "target-ALL_BUILD-Debug-430b2635e5f4a69b8869.json", "name": "ALL_BUILD", "projectIndex": 43}, {"directoryIndex": 5, "id": "ALL_BUILD::@92995a2f85961e8f5b16", "jsonFile": "target-ALL_BUILD-Debug-8982b17ad107170d8e83.json", "name": "ALL_BUILD", "projectIndex": 5}, {"directoryIndex": 71, "id": "ALL_BUILD::@51e97efefc2313866ad5", "jsonFile": "target-ALL_BUILD-Debug-54994d3f173f21751830.json", "name": "ALL_BUILD", "projectIndex": 71}, {"directoryIndex": 28, "id": "ALL_BUILD::@1df6ab8cb0b8ddb593f8", "jsonFile": "target-ALL_BUILD-Debug-c504476d8ceceb45d7c2.json", "name": "ALL_BUILD", "projectIndex": 28}, {"directoryIndex": 6, "id": "ALL_BUILD::@e9ece92fe2bc47be420b", "jsonFile": "target-ALL_BUILD-Debug-c6d18cd0ab434516660b.json", "name": "ALL_BUILD", "projectIndex": 6}, {"directoryIndex": 72, "id": "ALL_BUILD::@9d9cd8ab08d8520a4ac3", "jsonFile": "target-ALL_BUILD-Debug-3d4cfdff622930c380ca.json", "name": "ALL_BUILD", "projectIndex": 72}, {"directoryIndex": 73, "id": "ALL_BUILD::@116eb0f160f4d76de168", "jsonFile": "target-ALL_BUILD-Debug-16899497fe6bfb496bf5.json", "name": "ALL_BUILD", "projectIndex": 73}, {"directoryIndex": 7, "id": "ALL_BUILD::@03c7a47b8090dea9b455", "jsonFile": "target-ALL_BUILD-Debug-ffa581a59595a1d89428.json", "name": "ALL_BUILD", "projectIndex": 7}, {"directoryIndex": 74, "id": "ALL_BUILD::@d7390e83b7e4f79f633d", "jsonFile": "target-ALL_BUILD-Debug-f7c053f7188b257ad6ab.json", "name": "ALL_BUILD", "projectIndex": 74}, {"directoryIndex": 38, "id": "ALL_BUILD::@7e2e321726c5f3e3edcb", "jsonFile": "target-ALL_BUILD-Debug-c6bf30b5692cd0ba3e2e.json", "name": "ALL_BUILD", "projectIndex": 38}, {"directoryIndex": 8, "id": "ALL_BUILD::@3f043c5f38f013ef2115", "jsonFile": "target-ALL_BUILD-Debug-a38675298dfaed7097d8.json", "name": "ALL_BUILD", "projectIndex": 8}, {"directoryIndex": 75, "id": "ALL_BUILD::@ae279e4383f26a866133", "jsonFile": "target-ALL_BUILD-Debug-0eb479fa5fcd1bd5f128.json", "name": "ALL_BUILD", "projectIndex": 75}, {"directoryIndex": 29, "id": "ALL_BUILD::@75eb8879fc099b4640aa", "jsonFile": "target-ALL_BUILD-Debug-999f14f3c47b505fa427.json", "name": "ALL_BUILD", "projectIndex": 29}, {"directoryIndex": 9, "id": "ALL_BUILD::@6b827d246feac3c35b9a", "jsonFile": "target-ALL_BUILD-Debug-bf24b70e9943d8b96cbc.json", "name": "ALL_BUILD", "projectIndex": 9}, {"directoryIndex": 69, "id": "ALL_BUILD::@bfaa0a8775de30d870f0", "jsonFile": "target-ALL_BUILD-Debug-9918016ebbb419e28d18.json", "name": "ALL_BUILD", "projectIndex": 69}, {"directoryIndex": 49, "id": "ALL_BUILD::@03373f949cbd329c961c", "jsonFile": "target-ALL_BUILD-Debug-b9a988aed78fcad4eb7e.json", "name": "ALL_BUILD", "projectIndex": 49}, {"directoryIndex": 10, "id": "ALL_BUILD::@6ccf8425ca6a81980105", "jsonFile": "target-ALL_BUILD-Debug-dad371b5f71f8a659af3.json", "name": "ALL_BUILD", "projectIndex": 10}, {"directoryIndex": 76, "id": "ALL_BUILD::@64c63141ea1fe7a116f6", "jsonFile": "target-ALL_BUILD-Debug-1048f699b992ebfb0588.json", "name": "ALL_BUILD", "projectIndex": 76}, {"directoryIndex": 36, "id": "ALL_BUILD::@08c5adc7ee1d91091a97", "jsonFile": "target-ALL_BUILD-Debug-ece8b323ed57b6f1fbe7.json", "name": "ALL_BUILD", "projectIndex": 36}, {"directoryIndex": 11, "id": "ALL_BUILD::@e0567cd60ef58755dd5b", "jsonFile": "target-ALL_BUILD-Debug-7e71292cd8fb1d3f305b.json", "name": "ALL_BUILD", "projectIndex": 11}, {"directoryIndex": 77, "id": "ALL_BUILD::@f8ebbc87f7fac77328c8", "jsonFile": "target-ALL_BUILD-Debug-2307048a7f0d82e4133d.json", "name": "ALL_BUILD", "projectIndex": 77}, {"directoryIndex": 30, "id": "ALL_BUILD::@4e1303897d180b86ab2f", "jsonFile": "target-ALL_BUILD-Debug-5b129cd61e1e6e577f56.json", "name": "ALL_BUILD", "projectIndex": 30}, {"directoryIndex": 12, "id": "ALL_BUILD::@96a57770f6c6f4e493b3", "jsonFile": "target-ALL_BUILD-Debug-6e1bc5ae653b03810b1e.json", "name": "ALL_BUILD", "projectIndex": 12}, {"directoryIndex": 78, "id": "ALL_BUILD::@f4fb3041b29f01391299", "jsonFile": "target-ALL_BUILD-Debug-1f9beecd76424e38b6ac.json", "name": "ALL_BUILD", "projectIndex": 78}, {"directoryIndex": 48, "id": "ALL_BUILD::@a384ba46c8f7385844c3", "jsonFile": "target-ALL_BUILD-Debug-585b87981061d80d6b32.json", "name": "ALL_BUILD", "projectIndex": 48}, {"directoryIndex": 13, "id": "ALL_BUILD::@6eec4415674bd20e6491", "jsonFile": "target-ALL_BUILD-Debug-67d66f0bb28969695805.json", "name": "ALL_BUILD", "projectIndex": 13}, {"directoryIndex": 79, "id": "ALL_BUILD::@efca4bcc8ad294d52f3d", "jsonFile": "target-ALL_BUILD-Debug-427747eec773300cb006.json", "name": "ALL_BUILD", "projectIndex": 79}, {"directoryIndex": 42, "id": "ALL_BUILD::@b13ebbd4a3aafa6a0363", "jsonFile": "target-ALL_BUILD-Debug-b28ba7495172b5cff460.json", "name": "ALL_BUILD", "projectIndex": 42}, {"directoryIndex": 14, "id": "ALL_BUILD::@1bc5057501657c23b12e", "jsonFile": "target-ALL_BUILD-Debug-44929f2f6ed2fa190302.json", "name": "ALL_BUILD", "projectIndex": 14}, {"directoryIndex": 64, "id": "ALL_BUILD::@14914dfd89874674d41d", "jsonFile": "target-ALL_BUILD-Debug-901e1781755324a5fec6.json", "name": "ALL_BUILD", "projectIndex": 64}, {"directoryIndex": 31, "id": "ALL_BUILD::@30345e39cecb9bcc06b0", "jsonFile": "target-ALL_BUILD-Debug-a2f4c2c3699b6d4e8f02.json", "name": "ALL_BUILD", "projectIndex": 31}, {"directoryIndex": 15, "id": "ALL_BUILD::@7c9daef8275400bf8ba5", "jsonFile": "target-ALL_BUILD-Debug-8eb8385fb4710473c8b0.json", "name": "ALL_BUILD", "projectIndex": 15}, {"directoryIndex": 63, "id": "ALL_BUILD::@e58766abf91db77f862b", "jsonFile": "target-ALL_BUILD-Debug-c49bfb773b8d9ae8a563.json", "name": "ALL_BUILD", "projectIndex": 63}, {"directoryIndex": 47, "id": "ALL_BUILD::@76c3a22b9f657d2ec026", "jsonFile": "target-ALL_BUILD-Debug-73870bfe79e2590bf24b.json", "name": "ALL_BUILD", "projectIndex": 47}, {"directoryIndex": 16, "id": "ALL_BUILD::@8670365571700e12b583", "jsonFile": "target-ALL_BUILD-Debug-f7583928953388d9577a.json", "name": "ALL_BUILD", "projectIndex": 16}, {"directoryIndex": 62, "id": "ALL_BUILD::@7bf30a519259482def19", "jsonFile": "target-ALL_BUILD-Debug-7285147ac61a7f6b2475.json", "name": "ALL_BUILD", "projectIndex": 62}, {"directoryIndex": 40, "id": "ALL_BUILD::@121a4898e406881ffb23", "jsonFile": "target-ALL_BUILD-Debug-9c608b9e07c5eb8aa8d3.json", "name": "ALL_BUILD", "projectIndex": 40}, {"directoryIndex": 17, "id": "ALL_BUILD::@f8e8ceb61b4f8c6d7034", "jsonFile": "target-ALL_BUILD-Debug-242568ff82df40a13a70.json", "name": "ALL_BUILD", "projectIndex": 17}, {"directoryIndex": 61, "id": "ALL_BUILD::@a2142d788288f069154a", "jsonFile": "target-ALL_BUILD-Debug-765fa3e850ca53bc5583.json", "name": "ALL_BUILD", "projectIndex": 61}, {"directoryIndex": 32, "id": "ALL_BUILD::@5cdc2d2ab21b3b9e72cd", "jsonFile": "target-ALL_BUILD-Debug-f679fc8971fbb8230b83.json", "name": "ALL_BUILD", "projectIndex": 32}, {"directoryIndex": 18, "id": "ALL_BUILD::@0a3c2e809899f2f13f5a", "jsonFile": "target-ALL_BUILD-Debug-0d2ad87deda7b2a9aa06.json", "name": "ALL_BUILD", "projectIndex": 18}, {"directoryIndex": 60, "id": "ALL_BUILD::@39b1645ff4c023a4e445", "jsonFile": "target-ALL_BUILD-Debug-084b090b7dc16868a8bc.json", "name": "ALL_BUILD", "projectIndex": 60}, {"directoryIndex": 46, "id": "ALL_BUILD::@65e3165a8812532710ef", "jsonFile": "target-ALL_BUILD-Debug-26611f28379a7309f899.json", "name": "ALL_BUILD", "projectIndex": 46}, {"directoryIndex": 19, "id": "ALL_BUILD::@933176848578d8c440c9", "jsonFile": "target-ALL_BUILD-Debug-c48b3bb23f187ae9ce55.json", "name": "ALL_BUILD", "projectIndex": 19}, {"directoryIndex": 59, "id": "ALL_BUILD::@a99f8207d5ede56c5cae", "jsonFile": "target-ALL_BUILD-Debug-9c0ab327cff663eb592d.json", "name": "ALL_BUILD", "projectIndex": 59}, {"directoryIndex": 37, "id": "ALL_BUILD::@889b7d31514c76e85624", "jsonFile": "target-ALL_BUILD-Debug-46891ee6e417731d3bb2.json", "name": "ALL_BUILD", "projectIndex": 37}, {"directoryIndex": 20, "id": "ALL_BUILD::@80cf03468317b5d7fe2b", "jsonFile": "target-ALL_BUILD-Debug-9a67b5f1091c8b72c93d.json", "name": "ALL_BUILD", "projectIndex": 20}, {"directoryIndex": 58, "id": "ALL_BUILD::@bc252bb14595a0f09d26", "jsonFile": "target-ALL_BUILD-Debug-0d333457689a8657f8bb.json", "name": "ALL_BUILD", "projectIndex": 58}, {"directoryIndex": 33, "id": "ALL_BUILD::@ccffbf515659b480dabe", "jsonFile": "target-ALL_BUILD-Debug-d029dae30f7abd04202a.json", "name": "ALL_BUILD", "projectIndex": 33}, {"directoryIndex": 21, "id": "ALL_BUILD::@9c3ce50b53cc3ff6eff5", "jsonFile": "target-ALL_BUILD-Debug-2bdca0b9ff24f6069f6f.json", "name": "ALL_BUILD", "projectIndex": 21}, {"directoryIndex": 57, "id": "ALL_BUILD::@e336ced093e233e6d829", "jsonFile": "target-ALL_BUILD-Debug-2490e630ef488d559bda.json", "name": "ALL_BUILD", "projectIndex": 57}, {"directoryIndex": 45, "id": "ALL_BUILD::@bb78083dcad0a236858d", "jsonFile": "target-ALL_BUILD-Debug-a4715af1c837cd3cfb57.json", "name": "ALL_BUILD", "projectIndex": 45}, {"directoryIndex": 22, "id": "ALL_BUILD::@59f7d9ae5c13d347c5f4", "jsonFile": "target-ALL_BUILD-Debug-55c4b70ff406511ddf93.json", "name": "ALL_BUILD", "projectIndex": 22}, {"directoryIndex": 56, "id": "ALL_BUILD::@e75f830eb736a5dca1ce", "jsonFile": "target-ALL_BUILD-Debug-d9762c58eea40fa5b0b3.json", "name": "ALL_BUILD", "projectIndex": 56}, {"directoryIndex": 41, "id": "ALL_BUILD::@1dc2f4735d896dd76909", "jsonFile": "target-ALL_BUILD-Debug-929fe4cbdbeb7ddf242d.json", "name": "ALL_BUILD", "projectIndex": 41}, {"directoryIndex": 23, "id": "ALL_BUILD::@d1520424919af3a40272", "jsonFile": "target-ALL_BUILD-Debug-6f3f47d4c763e4b65f51.json", "name": "ALL_BUILD", "projectIndex": 23}, {"directoryIndex": 55, "id": "ALL_BUILD::@fd5e493b37d2bab880d9", "jsonFile": "target-ALL_BUILD-Debug-603c76eaf26e2c4621cd.json", "name": "ALL_BUILD", "projectIndex": 55}, {"directoryIndex": 34, "id": "ALL_BUILD::@d803dad5c2b28052d845", "jsonFile": "target-ALL_BUILD-Debug-3f8d66d1990c24f51ece.json", "name": "ALL_BUILD", "projectIndex": 34}, {"directoryIndex": 24, "id": "ALL_BUILD::@cf8a855e37e415d7ca08", "jsonFile": "target-ALL_BUILD-Debug-4072b617fa6d0b547136.json", "name": "ALL_BUILD", "projectIndex": 24}, {"directoryIndex": 54, "id": "ALL_BUILD::@80c0713ae5ba495463b6", "jsonFile": "target-ALL_BUILD-Debug-f6c6c0ef380fb6385d06.json", "name": "ALL_BUILD", "projectIndex": 54}, {"directoryIndex": 44, "id": "ALL_BUILD::@7a0ade4671e16056f257", "jsonFile": "target-ALL_BUILD-Debug-446feef9c15e67387422.json", "name": "ALL_BUILD", "projectIndex": 44}, {"directoryIndex": 25, "id": "ALL_BUILD::@bca145e2342aef659032", "jsonFile": "target-ALL_BUILD-Debug-eb32980119712b6b14dc.json", "name": "ALL_BUILD", "projectIndex": 25}, {"directoryIndex": 53, "id": "ALL_BUILD::@7d9d822efa235ac321e6", "jsonFile": "target-ALL_BUILD-Debug-382e2efa87d6a17ef3e9.json", "name": "ALL_BUILD", "projectIndex": 53}, {"directoryIndex": 39, "id": "ALL_BUILD::@e99d12ef8be33386882a", "jsonFile": "target-ALL_BUILD-Debug-d89f761cec3c2e760907.json", "name": "ALL_BUILD", "projectIndex": 39}, {"directoryIndex": 10, "id": "Analysis_RobotApp::@6ccf8425ca6a81980105", "jsonFile": "target-Analysis_RobotApp-Debug-656ada18b0a824b060f0.json", "name": "Analysis_RobotApp", "projectIndex": 10}, {"directoryIndex": 11, "id": "Analysis_RobotalgorithmscoordinateTransform::@e0567cd60ef58755dd5b", "jsonFile": "target-Analysis_RobotalgorithmscoordinateTransform-Debug-fb717a230f642f9a0508.json", "name": "Analysis_RobotalgorithmscoordinateTransform", "projectIndex": 11}, {"directoryIndex": 2, "id": "Analysis_RobotalgorithmspouringControl::@07001b74ee4af3db8a6e", "jsonFile": "target-Analysis_RobotalgorithmspouringControl-Debug-123f28c6758cfc86413f.json", "name": "Analysis_RobotalgorithmspouringControl", "projectIndex": 2}, {"directoryIndex": 12, "id": "Analysis_RobotalgorithmstcpPositionMaintain::@96a57770f6c6f4e493b3", "jsonFile": "target-Analysis_RobotalgorithmstcpPositionMaintain-Debug-9a6d4d2b54f0e362cf40.json", "name": "Analysis_RobotalgorithmstcpPositionMaintain", "projectIndex": 12}, {"directoryIndex": 3, "id": "Analysis_RobotdriversaixsDriver::@19f706e88e1d43a9565c", "jsonFile": "target-Analysis_RobotdriversaixsDriver-Debug-355ce14a5206c64c939b.json", "name": "Analysis_RobotdriversaixsDriver", "projectIndex": 3}, {"directoryIndex": 5, "id": "Analysis_RobotdriversbalanceDriver::@92995a2f85961e8f5b16", "jsonFile": "target-Analysis_RobotdriversbalanceDriver-Debug-856638bf8aabe84a65a5.json", "name": "Analysis_RobotdriversbalanceDriver", "projectIndex": 5}, {"directoryIndex": 6, "id": "Analysis_RobotdriversheatingMagneticStirrerDriver::@e9ece92fe2bc47be420b", "jsonFile": "target-Analysis_RobotdriversheatingMagneticStirrerDriver-Debug-02a271947f875ee1bdf5.json", "name": "Analysis_RobotdriversheatingMagneticStirrerDriver", "projectIndex": 6}, {"directoryIndex": 7, "id": "Analysis_RobotdriversmoistureAnalyzerDriver::@03c7a47b8090dea9b455", "jsonFile": "target-Analysis_RobotdriversmoistureAnalyzerDriver-Debug-359aa73523cf92d4c4c5.json", "name": "Analysis_RobotdriversmoistureAnalyzerDriver", "projectIndex": 7}, {"directoryIndex": 4, "id": "Analysis_RobotdriversplcDriver::@97966baa9ab9c14a9bcf", "jsonFile": "target-Analysis_RobotdriversplcDriver-Debug-d2def6a0808477f62dac.json", "name": "Analysis_RobotdriversplcDriver", "projectIndex": 4}, {"directoryIndex": 9, "id": "Analysis_RobotdriversrestInterfaceDriver::@6b827d246feac3c35b9a", "jsonFile": "target-Analysis_RobotdriversrestInterfaceDriver-Debug-907b9562b1341d5c28df.json", "name": "Analysis_RobotdriversrestInterfaceDriver", "projectIndex": 9}, {"directoryIndex": 8, "id": "Analysis_RobotdriversrobotDriver::@3f043c5f38f013ef2115", "jsonFile": "target-Analysis_RobotdriversrobotDriver-Debug-2b2a3b693c5b0f76d946.json", "name": "Analysis_RobotdriversrobotDriver", "projectIndex": 8}, {"directoryIndex": 13, "id": "Analysis_RobottestheaterApiTest::@6eec4415674bd20e6491", "jsonFile": "target-Analysis_RobottestheaterApiTest-Debug-1b8a605feb594dec374b.json", "name": "Analysis_RobottestheaterApiTest", "projectIndex": 13}, {"directoryIndex": 14, "id": "Analysis_RobottestheatingMagneticStirrerDriver::@1bc5057501657c23b12e", "jsonFile": "target-Analysis_RobottestheatingMagneticStirrerDriver-Debug-a4d2fb516244c55fcd25.json", "name": "Analysis_RobottestheatingMagneticStirrerDriver", "projectIndex": 14}, {"directoryIndex": 15, "id": "MJServerAPP::@7c9daef8275400bf8ba5", "jsonFile": "target-MJServerAPP-Debug-79a0d1c6b732e5422d2a.json", "name": "MJServerAPP", "projectIndex": 15}, {"directoryIndex": 17, "id": "MJServer_RefactorApp::@f8e8ceb61b4f8c6d7034", "jsonFile": "target-MJServer_RefactorApp-Debug-2a32df6f678c21a3be31.json", "name": "MJServer_RefactorApp", "projectIndex": 17}, {"directoryIndex": 16, "id": "MJServer_RefactorLibrary::@8670365571700e12b583", "jsonFile": "target-MJServer_RefactorLibrary-Debug-b2a4c4539f54aa77d49b.json", "name": "MJServer_RefactorLibrary", "projectIndex": 16}, {"directoryIndex": 18, "id": "MJServer_RefactorTestphase1_test::@0a3c2e809899f2f13f5a", "jsonFile": "target-MJServer_RefactorTestphase1_test-Debug-d1965b26996a512a9861.json", "name": "MJServer_RefactorTestphase1_test", "projectIndex": 18}, {"directoryIndex": 19, "id": "MJServer_RefactorTestsimple_abb_client::@933176848578d8c440c9", "jsonFile": "target-MJServer_RefactorTestsimple_abb_client-Debug-021b5dacda0df9036716.json", "name": "MJServer_RefactorTestsimple_abb_client", "projectIndex": 19}, {"directoryIndex": 20, "id": "MJServer_RefactorTestsimple_feeder_client::@80cf03468317b5d7fe2b", "jsonFile": "target-MJServer_RefactorTestsimple_feeder_client-Debug-2130149680b9584b3072.json", "name": "MJServer_RefactorTestsimple_feeder_client", "projectIndex": 20}, {"directoryIndex": 21, "id": "RoboticLaserMarkingAbbDriver::@9c3ce50b53cc3ff6eff5", "jsonFile": "target-RoboticLaserMarkingAbbDriver-Debug-492a70acc87fe9dcf1a5.json", "name": "RoboticLaserMarkingAbbDriver", "projectIndex": 21}, {"directoryIndex": 22, "id": "RoboticLaserMarkingLicenseGenerator::@59f7d9ae5c13d347c5f4", "jsonFile": "target-RoboticLaserMarkingLicenseGenerator-Debug-8e778b85f167a3313ec3.json", "name": "RoboticLaserMarkingLicenseGenerator", "projectIndex": 22}, {"directoryIndex": 23, "id": "RoboticLaserMarkingRFIDDriver::@d1520424919af3a40272", "jsonFile": "target-RoboticLaserMarkingRFIDDriver-Debug-d8348470391cc5bd4f50.json", "name": "RoboticLaserMarkingRFIDDriver", "projectIndex": 23}, {"directoryIndex": 25, "id": "RoboticLaserMarkingTestabbsocket::@bca145e2342aef659032", "jsonFile": "target-RoboticLaserMarkingTestabbsocket-Debug-d9dfcc508786f58429f5.json", "name": "RoboticLaserMarkingTestabbsocket", "projectIndex": 25}, {"directoryIndex": 26, "id": "RoboticLaserMarkingTestlaser::@fef5c779e5dd4e5a5c7c", "jsonFile": "target-RoboticLaserMarkingTestlaser-Debug-66ee14559e3cc3c744c1.json", "name": "RoboticLaserMarkingTestlaser", "projectIndex": 26}, {"directoryIndex": 27, "id": "RoboticLaserMarkingTestlaserUI::@7e6cec28b989a66fe139", "jsonFile": "target-RoboticLaserMarkingTestlaserUI-Debug-2eebfa5a915aff625fe5.json", "name": "RoboticLaserMarkingTestlaserUI", "projectIndex": 27}, {"directoryIndex": 28, "id": "RoboticLaserMarkingTestrfiddriver::@1df6ab8cb0b8ddb593f8", "jsonFile": "target-RoboticLaserMarkingTestrfiddriver-Debug-d7289d4eb357b5d5a74d.json", "name": "RoboticLaserMarkingTestrfiddriver", "projectIndex": 28}, {"directoryIndex": 29, "id": "RoboticLaserMarkingTestrfidserver::@75eb8879fc099b4640aa", "jsonFile": "target-RoboticLaserMarkingTestrfidserver-Debug-0b14a0f40c1466bef2d0.json", "name": "RoboticLaserMarkingTestrfidserver", "projectIndex": 29}, {"directoryIndex": 30, "id": "RoboticLaserMarkingUI::@4e1303897d180b86ab2f", "jsonFile": "target-RoboticLaserMarkingUI-Debug-3562c5fe7efd673f6c76.json", "name": "RoboticLaserMarkingUI", "projectIndex": 30}, {"directoryIndex": 24, "id": "RoboticLaserMarkinglaserDriver::@cf8a855e37e415d7ca08", "jsonFile": "target-RoboticLaserMarkinglaserDriver-Debug-7c2f5cc8807dfa20e1e8.json", "name": "RoboticLaserMarkinglaserDriver", "projectIndex": 24}, {"directoryIndex": 31, "id": "RoboticLaserMarkinglaserDriverSim::@30345e39cecb9bcc06b0", "jsonFile": "target-RoboticLaserMarkinglaserDriverSim-Debug-88d1db417d810ef835be.json", "name": "RoboticLaserMarkinglaserDriverSim", "projectIndex": 31}, {"directoryIndex": 34, "id": "Testtest_abb_socket::@d803dad5c2b28052d845", "jsonFile": "target-Testtest_abb_socket-Debug-4c958681a27d6f313973.json", "name": "Testtest_abb_socket", "projectIndex": 34}, {"directoryIndex": 35, "id": "Testtest_config_manager::@d885fae1c443095a1db7", "jsonFile": "target-Testtest_config_manager-Debug-0002c0d190d03f35b1da.json", "name": "Testtest_config_manager", "projectIndex": 35}, {"directoryIndex": 36, "id": "Testtest_csv::@08c5adc7ee1d91091a97", "jsonFile": "target-Testtest_csv-Debug-cbac2723d27d985df86e.json", "name": "Testtest_csv", "projectIndex": 36}, {"directoryIndex": 37, "id": "Testtest_event_listener::@889b7d31514c76e85624", "jsonFile": "target-Testtest_event_listener-Debug-9e44d02baa685ee52992.json", "name": "Testtest_event_listener", "projectIndex": 37}, {"directoryIndex": 38, "id": "Testtest_executor::@7e2e321726c5f3e3edcb", "jsonFile": "target-Testtest_executor-Debug-0ed0ea81e172ad9bea6c.json", "name": "Testtest_executor", "projectIndex": 38}, {"directoryIndex": 39, "id": "Testtest_executor_context::@e99d12ef8be33386882a", "jsonFile": "target-Testtest_executor_context-Debug-2cde386188cf4a6f0e2e.json", "name": "Testtest_executor_context", "projectIndex": 39}, {"directoryIndex": 40, "id": "Testtest_fa2204n_balance::@121a4898e406881ffb23", "jsonFile": "target-Testtest_fa2204n_balance-Debug-86dbc75d7ecb03a61e9c.json", "name": "Testtest_fa2204n_balance", "projectIndex": 40}, {"directoryIndex": 41, "id": "Testtest_fa2204n_balance_basic::@1dc2f4735d896dd76909", "jsonFile": "target-Testtest_fa2204n_balance_basic-Debug-1638a694e96f095bb1f7.json", "name": "Testtest_fa2204n_balance_basic", "projectIndex": 41}, {"directoryIndex": 42, "id": "Testtest_fileutil::@b13ebbd4a3aafa6a0363", "jsonFile": "target-Testtest_fileutil-Debug-357d88ae2386b11e51c7.json", "name": "Testtest_fileutil", "projectIndex": 42}, {"directoryIndex": 43, "id": "Testtest_json::@46d5ce0aeb8e42b7284d", "jsonFile": "target-Testtest_json-Debug-0c9cbc212c9bcee55dfe.json", "name": "Testtest_json", "projectIndex": 43}, {"directoryIndex": 44, "id": "Testtest_license_manager::@7a0ade4671e16056f257", "jsonFile": "target-Testtest_license_manager-Debug-97215afd247804a557f6.json", "name": "Testtest_license_manager", "projectIndex": 44}, {"directoryIndex": 45, "id": "Testtest_license_ui::@bb78083dcad0a236858d", "jsonFile": "target-Testtest_license_ui-Debug-8973c310499ba630a16d.json", "name": "Testtest_license_ui", "projectIndex": 45}, {"directoryIndex": 47, "id": "Testtest_network::@76c3a22b9f657d2ec026", "jsonFile": "target-Testtest_network-Debug-757eb1ba625c1c031dcf.json", "name": "Testtest_network", "projectIndex": 47}, {"directoryIndex": 48, "id": "Testtest_serial::@a384ba46c8f7385844c3", "jsonFile": "target-Testtest_serial-Debug-434a85260c9e8f54497c.json", "name": "Testtest_serial", "projectIndex": 48}, {"directoryIndex": 49, "id": "Testtest_service_container::@03373f949cbd329c961c", "jsonFile": "target-Testtest_service_container-Debug-85f6cb4ba544a530c617.json", "name": "Testtest_service_container", "projectIndex": 49}, {"directoryIndex": 50, "id": "Testtest_socket::@6ff24ad6fb1c26ae0a57", "jsonFile": "target-Testtest_socket-Debug-851b466f27a6d81b2f78.json", "name": "Testtest_socket", "projectIndex": 50}, {"directoryIndex": 51, "id": "Testtest_sqlite::@e4ccddf7a4c29adf5bd6", "jsonFile": "target-Testtest_sqlite-Debug-3bec0f809e0e06bb3e50.json", "name": "Testtest_sqlite", "projectIndex": 51}, {"directoryIndex": 52, "id": "Testtest_taskflow::@39116d767a15e3a891df", "jsonFile": "target-Testtest_taskflow-Debug-c79ffd7bcfbef3b447d3.json", "name": "Testtest_taskflow", "projectIndex": 52}, {"directoryIndex": 53, "id": "Testtest_twoaixsrobot::@7d9d822efa235ac321e6", "jsonFile": "target-Testtest_twoaixsrobot-Debug-b4eb8bdff105b0c68c5a.json", "name": "Testtest_twoaixsrobot", "projectIndex": 53}, {"directoryIndex": 54, "id": "Testtest_xml::@80c0713ae5ba495463b6", "jsonFile": "target-Testtest_xml-Debug-88925176959ac9f53293.json", "name": "Testtest_xml", "projectIndex": 54}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Debug-1ca4f1b2e21860d5885e.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 1, "id": "fuxicommon::@58335e9a86196d0a97e7", "jsonFile": "target-fuxicommon-Debug-49c1fdd919b15d401ce4.json", "name": "fuxicommon", "projectIndex": 1}, {"directoryIndex": 32, "id": "fuxicore::@5cdc2d2ab21b3b9e72cd", "jsonFile": "target-fuxicore-Debug-3e93764fc5e0523cbd0a.json", "name": "fuxicore", "projectIndex": 32}, {"directoryIndex": 55, "id": "hardwaredriverAuboArcsDriver::@fd5e493b37d2bab880d9", "jsonFile": "target-hardwaredriverAuboArcsDriver-Debug-1bdbe51b1505ae2eb7e3.json", "name": "hardwaredriverAuboArcsDriver", "projectIndex": 55}, {"directoryIndex": 56, "id": "hardwaredriverAuboDriver::@e75f830eb736a5dca1ce", "jsonFile": "target-hardwaredriverAuboDriver-Debug-63f691fbb0f54a9efaa6.json", "name": "hardwaredriverAuboDriver", "projectIndex": 56}, {"directoryIndex": 57, "id": "hardwaredriverElectricGripperDriver::@e336ced093e233e6d829", "jsonFile": "target-hardwaredriverElectricGripperDriver-Debug-6dac6b56c57ab5dc5014.json", "name": "hardwaredriverElectricGripperDriver", "projectIndex": 57}, {"directoryIndex": 58, "id": "hardwaredriverHikVisionCamera::@bc252bb14595a0f09d26", "jsonFile": "target-hardwaredriverHikVisionCamera-Debug-beeaf38774b650e211da.json", "name": "hardwaredriverHikVisionCamera", "projectIndex": 58}, {"directoryIndex": 59, "id": "hardwaredriverLabelPrinter::@a99f8207d5ede56c5cae", "jsonFile": "target-hardwaredriverLabelPrinter-Debug-d89e5406073a659dab06.json", "name": "hardwaredriverLabelPrinter", "projectIndex": 59}, {"directoryIndex": 60, "id": "hardwaredriverMettlerBalance::@39b1645ff4c023a4e445", "jsonFile": "target-hardwaredriverMettlerBalance-Debug-83a0aeeaec4f11985109.json", "name": "hardwaredriverMettlerBalance", "projectIndex": 60}, {"directoryIndex": 61, "id": "hardwaredriverOpcDa::@a2142d788288f069154a", "jsonFile": "target-hardwaredriverOpcDa-Debug-bec45f5d4746afdd8df6.json", "name": "hardwaredriverOpcDa", "projectIndex": 61}, {"directoryIndex": 62, "id": "hardwaredriverOpcUa::@7bf30a519259482def19", "jsonFile": "target-hardwaredriverOpcUa-Debug-3fa5bf62647dd43b8922.json", "name": "hardwaredriverOpcUa", "projectIndex": 62}, {"directoryIndex": 33, "id": "hardwaredriverabbRobotDriver::@ccffbf515659b480dabe", "jsonFile": "target-hardwaredriverabbRobotDriver-Debug-99ff0a72954571a1533d.json", "name": "hardwaredriverabbRobotDriver", "projectIndex": 33}, {"directoryIndex": 64, "id": "hardwaredriveragilerobotDriver::@14914dfd89874674d41d", "jsonFile": "target-hardwaredriveragilerobotDriver-Debug-4140815f888108eb94e3.json", "name": "hardwaredriveragilerobotDriver", "projectIndex": 64}, {"directoryIndex": 65, "id": "hardwaredriverfairinoDriver::@8a675cd9715b77cebac5", "jsonFile": "target-hardwaredriverfairinoDriver-Debug-8d3c4fa60279a0c31e2f.json", "name": "hardwaredriverfairinoDriver", "projectIndex": 65}, {"directoryIndex": 66, "id": "hardwaredriverjunduoHandDriver::@a89b79f2a82dbe076976", "jsonFile": "target-hardwaredriverjunduoHandDriver-Debug-435eed0bfed88c7ac9ad.json", "name": "hardwaredriverjunduoHandDriver", "projectIndex": 66}, {"directoryIndex": 67, "id": "hardwaredrivermodbus::@2ef1e00ba9bcf504e7b3", "jsonFile": "target-hardwaredrivermodbus-Debug-63860956d4b5aa14b140.json", "name": "hardwaredrivermodbus", "projectIndex": 67}, {"directoryIndex": 68, "id": "hardwaredriverserial::@e813d8aa5825a18a8390", "jsonFile": "target-hardwaredriverserial-Debug-ea55949f056b7203d406.json", "name": "hardwaredriverserial", "projectIndex": 68}, {"directoryIndex": 63, "id": "hardwaredriversocket::@e58766abf91db77f862b", "jsonFile": "target-hardwaredriversocket-Debug-f07047b3e4da01d9d2ab.json", "name": "hardwaredriversocket", "projectIndex": 63}, {"directoryIndex": 69, "id": "hardwaredriverusbcamera::@bfaa0a8775de30d870f0", "jsonFile": "target-hardwaredriverusbcamera-Debug-505873bed617ead10fcf.json", "name": "hardwaredriverusbcamera", "projectIndex": 69}, {"directoryIndex": 46, "id": "test_micro_dosing::@65e3165a8812532710ef", "jsonFile": "target-test_micro_dosing-Debug-75d10b0cfa5b9a9f64c7.json", "name": "test_micro_dosing", "projectIndex": 46}, {"directoryIndex": 70, "id": "toolcalbuild::@a167bea24520843f7e43", "jsonFile": "target-toolcalbuild-Debug-418ec703ec0e6446d310.json", "name": "toolcalbuild", "projectIndex": 70}, {"directoryIndex": 72, "id": "toolcaltest::@9d9cd8ab08d8520a4ac3", "jsonFile": "target-toolcaltest-Debug-968cb208eaf60cfcc6bf.json", "name": "toolcaltest", "projectIndex": 72}, {"directoryIndex": 71, "id": "toolcameraCalibrator::@51e97efefc2313866ad5", "jsonFile": "target-toolcameraCalibrator-Debug-081f6d11f58d66d63bfc.json", "name": "toolcameraCalibrator", "projectIndex": 71}, {"directoryIndex": 73, "id": "toolcommunication::@116eb0f160f4d76de168", "jsonFile": "target-toolcommunication-Debug-7f35d35492cb3be40725.json", "name": "toolcommunication", "projectIndex": 73}, {"directoryIndex": 74, "id": "toolhandeyecal::@d7390e83b7e4f79f633d", "jsonFile": "target-toolhandeyecal-Debug-7d0831e6e36b0c2f7c25.json", "name": "toolhandeyecal", "projectIndex": 74}, {"directoryIndex": 75, "id": "toolhandeyecaltest::@ae279e4383f26a866133", "jsonFile": "target-toolhandeyecaltest-Debug-40efaa913619d3b9df8a.json", "name": "toolhandeyecaltest", "projectIndex": 75}, {"directoryIndex": 76, "id": "toolhandeyecaluihandeyecalui::@64c63141ea1fe7a116f6", "jsonFile": "target-toolhandeyecaluihandeyecalui-Debug-d411ae18c3e956c37396.json", "name": "toolhandeyecaluihandeyecalui", "projectIndex": 76}, {"directoryIndex": 77, "id": "toolhandeyecaluipath::@f8ebbc87f7fac77328c8", "jsonFile": "target-toolhandeyecaluipath-Debug-fc6cd18fdfcbe4a73a79.json", "name": "toolhandeyecaluipath", "projectIndex": 77}, {"directoryIndex": 78, "id": "toolhandeyecaluipathAuto::@f4fb3041b29f01391299", "jsonFile": "target-toolhandeyecaluipathAuto-Debug-77375694226a34f2e337.json", "name": "toolhandeyecaluipathAuto", "projectIndex": 78}, {"directoryIndex": 79, "id": "toolverify_calibration::@efca4bcc8ad294d52f3d", "jsonFile": "target-toolverify_calibration-Debug-cb3b6ab15f8ad8ccebec.json", "name": "toolverify_calibration", "projectIndex": 79}]}, {"directories": [{"build": ".", "childIndexes": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79], "hasInstallRule": true, "jsonFile": "directory-.-Release-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [1, 130]}, {"build": "fuxicommon", "hasInstallRule": true, "jsonFile": "directory-fuxicommon-Release-45b01aad2fc6492274cb.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 1, "source": "fuxicommon", "targetIndexes": [5, 131]}, {"build": "Analysis_Robot/algorithms/pouringControl", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.algorithms.pouringControl-Release-b6a4a096167825bdc477.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 2, "source": "Analysis_Robot/algorithms/pouringControl", "targetIndexes": [8, 82]}, {"build": "Analysis_Robot/drivers/aixsDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.aixsDriver-Release-acad4faf4f883a05b16c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 3, "source": "Analysis_Robot/drivers/aixsDriver", "targetIndexes": [0, 84]}, {"build": "Analysis_Robot/drivers/plcDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.plcDriver-Release-1785bcd9213080dbd037.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 4, "source": "Analysis_Robot/drivers/plcDriver", "targetIndexes": [14, 88]}, {"build": "Analysis_Robot/drivers/balanceDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.balanceDriver-Release-bd88b671d17006b62362.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 5, "source": "Analysis_Robot/drivers/balanceDriver", "targetIndexes": [17, 85]}, {"build": "Analysis_Robot/drivers/heatingMagneticStirrerDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.heatingMagneticStirrerDriver-Release-58f3ac9f2a5c9e15f72a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 6, "source": "Analysis_Robot/drivers/heatingMagneticStirrerDriver", "targetIndexes": [20, 86]}, {"build": "Analysis_Robot/drivers/moistureAnalyzerDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.moistureAnalyzerDriver-Release-013e15aafee981d8281c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 7, "source": "Analysis_Robot/drivers/moistureAnalyzerDriver", "targetIndexes": [23, 87]}, {"build": "Analysis_Robot/drivers/robotDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.robotDriver-Release-2c83fb91a80b187dc6d1.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 8, "source": "Analysis_Robot/drivers/robotDriver", "targetIndexes": [26, 90]}, {"build": "Analysis_Robot/drivers/restInterfaceDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.restInterfaceDriver-Release-32537dba61e43ea53bc5.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 9, "source": "Analysis_Robot/drivers/restInterfaceDriver", "targetIndexes": [29, 89]}, {"build": "Analysis_Robot/App", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.App-Release-e2ede17c1187489a25e9.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 10, "source": "Analysis_Robot/App", "targetIndexes": [32, 80]}, {"build": "Analysis_Robot/algorithms/coordinateTransform", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.algorithms.coordinateTransform-Release-0dc862cc663c92448147.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 11, "source": "Analysis_Robot/algorithms/coordinateTransform", "targetIndexes": [35, 81]}, {"build": "Analysis_Robot/algorithms/tcpPositionMaintain", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.algorithms.tcpPositionMaintain-Release-14eb9bf209fc848d31d1.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 12, "source": "Analysis_Robot/algorithms/tcpPositionMaintain", "targetIndexes": [38, 83]}, {"build": "Analysis_Robot/test/heaterApiTest", "jsonFile": "directory-Analysis_Robot.test.heaterApiTest-Release-b999f5688c4067f4e68d.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 13, "source": "Analysis_Robot/test/heaterApiTest", "targetIndexes": [41, 91]}, {"build": "Analysis_Robot/test/heatingMagneticStirrerDriver", "jsonFile": "directory-Analysis_Robot.test.heatingMagneticStirrerDriver-Release-e5cbd850de4545ce6cdf.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 14, "source": "Analysis_Robot/test/heatingMagneticStirrerDriver", "targetIndexes": [44, 92]}, {"build": "MJServer/APP", "hasInstallRule": true, "jsonFile": "directory-MJServer.APP-Release-9a0005353ac4d44a8cb8.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 15, "source": "MJServer/APP", "targetIndexes": [47, 93]}, {"build": "MJServer_Refactor/Library", "hasInstallRule": true, "jsonFile": "directory-MJServer_Refactor.Library-Release-9ec6a9fe118a63a425b3.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 16, "source": "MJServer_Refactor/Library", "targetIndexes": [50, 95]}, {"build": "MJServer_Refactor/App", "jsonFile": "directory-MJServer_Refactor.App-Release-25c2817210280de79843.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 17, "source": "MJServer_Refactor/App", "targetIndexes": [53, 94]}, {"build": "MJServer_Refactor/Test/phase1_test", "jsonFile": "directory-MJServer_Refactor.Test.phase1_test-Release-0a76c9c92d103a235c64.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 18, "source": "MJServer_Refactor/Test/phase1_test", "targetIndexes": [56, 96]}, {"build": "MJServer_Refactor/Test/simple_abb_client", "jsonFile": "directory-MJServer_Refactor.Test.simple_abb_client-Release-d55604710c3a3fb8f0bb.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 19, "source": "MJServer_Refactor/Test/simple_abb_client", "targetIndexes": [59, 97]}, {"build": "MJServer_Refactor/Test/simple_feeder_client", "jsonFile": "directory-MJServer_Refactor.Test.simple_feeder_client-Release-615291d1f0d9de4ac87c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 20, "source": "MJServer_Refactor/Test/simple_feeder_client", "targetIndexes": [62, 98]}, {"build": "RoboticLaserMarking/AbbDriver", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.AbbDriver-Release-6a2fe08206f932672311.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 21, "source": "RoboticLaserMarking/AbbDriver", "targetIndexes": [65, 99]}, {"build": "RoboticLaserMarking/LicenseGenerator", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.LicenseGenerator-Release-5d8bc3a4c1c58bab6f7a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 22, "source": "RoboticLaserMarking/LicenseGenerator", "targetIndexes": [68, 100]}, {"build": "RoboticLaserMarking/RFIDDriver", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.RFIDDriver-Release-ce034b8fd7255dd94677.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 23, "source": "RoboticLaserMarking/RFIDDriver", "targetIndexes": [71, 101]}, {"build": "RoboticLaserMarking/laserDriver", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.laserDriver-Release-50ac02b6f964d67969b6.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 24, "source": "RoboticLaserMarking/laserDriver", "targetIndexes": [74, 108]}, {"build": "RoboticLaserMarking/Test/abbsocket", "jsonFile": "directory-RoboticLaserMarking.Test.abbsocket-Release-c54e19bdb9ae31bded65.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 25, "source": "RoboticLaserMarking/Test/abbsocket", "targetIndexes": [77, 102]}, {"build": "RoboticLaserMarking/Test/laser", "jsonFile": "directory-RoboticLaserMarking.Test.laser-Release-fda96428a10838b20da0.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 26, "source": "RoboticLaserMarking/Test/laser", "targetIndexes": [6, 103]}, {"build": "RoboticLaserMarking/Test/laserUI", "jsonFile": "directory-RoboticLaserMarking.Test.laserUI-Release-906cc9e5b37f734dfdd8.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 27, "source": "RoboticLaserMarking/Test/laserUI", "targetIndexes": [10, 104]}, {"build": "RoboticLaserMarking/Test/rfiddriver", "jsonFile": "directory-RoboticLaserMarking.Test.rfiddriver-Release-f51044958bce4e484373.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 28, "source": "RoboticLaserMarking/Test/rfiddriver", "targetIndexes": [19, 105]}, {"build": "RoboticLaserMarking/Test/rfidserver", "jsonFile": "directory-RoboticLaserMarking.Test.rfidserver-Release-7e022394fa2631804126.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 29, "source": "RoboticLaserMarking/Test/rfidserver", "targetIndexes": [28, 106]}, {"build": "RoboticLaserMarking/UI", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.UI-Release-a0a826d45ad2ea8615b1.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 30, "source": "RoboticLaserMarking/UI", "targetIndexes": [37, 107]}, {"build": "RoboticLaserMarking/laserDriverSim", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.laserDriverSim-Release-9fa894fbfa57d970660d.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 31, "source": "RoboticLaserMarking/laserDriverSim", "targetIndexes": [46, 109]}, {"build": "fuxicore", "hasInstallRule": true, "jsonFile": "directory-fuxicore-Release-6019081bb93cc97e5bd3.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 32, "source": "fuxicore", "targetIndexes": [55, 132]}, {"build": "hardwaredriver/abbRobotDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.abbRobotDriver-Release-6dd56340ab873b7947eb.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 33, "source": "hardwaredriver/abbRobotDriver", "targetIndexes": [64, 141]}, {"build": "Test/test_abb_socket", "jsonFile": "directory-Test.test_abb_socket-Release-b83d4bb5753115673b1e.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 34, "source": "Test/test_abb_socket", "targetIndexes": [73, 110]}, {"build": "Test/test_config_manager", "jsonFile": "directory-Test.test_config_manager-Release-b1e58fcc0034795c6ae3.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 35, "source": "Test/test_config_manager", "targetIndexes": [7, 111]}, {"build": "Test/test_csv", "jsonFile": "directory-Test.test_csv-Release-46cd2d2264edf5d7592e.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 36, "source": "Test/test_csv", "targetIndexes": [34, 112]}, {"build": "Test/test_event_listener", "jsonFile": "directory-Test.test_event_listener-Release-87efd742cb5ddc812bd7.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 37, "source": "Test/test_event_listener", "targetIndexes": [61, 113]}, {"build": "Test/test_executor", "jsonFile": "directory-Test.test_executor-Release-a98e8731e17c62507f58.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 38, "source": "Test/test_executor", "targetIndexes": [25, 114]}, {"build": "Test/test_executor_context", "jsonFile": "directory-Test.test_executor_context-Release-7ab477d5e8562815cbb7.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 39, "source": "Test/test_executor_context", "targetIndexes": [79, 115]}, {"build": "Test/test_fa2204n_balance", "jsonFile": "directory-Test.test_fa2204n_balance-Release-7560d87111b5ff21f33f.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 40, "source": "Test/test_fa2204n_balance", "targetIndexes": [52, 116]}, {"build": "Test/test_fa2204n_balance_basic", "jsonFile": "directory-Test.test_fa2204n_balance_basic-Release-c5e19bbf14befd24dc6e.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 41, "source": "Test/test_fa2204n_balance_basic", "targetIndexes": [70, 117]}, {"build": "Test/test_fileutil", "jsonFile": "directory-Test.test_fileutil-Release-6a4234fe4fd9c32b60c0.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 42, "source": "Test/test_fileutil", "targetIndexes": [43, 118]}, {"build": "Test/test_json", "jsonFile": "directory-Test.test_json-Release-70316fee5b76b6255c9c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 43, "source": "Test/test_json", "targetIndexes": [16, 119]}, {"build": "Test/test_license_manager", "jsonFile": "directory-Test.test_license_manager-Release-adf854e580c4cdcb7fc5.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 44, "source": "Test/test_license_manager", "targetIndexes": [76, 120]}, {"build": "Test/test_license_ui", "hasInstallRule": true, "jsonFile": "directory-Test.test_license_ui-Release-17e8c4b69d69a26aaf2a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 45, "source": "Test/test_license_ui", "targetIndexes": [67, 121]}, {"build": "Test/test_micro_dosing", "jsonFile": "directory-Test.test_micro_dosing-Release-5e830cdb711d4e75b3ab.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 46, "source": "Test/test_micro_dosing", "targetIndexes": [58, 149]}, {"build": "Test/test_network", "jsonFile": "directory-Test.test_network-Release-802d04b39615713bdaa5.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 47, "source": "Test/test_network", "targetIndexes": [49, 122]}, {"build": "Test/test_serial", "jsonFile": "directory-Test.test_serial-Release-1539ef84150cf55d007b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 48, "source": "Test/test_serial", "targetIndexes": [40, 123]}, {"build": "Test/test_service_container", "jsonFile": "directory-Test.test_service_container-Release-3c607165866062790453.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 49, "source": "Test/test_service_container", "targetIndexes": [31, 124]}, {"build": "Test/test_socket", "jsonFile": "directory-Test.test_socket-Release-7646e8df0e04f7d933fc.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 50, "source": "Test/test_socket", "targetIndexes": [11, 125]}, {"build": "Test/test_sqlite", "jsonFile": "directory-Test.test_sqlite-Release-a3f7e251b42c8d6e85bd.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 51, "source": "Test/test_sqlite", "targetIndexes": [13, 126]}, {"build": "Test/test_taskflow", "jsonFile": "directory-Test.test_taskflow-Release-338290a1d2d2c191692b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 52, "source": "Test/test_taskflow", "targetIndexes": [4, 127]}, {"build": "Test/test_twoaixsrobot", "jsonFile": "directory-Test.test_twoaixsrobot-Release-829343d8fb5c82be5c03.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 53, "source": "Test/test_twoaixsrobot", "targetIndexes": [78, 128]}, {"build": "Test/test_xml", "jsonFile": "directory-Test.test_xml-Release-91dd4b4ea7db1ac593a4.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 54, "source": "Test/test_xml", "targetIndexes": [75, 129]}, {"build": "hardwaredriver/AuboArcsDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.AuboArcsDriver-Release-c8464a5a4e222d8a4324.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 55, "source": "hardwaredriver/AuboArcsDriver", "targetIndexes": [72, 133]}, {"build": "hardwaredriver/AuboDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.AuboDriver-Release-f6ef18b990c62843287d.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 56, "source": "hardwaredriver/AuboDriver", "targetIndexes": [69, 134]}, {"build": "hardwaredriver/ElectricGripperDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.ElectricGripperDriver-Release-9050ee0aa574d02a13c1.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 57, "source": "hardwaredriver/ElectricGripperDriver", "targetIndexes": [66, 135]}, {"build": "hardwaredriver/HikVisionCamera", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.HikVisionCamera-Release-2f15954957700dc82692.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 58, "source": "hardwaredriver/HikVisionCamera", "targetIndexes": [63, 136]}, {"build": "hardwaredriver/LabelPrinter", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.LabelPrinter-Release-a98e344906e45e035c7a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 59, "source": "hardwaredriver/LabelPrinter", "targetIndexes": [60, 137]}, {"build": "hardwaredriver/MettlerBalance", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.MettlerBalance-Release-f65a38351f7de3288dc7.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 60, "source": "hardwaredriver/MettlerBalance", "targetIndexes": [57, 138]}, {"build": "hardwaredriver/OpcDa", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.OpcDa-Release-5c7bc29dbda15a9b8ae4.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 61, "source": "hardwaredriver/OpcDa", "targetIndexes": [54, 139]}, {"build": "hardwaredriver/OpcUa", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.OpcUa-Release-36fae1fe277182987806.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 62, "source": "hardwaredriver/OpcUa", "targetIndexes": [51, 140]}, {"build": "hardwaredriver/socket", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.socket-Release-b084aeaa98c55ca54242.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 63, "source": "hardwaredriver/socket", "targetIndexes": [48, 147]}, {"build": "hardwaredriver/agilerobotDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.agilerobotDriver-Release-be0c33e6eb56c2575bd9.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 64, "source": "hardwaredriver/agilerobotDriver", "targetIndexes": [45, 142]}, {"build": "hardwaredriver/fairinoDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.fairinoDriver-Release-b638d5fdff8da064311b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 65, "source": "hardwaredriver/fairinoDriver", "targetIndexes": [3, 143]}, {"build": "hardwaredriver/junduoHandDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.junduoHandDriver-Release-e7024192621c226dc16f.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 66, "source": "hardwaredriver/junduoHandDriver", "targetIndexes": [2, 144]}, {"build": "hardwaredriver/modbus", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.modbus-Release-1f7cf0a90d4506a41f5a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 67, "source": "hardwaredriver/modbus", "targetIndexes": [9, 145]}, {"build": "hardwaredriver/serial", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.serial-Release-40c48bd4e0de0af8828e.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 68, "source": "hardwaredriver/serial", "targetIndexes": [12, 146]}, {"build": "hardwaredriver/usbcamera", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.usbcamera-Release-968bee8cafdf7e5f9ccf.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 69, "source": "hardwaredriver/usbcamera", "targetIndexes": [30, 148]}, {"build": "tool/calbuild", "jsonFile": "directory-tool.calbuild-Release-9edb4e157f9b6e54846b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 70, "source": "tool/calbuild", "targetIndexes": [15, 150]}, {"build": "tool/cameraCalibrator", "hasInstallRule": true, "jsonFile": "directory-tool.cameraCalibrator-Release-a861825f388659f4f715.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 71, "source": "tool/cameraCalibrator", "targetIndexes": [18, 152]}, {"build": "tool/caltest", "hasInstallRule": true, "jsonFile": "directory-tool.caltest-Release-63fb02a9aaef7a0c834c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 72, "source": "tool/caltest", "targetIndexes": [21, 151]}, {"build": "tool/communication", "hasInstallRule": true, "jsonFile": "directory-tool.communication-Release-324152a549fff44395ce.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 73, "source": "tool/communication", "targetIndexes": [22, 153]}, {"build": "tool/handeyecal", "hasInstallRule": true, "jsonFile": "directory-tool.handeyecal-Release-77ea94e986eab795f767.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 74, "source": "tool/handeyecal", "targetIndexes": [24, 154]}, {"build": "tool/handeyecaltest", "jsonFile": "directory-tool.handeyecaltest-Release-f9f22629594fc4ab2dbc.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 75, "source": "tool/handeyecaltest", "targetIndexes": [27, 155]}, {"build": "tool/handeyecalui/handeyecalui", "jsonFile": "directory-tool.handeyecalui.handeyecalui-Release-1fafdc8136eaae57e66b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 76, "source": "tool/handeyecalui/handeyecalui", "targetIndexes": [33, 156]}, {"build": "tool/handeyecaluipath", "jsonFile": "directory-tool.handeyecaluipath-Release-e479629856c728efc292.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 77, "source": "tool/handeyecaluipath", "targetIndexes": [36, 157]}, {"build": "tool/handeyecaluipathAuto", "jsonFile": "directory-tool.handeyecaluipathAuto-Release-0a177976965c3cc59f22.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 78, "source": "tool/handeyecaluipathAuto", "targetIndexes": [39, 158]}, {"build": "tool/verify_calibration", "jsonFile": "directory-tool.verify_calibration-Release-2d3946f9054860a13e8b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 79, "source": "tool/verify_calibration", "targetIndexes": [42, 159]}], "name": "Release", "projects": [{"childIndexes": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79], "directoryIndexes": [0], "name": "Project", "targetIndexes": [1, 130]}, {"directoryIndexes": [1], "name": "fuxicommon", "parentIndex": 0, "targetIndexes": [5, 131]}, {"directoryIndexes": [2], "name": "Analysis_RobotalgorithmspouringControl", "parentIndex": 0, "targetIndexes": [8, 82]}, {"directoryIndexes": [3], "name": "Analysis_RobotdriversaixsDriver", "parentIndex": 0, "targetIndexes": [0, 84]}, {"directoryIndexes": [4], "name": "Analysis_RobotdriversplcDriver", "parentIndex": 0, "targetIndexes": [14, 88]}, {"directoryIndexes": [5], "name": "Analysis_RobotdriversbalanceDriver", "parentIndex": 0, "targetIndexes": [17, 85]}, {"directoryIndexes": [6], "name": "Analysis_RobotdriversheatingMagneticStirrerDriver", "parentIndex": 0, "targetIndexes": [20, 86]}, {"directoryIndexes": [7], "name": "Analysis_RobotdriversmoistureAnalyzerDriver", "parentIndex": 0, "targetIndexes": [23, 87]}, {"directoryIndexes": [8], "name": "Analysis_RobotdriversrobotDriver", "parentIndex": 0, "targetIndexes": [26, 90]}, {"directoryIndexes": [9], "name": "Analysis_RobotdriversrestInterfaceDriver", "parentIndex": 0, "targetIndexes": [29, 89]}, {"directoryIndexes": [10], "name": "Analysis_RobotApp", "parentIndex": 0, "targetIndexes": [32, 80]}, {"directoryIndexes": [11], "name": "Analysis_RobotalgorithmscoordinateTransform", "parentIndex": 0, "targetIndexes": [35, 81]}, {"directoryIndexes": [12], "name": "Analysis_RobotalgorithmstcpPositionMaintain", "parentIndex": 0, "targetIndexes": [38, 83]}, {"directoryIndexes": [13], "name": "Analysis_RobottestheaterApiTest", "parentIndex": 0, "targetIndexes": [41, 91]}, {"directoryIndexes": [14], "name": "Analysis_RobottestheatingMagneticStirrerDriver", "parentIndex": 0, "targetIndexes": [44, 92]}, {"directoryIndexes": [15], "name": "MJServerAPP", "parentIndex": 0, "targetIndexes": [47, 93]}, {"directoryIndexes": [16], "name": "MJServer_RefactorLibrary", "parentIndex": 0, "targetIndexes": [50, 95]}, {"directoryIndexes": [17], "name": "MJServer_RefactorApp", "parentIndex": 0, "targetIndexes": [53, 94]}, {"directoryIndexes": [18], "name": "MJServer_RefactorTestphase1_test", "parentIndex": 0, "targetIndexes": [56, 96]}, {"directoryIndexes": [19], "name": "MJServer_RefactorTestsimple_abb_client", "parentIndex": 0, "targetIndexes": [59, 97]}, {"directoryIndexes": [20], "name": "MJServer_RefactorTestsimple_feeder_client", "parentIndex": 0, "targetIndexes": [62, 98]}, {"directoryIndexes": [21], "name": "RoboticLaserMarkingAbbDriver", "parentIndex": 0, "targetIndexes": [65, 99]}, {"directoryIndexes": [22], "name": "RoboticLaserMarkingLicenseGenerator", "parentIndex": 0, "targetIndexes": [68, 100]}, {"directoryIndexes": [23], "name": "RoboticLaserMarkingRFIDDriver", "parentIndex": 0, "targetIndexes": [71, 101]}, {"directoryIndexes": [24], "name": "RoboticLaserMarkinglaserDriver", "parentIndex": 0, "targetIndexes": [74, 108]}, {"directoryIndexes": [25], "name": "RoboticLaserMarkingTestabbsocket", "parentIndex": 0, "targetIndexes": [77, 102]}, {"directoryIndexes": [26], "name": "RoboticLaserMarkingTestlaser", "parentIndex": 0, "targetIndexes": [6, 103]}, {"directoryIndexes": [27], "name": "RoboticLaserMarkingTestlaserUI", "parentIndex": 0, "targetIndexes": [10, 104]}, {"directoryIndexes": [28], "name": "RoboticLaserMarkingTestrfiddriver", "parentIndex": 0, "targetIndexes": [19, 105]}, {"directoryIndexes": [29], "name": "RoboticLaserMarkingTestrfidserver", "parentIndex": 0, "targetIndexes": [28, 106]}, {"directoryIndexes": [30], "name": "RoboticLaserMarkingUI", "parentIndex": 0, "targetIndexes": [37, 107]}, {"directoryIndexes": [31], "name": "RoboticLaserMarkinglaserDriverSim", "parentIndex": 0, "targetIndexes": [46, 109]}, {"directoryIndexes": [32], "name": "fuxicore", "parentIndex": 0, "targetIndexes": [55, 132]}, {"directoryIndexes": [33], "name": "hardwaredriverabbRobotDriver", "parentIndex": 0, "targetIndexes": [64, 141]}, {"directoryIndexes": [34], "name": "Testtest_abb_socket", "parentIndex": 0, "targetIndexes": [73, 110]}, {"directoryIndexes": [35], "name": "Testtest_config_manager", "parentIndex": 0, "targetIndexes": [7, 111]}, {"directoryIndexes": [36], "name": "Testtest_csv", "parentIndex": 0, "targetIndexes": [34, 112]}, {"directoryIndexes": [37], "name": "Testtest_event_listener", "parentIndex": 0, "targetIndexes": [61, 113]}, {"directoryIndexes": [38], "name": "Testtest_executor", "parentIndex": 0, "targetIndexes": [25, 114]}, {"directoryIndexes": [39], "name": "Testtest_executor_context", "parentIndex": 0, "targetIndexes": [79, 115]}, {"directoryIndexes": [40], "name": "Testtest_fa2204n_balance", "parentIndex": 0, "targetIndexes": [52, 116]}, {"directoryIndexes": [41], "name": "Testtest_fa2204n_balance_basic", "parentIndex": 0, "targetIndexes": [70, 117]}, {"directoryIndexes": [42], "name": "Testtest_fileutil", "parentIndex": 0, "targetIndexes": [43, 118]}, {"directoryIndexes": [43], "name": "Testtest_json", "parentIndex": 0, "targetIndexes": [16, 119]}, {"directoryIndexes": [44], "name": "Testtest_license_manager", "parentIndex": 0, "targetIndexes": [76, 120]}, {"directoryIndexes": [45], "name": "Testtest_license_ui", "parentIndex": 0, "targetIndexes": [67, 121]}, {"directoryIndexes": [46], "name": "test_micro_dosing", "parentIndex": 0, "targetIndexes": [58, 149]}, {"directoryIndexes": [47], "name": "Testtest_network", "parentIndex": 0, "targetIndexes": [49, 122]}, {"directoryIndexes": [48], "name": "Testtest_serial", "parentIndex": 0, "targetIndexes": [40, 123]}, {"directoryIndexes": [49], "name": "Testtest_service_container", "parentIndex": 0, "targetIndexes": [31, 124]}, {"directoryIndexes": [50], "name": "Testtest_socket", "parentIndex": 0, "targetIndexes": [11, 125]}, {"directoryIndexes": [51], "name": "Testtest_sqlite", "parentIndex": 0, "targetIndexes": [13, 126]}, {"directoryIndexes": [52], "name": "Testtest_taskflow", "parentIndex": 0, "targetIndexes": [4, 127]}, {"directoryIndexes": [53], "name": "Testtest_twoaixsrobot", "parentIndex": 0, "targetIndexes": [78, 128]}, {"directoryIndexes": [54], "name": "Testtest_xml", "parentIndex": 0, "targetIndexes": [75, 129]}, {"directoryIndexes": [55], "name": "hardwaredriverAuboArcsDriver", "parentIndex": 0, "targetIndexes": [72, 133]}, {"directoryIndexes": [56], "name": "hardwaredriverAuboDriver", "parentIndex": 0, "targetIndexes": [69, 134]}, {"directoryIndexes": [57], "name": "hardwaredriverElectricGripperDriver", "parentIndex": 0, "targetIndexes": [66, 135]}, {"directoryIndexes": [58], "name": "hardwaredriverHikVisionCamera", "parentIndex": 0, "targetIndexes": [63, 136]}, {"directoryIndexes": [59], "name": "hardwaredriverLabelPrinter", "parentIndex": 0, "targetIndexes": [60, 137]}, {"directoryIndexes": [60], "name": "hardwaredriverMettlerBalance", "parentIndex": 0, "targetIndexes": [57, 138]}, {"directoryIndexes": [61], "name": "hardwaredriverOpcDa", "parentIndex": 0, "targetIndexes": [54, 139]}, {"directoryIndexes": [62], "name": "hardwaredriverOpcUa", "parentIndex": 0, "targetIndexes": [51, 140]}, {"directoryIndexes": [63], "name": "hardwaredriversocket", "parentIndex": 0, "targetIndexes": [48, 147]}, {"directoryIndexes": [64], "name": "hardwaredriveragilerobotDriver", "parentIndex": 0, "targetIndexes": [45, 142]}, {"directoryIndexes": [65], "name": "hardwaredriverfairinoDriver", "parentIndex": 0, "targetIndexes": [3, 143]}, {"directoryIndexes": [66], "name": "hardwaredriverjunduoHandDriver", "parentIndex": 0, "targetIndexes": [2, 144]}, {"directoryIndexes": [67], "name": "hardwaredrivermodbus", "parentIndex": 0, "targetIndexes": [9, 145]}, {"directoryIndexes": [68], "name": "hardwaredriverserial", "parentIndex": 0, "targetIndexes": [12, 146]}, {"directoryIndexes": [69], "name": "hardwaredriverusbcamera", "parentIndex": 0, "targetIndexes": [30, 148]}, {"directoryIndexes": [70], "name": "toolcalbuild", "parentIndex": 0, "targetIndexes": [15, 150]}, {"directoryIndexes": [71], "name": "toolcameraCalibrator", "parentIndex": 0, "targetIndexes": [18, 152]}, {"directoryIndexes": [72], "name": "toolcaltest", "parentIndex": 0, "targetIndexes": [21, 151]}, {"directoryIndexes": [73], "name": "toolcommunication", "parentIndex": 0, "targetIndexes": [22, 153]}, {"directoryIndexes": [74], "name": "toolhandeyecal", "parentIndex": 0, "targetIndexes": [24, 154]}, {"directoryIndexes": [75], "name": "toolhandeyecaltest", "parentIndex": 0, "targetIndexes": [27, 155]}, {"directoryIndexes": [76], "name": "toolhandeyecaluihandeyecalui", "parentIndex": 0, "targetIndexes": [33, 156]}, {"directoryIndexes": [77], "name": "toolhandeyecaluipath", "parentIndex": 0, "targetIndexes": [36, 157]}, {"directoryIndexes": [78], "name": "toolhandeyecaluipathAuto", "parentIndex": 0, "targetIndexes": [39, 158]}, {"directoryIndexes": [79], "name": "toolverify_calibration", "parentIndex": 0, "targetIndexes": [42, 159]}], "targets": [{"directoryIndex": 3, "id": "ALL_BUILD::@19f706e88e1d43a9565c", "jsonFile": "target-ALL_BUILD-Release-85da0b39f38e7483b20e.json", "name": "ALL_BUILD", "projectIndex": 3}, {"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Release-342279978964b07ce418.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 66, "id": "ALL_BUILD::@a89b79f2a82dbe076976", "jsonFile": "target-ALL_BUILD-Release-40a7d901ed8e10a0c7f5.json", "name": "ALL_BUILD", "projectIndex": 66}, {"directoryIndex": 65, "id": "ALL_BUILD::@8a675cd9715b77cebac5", "jsonFile": "target-ALL_BUILD-Release-2e630e3e3d96ead79510.json", "name": "ALL_BUILD", "projectIndex": 65}, {"directoryIndex": 52, "id": "ALL_BUILD::@39116d767a15e3a891df", "jsonFile": "target-ALL_BUILD-Release-a7eeeef2e82f92727250.json", "name": "ALL_BUILD", "projectIndex": 52}, {"directoryIndex": 1, "id": "ALL_BUILD::@58335e9a86196d0a97e7", "jsonFile": "target-ALL_BUILD-Release-0841e45febd559571c13.json", "name": "ALL_BUILD", "projectIndex": 1}, {"directoryIndex": 26, "id": "ALL_BUILD::@fef5c779e5dd4e5a5c7c", "jsonFile": "target-ALL_BUILD-Release-68f406d96290d6b7df07.json", "name": "ALL_BUILD", "projectIndex": 26}, {"directoryIndex": 35, "id": "ALL_BUILD::@d885fae1c443095a1db7", "jsonFile": "target-ALL_BUILD-Release-2569c2c10fad6870c72b.json", "name": "ALL_BUILD", "projectIndex": 35}, {"directoryIndex": 2, "id": "ALL_BUILD::@07001b74ee4af3db8a6e", "jsonFile": "target-ALL_BUILD-Release-a93df49a17068820adac.json", "name": "ALL_BUILD", "projectIndex": 2}, {"directoryIndex": 67, "id": "ALL_BUILD::@2ef1e00ba9bcf504e7b3", "jsonFile": "target-ALL_BUILD-Release-29cfabbfeabe66f20220.json", "name": "ALL_BUILD", "projectIndex": 67}, {"directoryIndex": 27, "id": "ALL_BUILD::@7e6cec28b989a66fe139", "jsonFile": "target-ALL_BUILD-Release-07b7923a48da597b2076.json", "name": "ALL_BUILD", "projectIndex": 27}, {"directoryIndex": 50, "id": "ALL_BUILD::@6ff24ad6fb1c26ae0a57", "jsonFile": "target-ALL_BUILD-Release-5e2fbceb8d24c84e42cf.json", "name": "ALL_BUILD", "projectIndex": 50}, {"directoryIndex": 68, "id": "ALL_BUILD::@e813d8aa5825a18a8390", "jsonFile": "target-ALL_BUILD-Release-19c438a9df50cace71d5.json", "name": "ALL_BUILD", "projectIndex": 68}, {"directoryIndex": 51, "id": "ALL_BUILD::@e4ccddf7a4c29adf5bd6", "jsonFile": "target-ALL_BUILD-Release-119fa9189d354d4060ac.json", "name": "ALL_BUILD", "projectIndex": 51}, {"directoryIndex": 4, "id": "ALL_BUILD::@97966baa9ab9c14a9bcf", "jsonFile": "target-ALL_BUILD-Release-5339ba61d8ad5be7b663.json", "name": "ALL_BUILD", "projectIndex": 4}, {"directoryIndex": 70, "id": "ALL_BUILD::@a167bea24520843f7e43", "jsonFile": "target-ALL_BUILD-Release-da1d6472f79bdb30833d.json", "name": "ALL_BUILD", "projectIndex": 70}, {"directoryIndex": 43, "id": "ALL_BUILD::@46d5ce0aeb8e42b7284d", "jsonFile": "target-ALL_BUILD-Release-430b2635e5f4a69b8869.json", "name": "ALL_BUILD", "projectIndex": 43}, {"directoryIndex": 5, "id": "ALL_BUILD::@92995a2f85961e8f5b16", "jsonFile": "target-ALL_BUILD-Release-8982b17ad107170d8e83.json", "name": "ALL_BUILD", "projectIndex": 5}, {"directoryIndex": 71, "id": "ALL_BUILD::@51e97efefc2313866ad5", "jsonFile": "target-ALL_BUILD-Release-54994d3f173f21751830.json", "name": "ALL_BUILD", "projectIndex": 71}, {"directoryIndex": 28, "id": "ALL_BUILD::@1df6ab8cb0b8ddb593f8", "jsonFile": "target-ALL_BUILD-Release-c504476d8ceceb45d7c2.json", "name": "ALL_BUILD", "projectIndex": 28}, {"directoryIndex": 6, "id": "ALL_BUILD::@e9ece92fe2bc47be420b", "jsonFile": "target-ALL_BUILD-Release-c6d18cd0ab434516660b.json", "name": "ALL_BUILD", "projectIndex": 6}, {"directoryIndex": 72, "id": "ALL_BUILD::@9d9cd8ab08d8520a4ac3", "jsonFile": "target-ALL_BUILD-Release-3d4cfdff622930c380ca.json", "name": "ALL_BUILD", "projectIndex": 72}, {"directoryIndex": 73, "id": "ALL_BUILD::@116eb0f160f4d76de168", "jsonFile": "target-ALL_BUILD-Release-16899497fe6bfb496bf5.json", "name": "ALL_BUILD", "projectIndex": 73}, {"directoryIndex": 7, "id": "ALL_BUILD::@03c7a47b8090dea9b455", "jsonFile": "target-ALL_BUILD-Release-ffa581a59595a1d89428.json", "name": "ALL_BUILD", "projectIndex": 7}, {"directoryIndex": 74, "id": "ALL_BUILD::@d7390e83b7e4f79f633d", "jsonFile": "target-ALL_BUILD-Release-f7c053f7188b257ad6ab.json", "name": "ALL_BUILD", "projectIndex": 74}, {"directoryIndex": 38, "id": "ALL_BUILD::@7e2e321726c5f3e3edcb", "jsonFile": "target-ALL_BUILD-Release-c6bf30b5692cd0ba3e2e.json", "name": "ALL_BUILD", "projectIndex": 38}, {"directoryIndex": 8, "id": "ALL_BUILD::@3f043c5f38f013ef2115", "jsonFile": "target-ALL_BUILD-Release-a38675298dfaed7097d8.json", "name": "ALL_BUILD", "projectIndex": 8}, {"directoryIndex": 75, "id": "ALL_BUILD::@ae279e4383f26a866133", "jsonFile": "target-ALL_BUILD-Release-0eb479fa5fcd1bd5f128.json", "name": "ALL_BUILD", "projectIndex": 75}, {"directoryIndex": 29, "id": "ALL_BUILD::@75eb8879fc099b4640aa", "jsonFile": "target-ALL_BUILD-Release-999f14f3c47b505fa427.json", "name": "ALL_BUILD", "projectIndex": 29}, {"directoryIndex": 9, "id": "ALL_BUILD::@6b827d246feac3c35b9a", "jsonFile": "target-ALL_BUILD-Release-bf24b70e9943d8b96cbc.json", "name": "ALL_BUILD", "projectIndex": 9}, {"directoryIndex": 69, "id": "ALL_BUILD::@bfaa0a8775de30d870f0", "jsonFile": "target-ALL_BUILD-Release-9918016ebbb419e28d18.json", "name": "ALL_BUILD", "projectIndex": 69}, {"directoryIndex": 49, "id": "ALL_BUILD::@03373f949cbd329c961c", "jsonFile": "target-ALL_BUILD-Release-b9a988aed78fcad4eb7e.json", "name": "ALL_BUILD", "projectIndex": 49}, {"directoryIndex": 10, "id": "ALL_BUILD::@6ccf8425ca6a81980105", "jsonFile": "target-ALL_BUILD-Release-dad371b5f71f8a659af3.json", "name": "ALL_BUILD", "projectIndex": 10}, {"directoryIndex": 76, "id": "ALL_BUILD::@64c63141ea1fe7a116f6", "jsonFile": "target-ALL_BUILD-Release-1048f699b992ebfb0588.json", "name": "ALL_BUILD", "projectIndex": 76}, {"directoryIndex": 36, "id": "ALL_BUILD::@08c5adc7ee1d91091a97", "jsonFile": "target-ALL_BUILD-Release-ece8b323ed57b6f1fbe7.json", "name": "ALL_BUILD", "projectIndex": 36}, {"directoryIndex": 11, "id": "ALL_BUILD::@e0567cd60ef58755dd5b", "jsonFile": "target-ALL_BUILD-Release-7e71292cd8fb1d3f305b.json", "name": "ALL_BUILD", "projectIndex": 11}, {"directoryIndex": 77, "id": "ALL_BUILD::@f8ebbc87f7fac77328c8", "jsonFile": "target-ALL_BUILD-Release-2307048a7f0d82e4133d.json", "name": "ALL_BUILD", "projectIndex": 77}, {"directoryIndex": 30, "id": "ALL_BUILD::@4e1303897d180b86ab2f", "jsonFile": "target-ALL_BUILD-Release-5b129cd61e1e6e577f56.json", "name": "ALL_BUILD", "projectIndex": 30}, {"directoryIndex": 12, "id": "ALL_BUILD::@96a57770f6c6f4e493b3", "jsonFile": "target-ALL_BUILD-Release-6e1bc5ae653b03810b1e.json", "name": "ALL_BUILD", "projectIndex": 12}, {"directoryIndex": 78, "id": "ALL_BUILD::@f4fb3041b29f01391299", "jsonFile": "target-ALL_BUILD-Release-1f9beecd76424e38b6ac.json", "name": "ALL_BUILD", "projectIndex": 78}, {"directoryIndex": 48, "id": "ALL_BUILD::@a384ba46c8f7385844c3", "jsonFile": "target-ALL_BUILD-Release-585b87981061d80d6b32.json", "name": "ALL_BUILD", "projectIndex": 48}, {"directoryIndex": 13, "id": "ALL_BUILD::@6eec4415674bd20e6491", "jsonFile": "target-ALL_BUILD-Release-67d66f0bb28969695805.json", "name": "ALL_BUILD", "projectIndex": 13}, {"directoryIndex": 79, "id": "ALL_BUILD::@efca4bcc8ad294d52f3d", "jsonFile": "target-ALL_BUILD-Release-427747eec773300cb006.json", "name": "ALL_BUILD", "projectIndex": 79}, {"directoryIndex": 42, "id": "ALL_BUILD::@b13ebbd4a3aafa6a0363", "jsonFile": "target-ALL_BUILD-Release-b28ba7495172b5cff460.json", "name": "ALL_BUILD", "projectIndex": 42}, {"directoryIndex": 14, "id": "ALL_BUILD::@1bc5057501657c23b12e", "jsonFile": "target-ALL_BUILD-Release-44929f2f6ed2fa190302.json", "name": "ALL_BUILD", "projectIndex": 14}, {"directoryIndex": 64, "id": "ALL_BUILD::@14914dfd89874674d41d", "jsonFile": "target-ALL_BUILD-Release-901e1781755324a5fec6.json", "name": "ALL_BUILD", "projectIndex": 64}, {"directoryIndex": 31, "id": "ALL_BUILD::@30345e39cecb9bcc06b0", "jsonFile": "target-ALL_BUILD-Release-a2f4c2c3699b6d4e8f02.json", "name": "ALL_BUILD", "projectIndex": 31}, {"directoryIndex": 15, "id": "ALL_BUILD::@7c9daef8275400bf8ba5", "jsonFile": "target-ALL_BUILD-Release-8eb8385fb4710473c8b0.json", "name": "ALL_BUILD", "projectIndex": 15}, {"directoryIndex": 63, "id": "ALL_BUILD::@e58766abf91db77f862b", "jsonFile": "target-ALL_BUILD-Release-c49bfb773b8d9ae8a563.json", "name": "ALL_BUILD", "projectIndex": 63}, {"directoryIndex": 47, "id": "ALL_BUILD::@76c3a22b9f657d2ec026", "jsonFile": "target-ALL_BUILD-Release-73870bfe79e2590bf24b.json", "name": "ALL_BUILD", "projectIndex": 47}, {"directoryIndex": 16, "id": "ALL_BUILD::@8670365571700e12b583", "jsonFile": "target-ALL_BUILD-Release-f7583928953388d9577a.json", "name": "ALL_BUILD", "projectIndex": 16}, {"directoryIndex": 62, "id": "ALL_BUILD::@7bf30a519259482def19", "jsonFile": "target-ALL_BUILD-Release-7285147ac61a7f6b2475.json", "name": "ALL_BUILD", "projectIndex": 62}, {"directoryIndex": 40, "id": "ALL_BUILD::@121a4898e406881ffb23", "jsonFile": "target-ALL_BUILD-Release-9c608b9e07c5eb8aa8d3.json", "name": "ALL_BUILD", "projectIndex": 40}, {"directoryIndex": 17, "id": "ALL_BUILD::@f8e8ceb61b4f8c6d7034", "jsonFile": "target-ALL_BUILD-Release-242568ff82df40a13a70.json", "name": "ALL_BUILD", "projectIndex": 17}, {"directoryIndex": 61, "id": "ALL_BUILD::@a2142d788288f069154a", "jsonFile": "target-ALL_BUILD-Release-765fa3e850ca53bc5583.json", "name": "ALL_BUILD", "projectIndex": 61}, {"directoryIndex": 32, "id": "ALL_BUILD::@5cdc2d2ab21b3b9e72cd", "jsonFile": "target-ALL_BUILD-Release-f679fc8971fbb8230b83.json", "name": "ALL_BUILD", "projectIndex": 32}, {"directoryIndex": 18, "id": "ALL_BUILD::@0a3c2e809899f2f13f5a", "jsonFile": "target-ALL_BUILD-Release-0d2ad87deda7b2a9aa06.json", "name": "ALL_BUILD", "projectIndex": 18}, {"directoryIndex": 60, "id": "ALL_BUILD::@39b1645ff4c023a4e445", "jsonFile": "target-ALL_BUILD-Release-084b090b7dc16868a8bc.json", "name": "ALL_BUILD", "projectIndex": 60}, {"directoryIndex": 46, "id": "ALL_BUILD::@65e3165a8812532710ef", "jsonFile": "target-ALL_BUILD-Release-26611f28379a7309f899.json", "name": "ALL_BUILD", "projectIndex": 46}, {"directoryIndex": 19, "id": "ALL_BUILD::@933176848578d8c440c9", "jsonFile": "target-ALL_BUILD-Release-c48b3bb23f187ae9ce55.json", "name": "ALL_BUILD", "projectIndex": 19}, {"directoryIndex": 59, "id": "ALL_BUILD::@a99f8207d5ede56c5cae", "jsonFile": "target-ALL_BUILD-Release-9c0ab327cff663eb592d.json", "name": "ALL_BUILD", "projectIndex": 59}, {"directoryIndex": 37, "id": "ALL_BUILD::@889b7d31514c76e85624", "jsonFile": "target-ALL_BUILD-Release-46891ee6e417731d3bb2.json", "name": "ALL_BUILD", "projectIndex": 37}, {"directoryIndex": 20, "id": "ALL_BUILD::@80cf03468317b5d7fe2b", "jsonFile": "target-ALL_BUILD-Release-9a67b5f1091c8b72c93d.json", "name": "ALL_BUILD", "projectIndex": 20}, {"directoryIndex": 58, "id": "ALL_BUILD::@bc252bb14595a0f09d26", "jsonFile": "target-ALL_BUILD-Release-0d333457689a8657f8bb.json", "name": "ALL_BUILD", "projectIndex": 58}, {"directoryIndex": 33, "id": "ALL_BUILD::@ccffbf515659b480dabe", "jsonFile": "target-ALL_BUILD-Release-d029dae30f7abd04202a.json", "name": "ALL_BUILD", "projectIndex": 33}, {"directoryIndex": 21, "id": "ALL_BUILD::@9c3ce50b53cc3ff6eff5", "jsonFile": "target-ALL_BUILD-Release-2bdca0b9ff24f6069f6f.json", "name": "ALL_BUILD", "projectIndex": 21}, {"directoryIndex": 57, "id": "ALL_BUILD::@e336ced093e233e6d829", "jsonFile": "target-ALL_BUILD-Release-2490e630ef488d559bda.json", "name": "ALL_BUILD", "projectIndex": 57}, {"directoryIndex": 45, "id": "ALL_BUILD::@bb78083dcad0a236858d", "jsonFile": "target-ALL_BUILD-Release-a4715af1c837cd3cfb57.json", "name": "ALL_BUILD", "projectIndex": 45}, {"directoryIndex": 22, "id": "ALL_BUILD::@59f7d9ae5c13d347c5f4", "jsonFile": "target-ALL_BUILD-Release-55c4b70ff406511ddf93.json", "name": "ALL_BUILD", "projectIndex": 22}, {"directoryIndex": 56, "id": "ALL_BUILD::@e75f830eb736a5dca1ce", "jsonFile": "target-ALL_BUILD-Release-d9762c58eea40fa5b0b3.json", "name": "ALL_BUILD", "projectIndex": 56}, {"directoryIndex": 41, "id": "ALL_BUILD::@1dc2f4735d896dd76909", "jsonFile": "target-ALL_BUILD-Release-929fe4cbdbeb7ddf242d.json", "name": "ALL_BUILD", "projectIndex": 41}, {"directoryIndex": 23, "id": "ALL_BUILD::@d1520424919af3a40272", "jsonFile": "target-ALL_BUILD-Release-6f3f47d4c763e4b65f51.json", "name": "ALL_BUILD", "projectIndex": 23}, {"directoryIndex": 55, "id": "ALL_BUILD::@fd5e493b37d2bab880d9", "jsonFile": "target-ALL_BUILD-Release-603c76eaf26e2c4621cd.json", "name": "ALL_BUILD", "projectIndex": 55}, {"directoryIndex": 34, "id": "ALL_BUILD::@d803dad5c2b28052d845", "jsonFile": "target-ALL_BUILD-Release-3f8d66d1990c24f51ece.json", "name": "ALL_BUILD", "projectIndex": 34}, {"directoryIndex": 24, "id": "ALL_BUILD::@cf8a855e37e415d7ca08", "jsonFile": "target-ALL_BUILD-Release-4072b617fa6d0b547136.json", "name": "ALL_BUILD", "projectIndex": 24}, {"directoryIndex": 54, "id": "ALL_BUILD::@80c0713ae5ba495463b6", "jsonFile": "target-ALL_BUILD-Release-f6c6c0ef380fb6385d06.json", "name": "ALL_BUILD", "projectIndex": 54}, {"directoryIndex": 44, "id": "ALL_BUILD::@7a0ade4671e16056f257", "jsonFile": "target-ALL_BUILD-Release-446feef9c15e67387422.json", "name": "ALL_BUILD", "projectIndex": 44}, {"directoryIndex": 25, "id": "ALL_BUILD::@bca145e2342aef659032", "jsonFile": "target-ALL_BUILD-Release-eb32980119712b6b14dc.json", "name": "ALL_BUILD", "projectIndex": 25}, {"directoryIndex": 53, "id": "ALL_BUILD::@7d9d822efa235ac321e6", "jsonFile": "target-ALL_BUILD-Release-382e2efa87d6a17ef3e9.json", "name": "ALL_BUILD", "projectIndex": 53}, {"directoryIndex": 39, "id": "ALL_BUILD::@e99d12ef8be33386882a", "jsonFile": "target-ALL_BUILD-Release-d89f761cec3c2e760907.json", "name": "ALL_BUILD", "projectIndex": 39}, {"directoryIndex": 10, "id": "Analysis_RobotApp::@6ccf8425ca6a81980105", "jsonFile": "target-Analysis_RobotApp-Release-2939a49895583d9096c9.json", "name": "Analysis_RobotApp", "projectIndex": 10}, {"directoryIndex": 11, "id": "Analysis_RobotalgorithmscoordinateTransform::@e0567cd60ef58755dd5b", "jsonFile": "target-Analysis_RobotalgorithmscoordinateTransform-Release-084d22aece68c3473685.json", "name": "Analysis_RobotalgorithmscoordinateTransform", "projectIndex": 11}, {"directoryIndex": 2, "id": "Analysis_RobotalgorithmspouringControl::@07001b74ee4af3db8a6e", "jsonFile": "target-Analysis_RobotalgorithmspouringControl-Release-d5b2bd841a86c9ae7c98.json", "name": "Analysis_RobotalgorithmspouringControl", "projectIndex": 2}, {"directoryIndex": 12, "id": "Analysis_RobotalgorithmstcpPositionMaintain::@96a57770f6c6f4e493b3", "jsonFile": "target-Analysis_RobotalgorithmstcpPositionMaintain-Release-932f89bc7a0163cf88e1.json", "name": "Analysis_RobotalgorithmstcpPositionMaintain", "projectIndex": 12}, {"directoryIndex": 3, "id": "Analysis_RobotdriversaixsDriver::@19f706e88e1d43a9565c", "jsonFile": "target-Analysis_RobotdriversaixsDriver-Release-f0aa899b77f2ef4c0558.json", "name": "Analysis_RobotdriversaixsDriver", "projectIndex": 3}, {"directoryIndex": 5, "id": "Analysis_RobotdriversbalanceDriver::@92995a2f85961e8f5b16", "jsonFile": "target-Analysis_RobotdriversbalanceDriver-Release-428d2b20f28442178f9e.json", "name": "Analysis_RobotdriversbalanceDriver", "projectIndex": 5}, {"directoryIndex": 6, "id": "Analysis_RobotdriversheatingMagneticStirrerDriver::@e9ece92fe2bc47be420b", "jsonFile": "target-Analysis_RobotdriversheatingMagneticStirrerDriver-Release-28e56f71ef61789faba1.json", "name": "Analysis_RobotdriversheatingMagneticStirrerDriver", "projectIndex": 6}, {"directoryIndex": 7, "id": "Analysis_RobotdriversmoistureAnalyzerDriver::@03c7a47b8090dea9b455", "jsonFile": "target-Analysis_RobotdriversmoistureAnalyzerDriver-Release-8994646a0beef6e2ed90.json", "name": "Analysis_RobotdriversmoistureAnalyzerDriver", "projectIndex": 7}, {"directoryIndex": 4, "id": "Analysis_RobotdriversplcDriver::@97966baa9ab9c14a9bcf", "jsonFile": "target-Analysis_RobotdriversplcDriver-Release-ee8f7cd1c753e799cb39.json", "name": "Analysis_RobotdriversplcDriver", "projectIndex": 4}, {"directoryIndex": 9, "id": "Analysis_RobotdriversrestInterfaceDriver::@6b827d246feac3c35b9a", "jsonFile": "target-Analysis_RobotdriversrestInterfaceDriver-Release-d5cb6280577bd43585b7.json", "name": "Analysis_RobotdriversrestInterfaceDriver", "projectIndex": 9}, {"directoryIndex": 8, "id": "Analysis_RobotdriversrobotDriver::@3f043c5f38f013ef2115", "jsonFile": "target-Analysis_RobotdriversrobotDriver-Release-7408408628bf8de88434.json", "name": "Analysis_RobotdriversrobotDriver", "projectIndex": 8}, {"directoryIndex": 13, "id": "Analysis_RobottestheaterApiTest::@6eec4415674bd20e6491", "jsonFile": "target-Analysis_RobottestheaterApiTest-Release-eeea5d412a52d1c5fad6.json", "name": "Analysis_RobottestheaterApiTest", "projectIndex": 13}, {"directoryIndex": 14, "id": "Analysis_RobottestheatingMagneticStirrerDriver::@1bc5057501657c23b12e", "jsonFile": "target-Analysis_RobottestheatingMagneticStirrerDriver-Release-c754c9f746da0f62e24f.json", "name": "Analysis_RobottestheatingMagneticStirrerDriver", "projectIndex": 14}, {"directoryIndex": 15, "id": "MJServerAPP::@7c9daef8275400bf8ba5", "jsonFile": "target-MJServerAPP-Release-05b109e06dcf3ab412ee.json", "name": "MJServerAPP", "projectIndex": 15}, {"directoryIndex": 17, "id": "MJServer_RefactorApp::@f8e8ceb61b4f8c6d7034", "jsonFile": "target-MJServer_RefactorApp-Release-82280416b33aa1a8fd1a.json", "name": "MJServer_RefactorApp", "projectIndex": 17}, {"directoryIndex": 16, "id": "MJServer_RefactorLibrary::@8670365571700e12b583", "jsonFile": "target-MJServer_RefactorLibrary-Release-d5b7e6f75cada99ed7f6.json", "name": "MJServer_RefactorLibrary", "projectIndex": 16}, {"directoryIndex": 18, "id": "MJServer_RefactorTestphase1_test::@0a3c2e809899f2f13f5a", "jsonFile": "target-MJServer_RefactorTestphase1_test-Release-87cbeaa53fa6ebfc4ce7.json", "name": "MJServer_RefactorTestphase1_test", "projectIndex": 18}, {"directoryIndex": 19, "id": "MJServer_RefactorTestsimple_abb_client::@933176848578d8c440c9", "jsonFile": "target-MJServer_RefactorTestsimple_abb_client-Release-3624d94c8b43c8b8f7a8.json", "name": "MJServer_RefactorTestsimple_abb_client", "projectIndex": 19}, {"directoryIndex": 20, "id": "MJServer_RefactorTestsimple_feeder_client::@80cf03468317b5d7fe2b", "jsonFile": "target-MJServer_RefactorTestsimple_feeder_client-Release-d7f02dd92543b1e37d8a.json", "name": "MJServer_RefactorTestsimple_feeder_client", "projectIndex": 20}, {"directoryIndex": 21, "id": "RoboticLaserMarkingAbbDriver::@9c3ce50b53cc3ff6eff5", "jsonFile": "target-RoboticLaserMarkingAbbDriver-Release-c97adb34fb3b5c1cfb7b.json", "name": "RoboticLaserMarkingAbbDriver", "projectIndex": 21}, {"directoryIndex": 22, "id": "RoboticLaserMarkingLicenseGenerator::@59f7d9ae5c13d347c5f4", "jsonFile": "target-RoboticLaserMarkingLicenseGenerator-Release-03fbe3948da022ea77ae.json", "name": "RoboticLaserMarkingLicenseGenerator", "projectIndex": 22}, {"directoryIndex": 23, "id": "RoboticLaserMarkingRFIDDriver::@d1520424919af3a40272", "jsonFile": "target-RoboticLaserMarkingRFIDDriver-Release-c8d605402868f664c2a4.json", "name": "RoboticLaserMarkingRFIDDriver", "projectIndex": 23}, {"directoryIndex": 25, "id": "RoboticLaserMarkingTestabbsocket::@bca145e2342aef659032", "jsonFile": "target-RoboticLaserMarkingTestabbsocket-Release-d14d55e29ac818a85772.json", "name": "RoboticLaserMarkingTestabbsocket", "projectIndex": 25}, {"directoryIndex": 26, "id": "RoboticLaserMarkingTestlaser::@fef5c779e5dd4e5a5c7c", "jsonFile": "target-RoboticLaserMarkingTestlaser-Release-ace2d94e2d18dfc331b9.json", "name": "RoboticLaserMarkingTestlaser", "projectIndex": 26}, {"directoryIndex": 27, "id": "RoboticLaserMarkingTestlaserUI::@7e6cec28b989a66fe139", "jsonFile": "target-RoboticLaserMarkingTestlaserUI-Release-eca9d77bfad22ba5d026.json", "name": "RoboticLaserMarkingTestlaserUI", "projectIndex": 27}, {"directoryIndex": 28, "id": "RoboticLaserMarkingTestrfiddriver::@1df6ab8cb0b8ddb593f8", "jsonFile": "target-RoboticLaserMarkingTestrfiddriver-Release-35247cf3139e08d673ef.json", "name": "RoboticLaserMarkingTestrfiddriver", "projectIndex": 28}, {"directoryIndex": 29, "id": "RoboticLaserMarkingTestrfidserver::@75eb8879fc099b4640aa", "jsonFile": "target-RoboticLaserMarkingTestrfidserver-Release-e5c9a708913b1e0fcc51.json", "name": "RoboticLaserMarkingTestrfidserver", "projectIndex": 29}, {"directoryIndex": 30, "id": "RoboticLaserMarkingUI::@4e1303897d180b86ab2f", "jsonFile": "target-RoboticLaserMarkingUI-Release-cc5836c2209f976c18b2.json", "name": "RoboticLaserMarkingUI", "projectIndex": 30}, {"directoryIndex": 24, "id": "RoboticLaserMarkinglaserDriver::@cf8a855e37e415d7ca08", "jsonFile": "target-RoboticLaserMarkinglaserDriver-Release-5e6bef6f8fd932300f7b.json", "name": "RoboticLaserMarkinglaserDriver", "projectIndex": 24}, {"directoryIndex": 31, "id": "RoboticLaserMarkinglaserDriverSim::@30345e39cecb9bcc06b0", "jsonFile": "target-RoboticLaserMarkinglaserDriverSim-Release-929c0e489d5f98d6b04f.json", "name": "RoboticLaserMarkinglaserDriverSim", "projectIndex": 31}, {"directoryIndex": 34, "id": "Testtest_abb_socket::@d803dad5c2b28052d845", "jsonFile": "target-Testtest_abb_socket-Release-cffbff8d69ada716c6c5.json", "name": "Testtest_abb_socket", "projectIndex": 34}, {"directoryIndex": 35, "id": "Testtest_config_manager::@d885fae1c443095a1db7", "jsonFile": "target-Testtest_config_manager-Release-bd5bc6741946361846e5.json", "name": "Testtest_config_manager", "projectIndex": 35}, {"directoryIndex": 36, "id": "Testtest_csv::@08c5adc7ee1d91091a97", "jsonFile": "target-Testtest_csv-Release-e80e704c1b06a13a5cef.json", "name": "Testtest_csv", "projectIndex": 36}, {"directoryIndex": 37, "id": "Testtest_event_listener::@889b7d31514c76e85624", "jsonFile": "target-Testtest_event_listener-Release-c97b678dff3fb5f26385.json", "name": "Testtest_event_listener", "projectIndex": 37}, {"directoryIndex": 38, "id": "Testtest_executor::@7e2e321726c5f3e3edcb", "jsonFile": "target-Testtest_executor-Release-d363a3c9dcf1a6f984ab.json", "name": "Testtest_executor", "projectIndex": 38}, {"directoryIndex": 39, "id": "Testtest_executor_context::@e99d12ef8be33386882a", "jsonFile": "target-Testtest_executor_context-Release-5e095d1584fca75319ba.json", "name": "Testtest_executor_context", "projectIndex": 39}, {"directoryIndex": 40, "id": "Testtest_fa2204n_balance::@121a4898e406881ffb23", "jsonFile": "target-Testtest_fa2204n_balance-Release-83b13c7cac54b64b1dde.json", "name": "Testtest_fa2204n_balance", "projectIndex": 40}, {"directoryIndex": 41, "id": "Testtest_fa2204n_balance_basic::@1dc2f4735d896dd76909", "jsonFile": "target-Testtest_fa2204n_balance_basic-Release-f6c659d9b5c5a2ef8c8a.json", "name": "Testtest_fa2204n_balance_basic", "projectIndex": 41}, {"directoryIndex": 42, "id": "Testtest_fileutil::@b13ebbd4a3aafa6a0363", "jsonFile": "target-Testtest_fileutil-Release-8602c19c625b559c46e5.json", "name": "Testtest_fileutil", "projectIndex": 42}, {"directoryIndex": 43, "id": "Testtest_json::@46d5ce0aeb8e42b7284d", "jsonFile": "target-Testtest_json-Release-595cacdfe10a9d5fbff4.json", "name": "Testtest_json", "projectIndex": 43}, {"directoryIndex": 44, "id": "Testtest_license_manager::@7a0ade4671e16056f257", "jsonFile": "target-Testtest_license_manager-Release-f13abf23db25712d7bdd.json", "name": "Testtest_license_manager", "projectIndex": 44}, {"directoryIndex": 45, "id": "Testtest_license_ui::@bb78083dcad0a236858d", "jsonFile": "target-Testtest_license_ui-Release-da1f15f8f24b26247520.json", "name": "Testtest_license_ui", "projectIndex": 45}, {"directoryIndex": 47, "id": "Testtest_network::@76c3a22b9f657d2ec026", "jsonFile": "target-Testtest_network-Release-5da62d6ab71447193b5b.json", "name": "Testtest_network", "projectIndex": 47}, {"directoryIndex": 48, "id": "Testtest_serial::@a384ba46c8f7385844c3", "jsonFile": "target-Testtest_serial-Release-096ef25cad7d55a76cb0.json", "name": "Testtest_serial", "projectIndex": 48}, {"directoryIndex": 49, "id": "Testtest_service_container::@03373f949cbd329c961c", "jsonFile": "target-Testtest_service_container-Release-15510b835ef1c8e90bea.json", "name": "Testtest_service_container", "projectIndex": 49}, {"directoryIndex": 50, "id": "Testtest_socket::@6ff24ad6fb1c26ae0a57", "jsonFile": "target-Testtest_socket-Release-38ddc0dced56019e4f12.json", "name": "Testtest_socket", "projectIndex": 50}, {"directoryIndex": 51, "id": "Testtest_sqlite::@e4ccddf7a4c29adf5bd6", "jsonFile": "target-Testtest_sqlite-Release-8679654066b9deb022e4.json", "name": "Testtest_sqlite", "projectIndex": 51}, {"directoryIndex": 52, "id": "Testtest_taskflow::@39116d767a15e3a891df", "jsonFile": "target-Testtest_taskflow-Release-63ca7ab03a3618004bfa.json", "name": "Testtest_taskflow", "projectIndex": 52}, {"directoryIndex": 53, "id": "Testtest_twoaixsrobot::@7d9d822efa235ac321e6", "jsonFile": "target-Testtest_twoaixsrobot-Release-9465984137407a53b820.json", "name": "Testtest_twoaixsrobot", "projectIndex": 53}, {"directoryIndex": 54, "id": "Testtest_xml::@80c0713ae5ba495463b6", "jsonFile": "target-Testtest_xml-Release-7d98857a821668d03fc8.json", "name": "Testtest_xml", "projectIndex": 54}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Release-1ca4f1b2e21860d5885e.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 1, "id": "fuxicommon::@58335e9a86196d0a97e7", "jsonFile": "target-fuxicommon-Release-893c2a8474cd017989f8.json", "name": "fuxicommon", "projectIndex": 1}, {"directoryIndex": 32, "id": "fuxicore::@5cdc2d2ab21b3b9e72cd", "jsonFile": "target-fuxicore-Release-146f061111b9fc14df45.json", "name": "fuxicore", "projectIndex": 32}, {"directoryIndex": 55, "id": "hardwaredriverAuboArcsDriver::@fd5e493b37d2bab880d9", "jsonFile": "target-hardwaredriverAuboArcsDriver-Release-5810ca2929e098df2d9c.json", "name": "hardwaredriverAuboArcsDriver", "projectIndex": 55}, {"directoryIndex": 56, "id": "hardwaredriverAuboDriver::@e75f830eb736a5dca1ce", "jsonFile": "target-hardwaredriverAuboDriver-Release-966c8a01fdd89bdd8bb4.json", "name": "hardwaredriverAuboDriver", "projectIndex": 56}, {"directoryIndex": 57, "id": "hardwaredriverElectricGripperDriver::@e336ced093e233e6d829", "jsonFile": "target-hardwaredriverElectricGripperDriver-Release-e436aee86e9e3826c2a5.json", "name": "hardwaredriverElectricGripperDriver", "projectIndex": 57}, {"directoryIndex": 58, "id": "hardwaredriverHikVisionCamera::@bc252bb14595a0f09d26", "jsonFile": "target-hardwaredriverHikVisionCamera-Release-199a563cb206e3cb85e9.json", "name": "hardwaredriverHikVisionCamera", "projectIndex": 58}, {"directoryIndex": 59, "id": "hardwaredriverLabelPrinter::@a99f8207d5ede56c5cae", "jsonFile": "target-hardwaredriverLabelPrinter-Release-64b50525e502ec01bedb.json", "name": "hardwaredriverLabelPrinter", "projectIndex": 59}, {"directoryIndex": 60, "id": "hardwaredriverMettlerBalance::@39b1645ff4c023a4e445", "jsonFile": "target-hardwaredriverMettlerBalance-Release-4a316414ab97b9b7b25e.json", "name": "hardwaredriverMettlerBalance", "projectIndex": 60}, {"directoryIndex": 61, "id": "hardwaredriverOpcDa::@a2142d788288f069154a", "jsonFile": "target-hardwaredriverOpcDa-Release-db30cad099224fadeb16.json", "name": "hardwaredriverOpcDa", "projectIndex": 61}, {"directoryIndex": 62, "id": "hardwaredriverOpcUa::@7bf30a519259482def19", "jsonFile": "target-hardwaredriverOpcUa-Release-52434bbb2907d6f75ffe.json", "name": "hardwaredriverOpcUa", "projectIndex": 62}, {"directoryIndex": 33, "id": "hardwaredriverabbRobotDriver::@ccffbf515659b480dabe", "jsonFile": "target-hardwaredriverabbRobotDriver-Release-c6d3b4be3c2684049698.json", "name": "hardwaredriverabbRobotDriver", "projectIndex": 33}, {"directoryIndex": 64, "id": "hardwaredriveragilerobotDriver::@14914dfd89874674d41d", "jsonFile": "target-hardwaredriveragilerobotDriver-Release-34bc3a7d93dbf9379ac8.json", "name": "hardwaredriveragilerobotDriver", "projectIndex": 64}, {"directoryIndex": 65, "id": "hardwaredriverfairinoDriver::@8a675cd9715b77cebac5", "jsonFile": "target-hardwaredriverfairinoDriver-Release-b006dcbbd3c25c6fea0d.json", "name": "hardwaredriverfairinoDriver", "projectIndex": 65}, {"directoryIndex": 66, "id": "hardwaredriverjunduoHandDriver::@a89b79f2a82dbe076976", "jsonFile": "target-hardwaredriverjunduoHandDriver-Release-9b6cdb5670c87aa74560.json", "name": "hardwaredriverjunduoHandDriver", "projectIndex": 66}, {"directoryIndex": 67, "id": "hardwaredrivermodbus::@2ef1e00ba9bcf504e7b3", "jsonFile": "target-hardwaredrivermodbus-Release-1e8067720d9f79b865b3.json", "name": "hardwaredrivermodbus", "projectIndex": 67}, {"directoryIndex": 68, "id": "hardwaredriverserial::@e813d8aa5825a18a8390", "jsonFile": "target-hardwaredriverserial-Release-dfe32e6339f83d887c0f.json", "name": "hardwaredriverserial", "projectIndex": 68}, {"directoryIndex": 63, "id": "hardwaredriversocket::@e58766abf91db77f862b", "jsonFile": "target-hardwaredriversocket-Release-7e731e85912215b89b7b.json", "name": "hardwaredriversocket", "projectIndex": 63}, {"directoryIndex": 69, "id": "hardwaredriverusbcamera::@bfaa0a8775de30d870f0", "jsonFile": "target-hardwaredriverusbcamera-Release-6fc9540ea5cc534cb90a.json", "name": "hardwaredriverusbcamera", "projectIndex": 69}, {"directoryIndex": 46, "id": "test_micro_dosing::@65e3165a8812532710ef", "jsonFile": "target-test_micro_dosing-Release-35a5f0fa6968864334b1.json", "name": "test_micro_dosing", "projectIndex": 46}, {"directoryIndex": 70, "id": "toolcalbuild::@a167bea24520843f7e43", "jsonFile": "target-toolcalbuild-Release-226f9e43b99d04035637.json", "name": "toolcalbuild", "projectIndex": 70}, {"directoryIndex": 72, "id": "toolcaltest::@9d9cd8ab08d8520a4ac3", "jsonFile": "target-toolcaltest-Release-08769993f95a309df487.json", "name": "toolcaltest", "projectIndex": 72}, {"directoryIndex": 71, "id": "toolcameraCalibrator::@51e97efefc2313866ad5", "jsonFile": "target-toolcameraCalibrator-Release-b27c2b8db2d202167a69.json", "name": "toolcameraCalibrator", "projectIndex": 71}, {"directoryIndex": 73, "id": "toolcommunication::@116eb0f160f4d76de168", "jsonFile": "target-toolcommunication-Release-293d1ec0c5b6d5b363e2.json", "name": "toolcommunication", "projectIndex": 73}, {"directoryIndex": 74, "id": "toolhandeyecal::@d7390e83b7e4f79f633d", "jsonFile": "target-toolhandeyecal-Release-7365b5184775e9aaa87a.json", "name": "toolhandeyecal", "projectIndex": 74}, {"directoryIndex": 75, "id": "toolhandeyecaltest::@ae279e4383f26a866133", "jsonFile": "target-toolhandeyecaltest-Release-38f327e00ad88cc58889.json", "name": "toolhandeyecaltest", "projectIndex": 75}, {"directoryIndex": 76, "id": "toolhandeyecaluihandeyecalui::@64c63141ea1fe7a116f6", "jsonFile": "target-toolhandeyecaluihandeyecalui-Release-c0efdce79375b6bb5f84.json", "name": "toolhandeyecaluihandeyecalui", "projectIndex": 76}, {"directoryIndex": 77, "id": "toolhandeyecaluipath::@f8ebbc87f7fac77328c8", "jsonFile": "target-toolhandeyecaluipath-Release-6aec6bce4708a509059d.json", "name": "toolhandeyecaluipath", "projectIndex": 77}, {"directoryIndex": 78, "id": "toolhandeyecaluipathAuto::@f4fb3041b29f01391299", "jsonFile": "target-toolhandeyecaluipathAuto-Release-81b06e085a352bfb3225.json", "name": "toolhandeyecaluipathAuto", "projectIndex": 78}, {"directoryIndex": 79, "id": "toolverify_calibration::@efca4bcc8ad294d52f3d", "jsonFile": "target-toolverify_calibration-Release-5792246a09988e99c376.json", "name": "toolverify_calibration", "projectIndex": 79}]}, {"directories": [{"build": ".", "childIndexes": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79], "hasInstallRule": true, "jsonFile": "directory-.-MinSizeRel-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [1, 130]}, {"build": "fuxicommon", "hasInstallRule": true, "jsonFile": "directory-fuxicommon-MinSizeRel-45b01aad2fc6492274cb.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 1, "source": "fuxicommon", "targetIndexes": [5, 131]}, {"build": "Analysis_Robot/algorithms/pouringControl", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.algorithms.pouringControl-MinSizeRel-b6a4a096167825bdc477.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 2, "source": "Analysis_Robot/algorithms/pouringControl", "targetIndexes": [8, 82]}, {"build": "Analysis_Robot/drivers/aixsDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.aixsDriver-MinSizeRel-acad4faf4f883a05b16c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 3, "source": "Analysis_Robot/drivers/aixsDriver", "targetIndexes": [0, 84]}, {"build": "Analysis_Robot/drivers/plcDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.plcDriver-MinSizeRel-1785bcd9213080dbd037.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 4, "source": "Analysis_Robot/drivers/plcDriver", "targetIndexes": [14, 88]}, {"build": "Analysis_Robot/drivers/balanceDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.balanceDriver-MinSizeRel-bd88b671d17006b62362.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 5, "source": "Analysis_Robot/drivers/balanceDriver", "targetIndexes": [17, 85]}, {"build": "Analysis_Robot/drivers/heatingMagneticStirrerDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.heatingMagneticStirrerDriver-MinSizeRel-58f3ac9f2a5c9e15f72a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 6, "source": "Analysis_Robot/drivers/heatingMagneticStirrerDriver", "targetIndexes": [20, 86]}, {"build": "Analysis_Robot/drivers/moistureAnalyzerDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.moistureAnalyzerDriver-MinSizeRel-013e15aafee981d8281c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 7, "source": "Analysis_Robot/drivers/moistureAnalyzerDriver", "targetIndexes": [23, 87]}, {"build": "Analysis_Robot/drivers/robotDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.robotDriver-MinSizeRel-2c83fb91a80b187dc6d1.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 8, "source": "Analysis_Robot/drivers/robotDriver", "targetIndexes": [26, 90]}, {"build": "Analysis_Robot/drivers/restInterfaceDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.restInterfaceDriver-MinSizeRel-32537dba61e43ea53bc5.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 9, "source": "Analysis_Robot/drivers/restInterfaceDriver", "targetIndexes": [29, 89]}, {"build": "Analysis_Robot/App", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.App-MinSizeRel-e2ede17c1187489a25e9.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 10, "source": "Analysis_Robot/App", "targetIndexes": [32, 80]}, {"build": "Analysis_Robot/algorithms/coordinateTransform", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.algorithms.coordinateTransform-MinSizeRel-0dc862cc663c92448147.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 11, "source": "Analysis_Robot/algorithms/coordinateTransform", "targetIndexes": [35, 81]}, {"build": "Analysis_Robot/algorithms/tcpPositionMaintain", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.algorithms.tcpPositionMaintain-MinSizeRel-14eb9bf209fc848d31d1.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 12, "source": "Analysis_Robot/algorithms/tcpPositionMaintain", "targetIndexes": [38, 83]}, {"build": "Analysis_Robot/test/heaterApiTest", "jsonFile": "directory-Analysis_Robot.test.heaterApiTest-MinSizeRel-b999f5688c4067f4e68d.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 13, "source": "Analysis_Robot/test/heaterApiTest", "targetIndexes": [41, 91]}, {"build": "Analysis_Robot/test/heatingMagneticStirrerDriver", "jsonFile": "directory-Analysis_Robot.test.heatingMagneticStirrerDriver-MinSizeRel-e5cbd850de4545ce6cdf.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 14, "source": "Analysis_Robot/test/heatingMagneticStirrerDriver", "targetIndexes": [44, 92]}, {"build": "MJServer/APP", "hasInstallRule": true, "jsonFile": "directory-MJServer.APP-MinSizeRel-9a0005353ac4d44a8cb8.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 15, "source": "MJServer/APP", "targetIndexes": [47, 93]}, {"build": "MJServer_Refactor/Library", "hasInstallRule": true, "jsonFile": "directory-MJServer_Refactor.Library-MinSizeRel-9ec6a9fe118a63a425b3.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 16, "source": "MJServer_Refactor/Library", "targetIndexes": [50, 95]}, {"build": "MJServer_Refactor/App", "jsonFile": "directory-MJServer_Refactor.App-MinSizeRel-25c2817210280de79843.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 17, "source": "MJServer_Refactor/App", "targetIndexes": [53, 94]}, {"build": "MJServer_Refactor/Test/phase1_test", "jsonFile": "directory-MJServer_Refactor.Test.phase1_test-MinSizeRel-0a76c9c92d103a235c64.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 18, "source": "MJServer_Refactor/Test/phase1_test", "targetIndexes": [56, 96]}, {"build": "MJServer_Refactor/Test/simple_abb_client", "jsonFile": "directory-MJServer_Refactor.Test.simple_abb_client-MinSizeRel-d55604710c3a3fb8f0bb.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 19, "source": "MJServer_Refactor/Test/simple_abb_client", "targetIndexes": [59, 97]}, {"build": "MJServer_Refactor/Test/simple_feeder_client", "jsonFile": "directory-MJServer_Refactor.Test.simple_feeder_client-MinSizeRel-615291d1f0d9de4ac87c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 20, "source": "MJServer_Refactor/Test/simple_feeder_client", "targetIndexes": [62, 98]}, {"build": "RoboticLaserMarking/AbbDriver", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.AbbDriver-MinSizeRel-6a2fe08206f932672311.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 21, "source": "RoboticLaserMarking/AbbDriver", "targetIndexes": [65, 99]}, {"build": "RoboticLaserMarking/LicenseGenerator", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.LicenseGenerator-MinSizeRel-5d8bc3a4c1c58bab6f7a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 22, "source": "RoboticLaserMarking/LicenseGenerator", "targetIndexes": [68, 100]}, {"build": "RoboticLaserMarking/RFIDDriver", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.RFIDDriver-MinSizeRel-ce034b8fd7255dd94677.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 23, "source": "RoboticLaserMarking/RFIDDriver", "targetIndexes": [71, 101]}, {"build": "RoboticLaserMarking/laserDriver", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.laserDriver-MinSizeRel-50ac02b6f964d67969b6.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 24, "source": "RoboticLaserMarking/laserDriver", "targetIndexes": [74, 108]}, {"build": "RoboticLaserMarking/Test/abbsocket", "jsonFile": "directory-RoboticLaserMarking.Test.abbsocket-MinSizeRel-c54e19bdb9ae31bded65.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 25, "source": "RoboticLaserMarking/Test/abbsocket", "targetIndexes": [77, 102]}, {"build": "RoboticLaserMarking/Test/laser", "jsonFile": "directory-RoboticLaserMarking.Test.laser-MinSizeRel-fda96428a10838b20da0.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 26, "source": "RoboticLaserMarking/Test/laser", "targetIndexes": [6, 103]}, {"build": "RoboticLaserMarking/Test/laserUI", "jsonFile": "directory-RoboticLaserMarking.Test.laserUI-MinSizeRel-906cc9e5b37f734dfdd8.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 27, "source": "RoboticLaserMarking/Test/laserUI", "targetIndexes": [10, 104]}, {"build": "RoboticLaserMarking/Test/rfiddriver", "jsonFile": "directory-RoboticLaserMarking.Test.rfiddriver-MinSizeRel-f51044958bce4e484373.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 28, "source": "RoboticLaserMarking/Test/rfiddriver", "targetIndexes": [19, 105]}, {"build": "RoboticLaserMarking/Test/rfidserver", "jsonFile": "directory-RoboticLaserMarking.Test.rfidserver-MinSizeRel-7e022394fa2631804126.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 29, "source": "RoboticLaserMarking/Test/rfidserver", "targetIndexes": [28, 106]}, {"build": "RoboticLaserMarking/UI", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.UI-MinSizeRel-a0a826d45ad2ea8615b1.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 30, "source": "RoboticLaserMarking/UI", "targetIndexes": [37, 107]}, {"build": "RoboticLaserMarking/laserDriverSim", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.laserDriverSim-MinSizeRel-9fa894fbfa57d970660d.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 31, "source": "RoboticLaserMarking/laserDriverSim", "targetIndexes": [46, 109]}, {"build": "fuxicore", "hasInstallRule": true, "jsonFile": "directory-fuxicore-MinSizeRel-6019081bb93cc97e5bd3.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 32, "source": "fuxicore", "targetIndexes": [55, 132]}, {"build": "hardwaredriver/abbRobotDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.abbRobotDriver-MinSizeRel-6dd56340ab873b7947eb.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 33, "source": "hardwaredriver/abbRobotDriver", "targetIndexes": [64, 141]}, {"build": "Test/test_abb_socket", "jsonFile": "directory-Test.test_abb_socket-MinSizeRel-b83d4bb5753115673b1e.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 34, "source": "Test/test_abb_socket", "targetIndexes": [73, 110]}, {"build": "Test/test_config_manager", "jsonFile": "directory-Test.test_config_manager-MinSizeRel-b1e58fcc0034795c6ae3.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 35, "source": "Test/test_config_manager", "targetIndexes": [7, 111]}, {"build": "Test/test_csv", "jsonFile": "directory-Test.test_csv-MinSizeRel-46cd2d2264edf5d7592e.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 36, "source": "Test/test_csv", "targetIndexes": [34, 112]}, {"build": "Test/test_event_listener", "jsonFile": "directory-Test.test_event_listener-MinSizeRel-87efd742cb5ddc812bd7.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 37, "source": "Test/test_event_listener", "targetIndexes": [61, 113]}, {"build": "Test/test_executor", "jsonFile": "directory-Test.test_executor-MinSizeRel-a98e8731e17c62507f58.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 38, "source": "Test/test_executor", "targetIndexes": [25, 114]}, {"build": "Test/test_executor_context", "jsonFile": "directory-Test.test_executor_context-MinSizeRel-7ab477d5e8562815cbb7.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 39, "source": "Test/test_executor_context", "targetIndexes": [79, 115]}, {"build": "Test/test_fa2204n_balance", "jsonFile": "directory-Test.test_fa2204n_balance-MinSizeRel-7560d87111b5ff21f33f.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 40, "source": "Test/test_fa2204n_balance", "targetIndexes": [52, 116]}, {"build": "Test/test_fa2204n_balance_basic", "jsonFile": "directory-Test.test_fa2204n_balance_basic-MinSizeRel-c5e19bbf14befd24dc6e.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 41, "source": "Test/test_fa2204n_balance_basic", "targetIndexes": [70, 117]}, {"build": "Test/test_fileutil", "jsonFile": "directory-Test.test_fileutil-MinSizeRel-6a4234fe4fd9c32b60c0.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 42, "source": "Test/test_fileutil", "targetIndexes": [43, 118]}, {"build": "Test/test_json", "jsonFile": "directory-Test.test_json-MinSizeRel-70316fee5b76b6255c9c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 43, "source": "Test/test_json", "targetIndexes": [16, 119]}, {"build": "Test/test_license_manager", "jsonFile": "directory-Test.test_license_manager-MinSizeRel-adf854e580c4cdcb7fc5.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 44, "source": "Test/test_license_manager", "targetIndexes": [76, 120]}, {"build": "Test/test_license_ui", "hasInstallRule": true, "jsonFile": "directory-Test.test_license_ui-MinSizeRel-17e8c4b69d69a26aaf2a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 45, "source": "Test/test_license_ui", "targetIndexes": [67, 121]}, {"build": "Test/test_micro_dosing", "jsonFile": "directory-Test.test_micro_dosing-MinSizeRel-5e830cdb711d4e75b3ab.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 46, "source": "Test/test_micro_dosing", "targetIndexes": [58, 149]}, {"build": "Test/test_network", "jsonFile": "directory-Test.test_network-MinSizeRel-802d04b39615713bdaa5.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 47, "source": "Test/test_network", "targetIndexes": [49, 122]}, {"build": "Test/test_serial", "jsonFile": "directory-Test.test_serial-MinSizeRel-1539ef84150cf55d007b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 48, "source": "Test/test_serial", "targetIndexes": [40, 123]}, {"build": "Test/test_service_container", "jsonFile": "directory-Test.test_service_container-MinSizeRel-3c607165866062790453.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 49, "source": "Test/test_service_container", "targetIndexes": [31, 124]}, {"build": "Test/test_socket", "jsonFile": "directory-Test.test_socket-MinSizeRel-7646e8df0e04f7d933fc.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 50, "source": "Test/test_socket", "targetIndexes": [11, 125]}, {"build": "Test/test_sqlite", "jsonFile": "directory-Test.test_sqlite-MinSizeRel-a3f7e251b42c8d6e85bd.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 51, "source": "Test/test_sqlite", "targetIndexes": [13, 126]}, {"build": "Test/test_taskflow", "jsonFile": "directory-Test.test_taskflow-MinSizeRel-338290a1d2d2c191692b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 52, "source": "Test/test_taskflow", "targetIndexes": [4, 127]}, {"build": "Test/test_twoaixsrobot", "jsonFile": "directory-Test.test_twoaixsrobot-MinSizeRel-829343d8fb5c82be5c03.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 53, "source": "Test/test_twoaixsrobot", "targetIndexes": [78, 128]}, {"build": "Test/test_xml", "jsonFile": "directory-Test.test_xml-MinSizeRel-91dd4b4ea7db1ac593a4.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 54, "source": "Test/test_xml", "targetIndexes": [75, 129]}, {"build": "hardwaredriver/AuboArcsDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.AuboArcsDriver-MinSizeRel-c8464a5a4e222d8a4324.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 55, "source": "hardwaredriver/AuboArcsDriver", "targetIndexes": [72, 133]}, {"build": "hardwaredriver/AuboDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.AuboDriver-MinSizeRel-f6ef18b990c62843287d.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 56, "source": "hardwaredriver/AuboDriver", "targetIndexes": [69, 134]}, {"build": "hardwaredriver/ElectricGripperDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.ElectricGripperDriver-MinSizeRel-9050ee0aa574d02a13c1.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 57, "source": "hardwaredriver/ElectricGripperDriver", "targetIndexes": [66, 135]}, {"build": "hardwaredriver/HikVisionCamera", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.HikVisionCamera-MinSizeRel-2f15954957700dc82692.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 58, "source": "hardwaredriver/HikVisionCamera", "targetIndexes": [63, 136]}, {"build": "hardwaredriver/LabelPrinter", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.LabelPrinter-MinSizeRel-a98e344906e45e035c7a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 59, "source": "hardwaredriver/LabelPrinter", "targetIndexes": [60, 137]}, {"build": "hardwaredriver/MettlerBalance", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.MettlerBalance-MinSizeRel-f65a38351f7de3288dc7.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 60, "source": "hardwaredriver/MettlerBalance", "targetIndexes": [57, 138]}, {"build": "hardwaredriver/OpcDa", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.OpcDa-MinSizeRel-5c7bc29dbda15a9b8ae4.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 61, "source": "hardwaredriver/OpcDa", "targetIndexes": [54, 139]}, {"build": "hardwaredriver/OpcUa", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.OpcUa-MinSizeRel-36fae1fe277182987806.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 62, "source": "hardwaredriver/OpcUa", "targetIndexes": [51, 140]}, {"build": "hardwaredriver/socket", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.socket-MinSizeRel-b084aeaa98c55ca54242.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 63, "source": "hardwaredriver/socket", "targetIndexes": [48, 147]}, {"build": "hardwaredriver/agilerobotDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.agilerobotDriver-MinSizeRel-be0c33e6eb56c2575bd9.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 64, "source": "hardwaredriver/agilerobotDriver", "targetIndexes": [45, 142]}, {"build": "hardwaredriver/fairinoDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.fairinoDriver-MinSizeRel-b638d5fdff8da064311b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 65, "source": "hardwaredriver/fairinoDriver", "targetIndexes": [3, 143]}, {"build": "hardwaredriver/junduoHandDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.junduoHandDriver-MinSizeRel-e7024192621c226dc16f.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 66, "source": "hardwaredriver/junduoHandDriver", "targetIndexes": [2, 144]}, {"build": "hardwaredriver/modbus", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.modbus-MinSizeRel-1f7cf0a90d4506a41f5a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 67, "source": "hardwaredriver/modbus", "targetIndexes": [9, 145]}, {"build": "hardwaredriver/serial", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.serial-MinSizeRel-40c48bd4e0de0af8828e.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 68, "source": "hardwaredriver/serial", "targetIndexes": [12, 146]}, {"build": "hardwaredriver/usbcamera", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.usbcamera-MinSizeRel-968bee8cafdf7e5f9ccf.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 69, "source": "hardwaredriver/usbcamera", "targetIndexes": [30, 148]}, {"build": "tool/calbuild", "jsonFile": "directory-tool.calbuild-MinSizeRel-9edb4e157f9b6e54846b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 70, "source": "tool/calbuild", "targetIndexes": [15, 150]}, {"build": "tool/cameraCalibrator", "hasInstallRule": true, "jsonFile": "directory-tool.cameraCalibrator-MinSizeRel-a861825f388659f4f715.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 71, "source": "tool/cameraCalibrator", "targetIndexes": [18, 152]}, {"build": "tool/caltest", "hasInstallRule": true, "jsonFile": "directory-tool.caltest-MinSizeRel-63fb02a9aaef7a0c834c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 72, "source": "tool/caltest", "targetIndexes": [21, 151]}, {"build": "tool/communication", "hasInstallRule": true, "jsonFile": "directory-tool.communication-MinSizeRel-324152a549fff44395ce.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 73, "source": "tool/communication", "targetIndexes": [22, 153]}, {"build": "tool/handeyecal", "hasInstallRule": true, "jsonFile": "directory-tool.handeyecal-MinSizeRel-77ea94e986eab795f767.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 74, "source": "tool/handeyecal", "targetIndexes": [24, 154]}, {"build": "tool/handeyecaltest", "jsonFile": "directory-tool.handeyecaltest-MinSizeRel-f9f22629594fc4ab2dbc.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 75, "source": "tool/handeyecaltest", "targetIndexes": [27, 155]}, {"build": "tool/handeyecalui/handeyecalui", "jsonFile": "directory-tool.handeyecalui.handeyecalui-MinSizeRel-1fafdc8136eaae57e66b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 76, "source": "tool/handeyecalui/handeyecalui", "targetIndexes": [33, 156]}, {"build": "tool/handeyecaluipath", "jsonFile": "directory-tool.handeyecaluipath-MinSizeRel-e479629856c728efc292.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 77, "source": "tool/handeyecaluipath", "targetIndexes": [36, 157]}, {"build": "tool/handeyecaluipathAuto", "jsonFile": "directory-tool.handeyecaluipathAuto-MinSizeRel-0a177976965c3cc59f22.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 78, "source": "tool/handeyecaluipathAuto", "targetIndexes": [39, 158]}, {"build": "tool/verify_calibration", "jsonFile": "directory-tool.verify_calibration-MinSizeRel-2d3946f9054860a13e8b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 79, "source": "tool/verify_calibration", "targetIndexes": [42, 159]}], "name": "MinSizeRel", "projects": [{"childIndexes": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79], "directoryIndexes": [0], "name": "Project", "targetIndexes": [1, 130]}, {"directoryIndexes": [1], "name": "fuxicommon", "parentIndex": 0, "targetIndexes": [5, 131]}, {"directoryIndexes": [2], "name": "Analysis_RobotalgorithmspouringControl", "parentIndex": 0, "targetIndexes": [8, 82]}, {"directoryIndexes": [3], "name": "Analysis_RobotdriversaixsDriver", "parentIndex": 0, "targetIndexes": [0, 84]}, {"directoryIndexes": [4], "name": "Analysis_RobotdriversplcDriver", "parentIndex": 0, "targetIndexes": [14, 88]}, {"directoryIndexes": [5], "name": "Analysis_RobotdriversbalanceDriver", "parentIndex": 0, "targetIndexes": [17, 85]}, {"directoryIndexes": [6], "name": "Analysis_RobotdriversheatingMagneticStirrerDriver", "parentIndex": 0, "targetIndexes": [20, 86]}, {"directoryIndexes": [7], "name": "Analysis_RobotdriversmoistureAnalyzerDriver", "parentIndex": 0, "targetIndexes": [23, 87]}, {"directoryIndexes": [8], "name": "Analysis_RobotdriversrobotDriver", "parentIndex": 0, "targetIndexes": [26, 90]}, {"directoryIndexes": [9], "name": "Analysis_RobotdriversrestInterfaceDriver", "parentIndex": 0, "targetIndexes": [29, 89]}, {"directoryIndexes": [10], "name": "Analysis_RobotApp", "parentIndex": 0, "targetIndexes": [32, 80]}, {"directoryIndexes": [11], "name": "Analysis_RobotalgorithmscoordinateTransform", "parentIndex": 0, "targetIndexes": [35, 81]}, {"directoryIndexes": [12], "name": "Analysis_RobotalgorithmstcpPositionMaintain", "parentIndex": 0, "targetIndexes": [38, 83]}, {"directoryIndexes": [13], "name": "Analysis_RobottestheaterApiTest", "parentIndex": 0, "targetIndexes": [41, 91]}, {"directoryIndexes": [14], "name": "Analysis_RobottestheatingMagneticStirrerDriver", "parentIndex": 0, "targetIndexes": [44, 92]}, {"directoryIndexes": [15], "name": "MJServerAPP", "parentIndex": 0, "targetIndexes": [47, 93]}, {"directoryIndexes": [16], "name": "MJServer_RefactorLibrary", "parentIndex": 0, "targetIndexes": [50, 95]}, {"directoryIndexes": [17], "name": "MJServer_RefactorApp", "parentIndex": 0, "targetIndexes": [53, 94]}, {"directoryIndexes": [18], "name": "MJServer_RefactorTestphase1_test", "parentIndex": 0, "targetIndexes": [56, 96]}, {"directoryIndexes": [19], "name": "MJServer_RefactorTestsimple_abb_client", "parentIndex": 0, "targetIndexes": [59, 97]}, {"directoryIndexes": [20], "name": "MJServer_RefactorTestsimple_feeder_client", "parentIndex": 0, "targetIndexes": [62, 98]}, {"directoryIndexes": [21], "name": "RoboticLaserMarkingAbbDriver", "parentIndex": 0, "targetIndexes": [65, 99]}, {"directoryIndexes": [22], "name": "RoboticLaserMarkingLicenseGenerator", "parentIndex": 0, "targetIndexes": [68, 100]}, {"directoryIndexes": [23], "name": "RoboticLaserMarkingRFIDDriver", "parentIndex": 0, "targetIndexes": [71, 101]}, {"directoryIndexes": [24], "name": "RoboticLaserMarkinglaserDriver", "parentIndex": 0, "targetIndexes": [74, 108]}, {"directoryIndexes": [25], "name": "RoboticLaserMarkingTestabbsocket", "parentIndex": 0, "targetIndexes": [77, 102]}, {"directoryIndexes": [26], "name": "RoboticLaserMarkingTestlaser", "parentIndex": 0, "targetIndexes": [6, 103]}, {"directoryIndexes": [27], "name": "RoboticLaserMarkingTestlaserUI", "parentIndex": 0, "targetIndexes": [10, 104]}, {"directoryIndexes": [28], "name": "RoboticLaserMarkingTestrfiddriver", "parentIndex": 0, "targetIndexes": [19, 105]}, {"directoryIndexes": [29], "name": "RoboticLaserMarkingTestrfidserver", "parentIndex": 0, "targetIndexes": [28, 106]}, {"directoryIndexes": [30], "name": "RoboticLaserMarkingUI", "parentIndex": 0, "targetIndexes": [37, 107]}, {"directoryIndexes": [31], "name": "RoboticLaserMarkinglaserDriverSim", "parentIndex": 0, "targetIndexes": [46, 109]}, {"directoryIndexes": [32], "name": "fuxicore", "parentIndex": 0, "targetIndexes": [55, 132]}, {"directoryIndexes": [33], "name": "hardwaredriverabbRobotDriver", "parentIndex": 0, "targetIndexes": [64, 141]}, {"directoryIndexes": [34], "name": "Testtest_abb_socket", "parentIndex": 0, "targetIndexes": [73, 110]}, {"directoryIndexes": [35], "name": "Testtest_config_manager", "parentIndex": 0, "targetIndexes": [7, 111]}, {"directoryIndexes": [36], "name": "Testtest_csv", "parentIndex": 0, "targetIndexes": [34, 112]}, {"directoryIndexes": [37], "name": "Testtest_event_listener", "parentIndex": 0, "targetIndexes": [61, 113]}, {"directoryIndexes": [38], "name": "Testtest_executor", "parentIndex": 0, "targetIndexes": [25, 114]}, {"directoryIndexes": [39], "name": "Testtest_executor_context", "parentIndex": 0, "targetIndexes": [79, 115]}, {"directoryIndexes": [40], "name": "Testtest_fa2204n_balance", "parentIndex": 0, "targetIndexes": [52, 116]}, {"directoryIndexes": [41], "name": "Testtest_fa2204n_balance_basic", "parentIndex": 0, "targetIndexes": [70, 117]}, {"directoryIndexes": [42], "name": "Testtest_fileutil", "parentIndex": 0, "targetIndexes": [43, 118]}, {"directoryIndexes": [43], "name": "Testtest_json", "parentIndex": 0, "targetIndexes": [16, 119]}, {"directoryIndexes": [44], "name": "Testtest_license_manager", "parentIndex": 0, "targetIndexes": [76, 120]}, {"directoryIndexes": [45], "name": "Testtest_license_ui", "parentIndex": 0, "targetIndexes": [67, 121]}, {"directoryIndexes": [46], "name": "test_micro_dosing", "parentIndex": 0, "targetIndexes": [58, 149]}, {"directoryIndexes": [47], "name": "Testtest_network", "parentIndex": 0, "targetIndexes": [49, 122]}, {"directoryIndexes": [48], "name": "Testtest_serial", "parentIndex": 0, "targetIndexes": [40, 123]}, {"directoryIndexes": [49], "name": "Testtest_service_container", "parentIndex": 0, "targetIndexes": [31, 124]}, {"directoryIndexes": [50], "name": "Testtest_socket", "parentIndex": 0, "targetIndexes": [11, 125]}, {"directoryIndexes": [51], "name": "Testtest_sqlite", "parentIndex": 0, "targetIndexes": [13, 126]}, {"directoryIndexes": [52], "name": "Testtest_taskflow", "parentIndex": 0, "targetIndexes": [4, 127]}, {"directoryIndexes": [53], "name": "Testtest_twoaixsrobot", "parentIndex": 0, "targetIndexes": [78, 128]}, {"directoryIndexes": [54], "name": "Testtest_xml", "parentIndex": 0, "targetIndexes": [75, 129]}, {"directoryIndexes": [55], "name": "hardwaredriverAuboArcsDriver", "parentIndex": 0, "targetIndexes": [72, 133]}, {"directoryIndexes": [56], "name": "hardwaredriverAuboDriver", "parentIndex": 0, "targetIndexes": [69, 134]}, {"directoryIndexes": [57], "name": "hardwaredriverElectricGripperDriver", "parentIndex": 0, "targetIndexes": [66, 135]}, {"directoryIndexes": [58], "name": "hardwaredriverHikVisionCamera", "parentIndex": 0, "targetIndexes": [63, 136]}, {"directoryIndexes": [59], "name": "hardwaredriverLabelPrinter", "parentIndex": 0, "targetIndexes": [60, 137]}, {"directoryIndexes": [60], "name": "hardwaredriverMettlerBalance", "parentIndex": 0, "targetIndexes": [57, 138]}, {"directoryIndexes": [61], "name": "hardwaredriverOpcDa", "parentIndex": 0, "targetIndexes": [54, 139]}, {"directoryIndexes": [62], "name": "hardwaredriverOpcUa", "parentIndex": 0, "targetIndexes": [51, 140]}, {"directoryIndexes": [63], "name": "hardwaredriversocket", "parentIndex": 0, "targetIndexes": [48, 147]}, {"directoryIndexes": [64], "name": "hardwaredriveragilerobotDriver", "parentIndex": 0, "targetIndexes": [45, 142]}, {"directoryIndexes": [65], "name": "hardwaredriverfairinoDriver", "parentIndex": 0, "targetIndexes": [3, 143]}, {"directoryIndexes": [66], "name": "hardwaredriverjunduoHandDriver", "parentIndex": 0, "targetIndexes": [2, 144]}, {"directoryIndexes": [67], "name": "hardwaredrivermodbus", "parentIndex": 0, "targetIndexes": [9, 145]}, {"directoryIndexes": [68], "name": "hardwaredriverserial", "parentIndex": 0, "targetIndexes": [12, 146]}, {"directoryIndexes": [69], "name": "hardwaredriverusbcamera", "parentIndex": 0, "targetIndexes": [30, 148]}, {"directoryIndexes": [70], "name": "toolcalbuild", "parentIndex": 0, "targetIndexes": [15, 150]}, {"directoryIndexes": [71], "name": "toolcameraCalibrator", "parentIndex": 0, "targetIndexes": [18, 152]}, {"directoryIndexes": [72], "name": "toolcaltest", "parentIndex": 0, "targetIndexes": [21, 151]}, {"directoryIndexes": [73], "name": "toolcommunication", "parentIndex": 0, "targetIndexes": [22, 153]}, {"directoryIndexes": [74], "name": "toolhandeyecal", "parentIndex": 0, "targetIndexes": [24, 154]}, {"directoryIndexes": [75], "name": "toolhandeyecaltest", "parentIndex": 0, "targetIndexes": [27, 155]}, {"directoryIndexes": [76], "name": "toolhandeyecaluihandeyecalui", "parentIndex": 0, "targetIndexes": [33, 156]}, {"directoryIndexes": [77], "name": "toolhandeyecaluipath", "parentIndex": 0, "targetIndexes": [36, 157]}, {"directoryIndexes": [78], "name": "toolhandeyecaluipathAuto", "parentIndex": 0, "targetIndexes": [39, 158]}, {"directoryIndexes": [79], "name": "toolverify_calibration", "parentIndex": 0, "targetIndexes": [42, 159]}], "targets": [{"directoryIndex": 3, "id": "ALL_BUILD::@19f706e88e1d43a9565c", "jsonFile": "target-ALL_BUILD-MinSizeRel-85da0b39f38e7483b20e.json", "name": "ALL_BUILD", "projectIndex": 3}, {"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-MinSizeRel-342279978964b07ce418.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 66, "id": "ALL_BUILD::@a89b79f2a82dbe076976", "jsonFile": "target-ALL_BUILD-MinSizeRel-40a7d901ed8e10a0c7f5.json", "name": "ALL_BUILD", "projectIndex": 66}, {"directoryIndex": 65, "id": "ALL_BUILD::@8a675cd9715b77cebac5", "jsonFile": "target-ALL_BUILD-MinSizeRel-2e630e3e3d96ead79510.json", "name": "ALL_BUILD", "projectIndex": 65}, {"directoryIndex": 52, "id": "ALL_BUILD::@39116d767a15e3a891df", "jsonFile": "target-ALL_BUILD-MinSizeRel-a7eeeef2e82f92727250.json", "name": "ALL_BUILD", "projectIndex": 52}, {"directoryIndex": 1, "id": "ALL_BUILD::@58335e9a86196d0a97e7", "jsonFile": "target-ALL_BUILD-MinSizeRel-0841e45febd559571c13.json", "name": "ALL_BUILD", "projectIndex": 1}, {"directoryIndex": 26, "id": "ALL_BUILD::@fef5c779e5dd4e5a5c7c", "jsonFile": "target-ALL_BUILD-MinSizeRel-68f406d96290d6b7df07.json", "name": "ALL_BUILD", "projectIndex": 26}, {"directoryIndex": 35, "id": "ALL_BUILD::@d885fae1c443095a1db7", "jsonFile": "target-ALL_BUILD-MinSizeRel-2569c2c10fad6870c72b.json", "name": "ALL_BUILD", "projectIndex": 35}, {"directoryIndex": 2, "id": "ALL_BUILD::@07001b74ee4af3db8a6e", "jsonFile": "target-ALL_BUILD-MinSizeRel-a93df49a17068820adac.json", "name": "ALL_BUILD", "projectIndex": 2}, {"directoryIndex": 67, "id": "ALL_BUILD::@2ef1e00ba9bcf504e7b3", "jsonFile": "target-ALL_BUILD-MinSizeRel-29cfabbfeabe66f20220.json", "name": "ALL_BUILD", "projectIndex": 67}, {"directoryIndex": 27, "id": "ALL_BUILD::@7e6cec28b989a66fe139", "jsonFile": "target-ALL_BUILD-MinSizeRel-07b7923a48da597b2076.json", "name": "ALL_BUILD", "projectIndex": 27}, {"directoryIndex": 50, "id": "ALL_BUILD::@6ff24ad6fb1c26ae0a57", "jsonFile": "target-ALL_BUILD-MinSizeRel-5e2fbceb8d24c84e42cf.json", "name": "ALL_BUILD", "projectIndex": 50}, {"directoryIndex": 68, "id": "ALL_BUILD::@e813d8aa5825a18a8390", "jsonFile": "target-ALL_BUILD-MinSizeRel-19c438a9df50cace71d5.json", "name": "ALL_BUILD", "projectIndex": 68}, {"directoryIndex": 51, "id": "ALL_BUILD::@e4ccddf7a4c29adf5bd6", "jsonFile": "target-ALL_BUILD-MinSizeRel-119fa9189d354d4060ac.json", "name": "ALL_BUILD", "projectIndex": 51}, {"directoryIndex": 4, "id": "ALL_BUILD::@97966baa9ab9c14a9bcf", "jsonFile": "target-ALL_BUILD-MinSizeRel-5339ba61d8ad5be7b663.json", "name": "ALL_BUILD", "projectIndex": 4}, {"directoryIndex": 70, "id": "ALL_BUILD::@a167bea24520843f7e43", "jsonFile": "target-ALL_BUILD-MinSizeRel-da1d6472f79bdb30833d.json", "name": "ALL_BUILD", "projectIndex": 70}, {"directoryIndex": 43, "id": "ALL_BUILD::@46d5ce0aeb8e42b7284d", "jsonFile": "target-ALL_BUILD-MinSizeRel-430b2635e5f4a69b8869.json", "name": "ALL_BUILD", "projectIndex": 43}, {"directoryIndex": 5, "id": "ALL_BUILD::@92995a2f85961e8f5b16", "jsonFile": "target-ALL_BUILD-MinSizeRel-8982b17ad107170d8e83.json", "name": "ALL_BUILD", "projectIndex": 5}, {"directoryIndex": 71, "id": "ALL_BUILD::@51e97efefc2313866ad5", "jsonFile": "target-ALL_BUILD-MinSizeRel-54994d3f173f21751830.json", "name": "ALL_BUILD", "projectIndex": 71}, {"directoryIndex": 28, "id": "ALL_BUILD::@1df6ab8cb0b8ddb593f8", "jsonFile": "target-ALL_BUILD-MinSizeRel-c504476d8ceceb45d7c2.json", "name": "ALL_BUILD", "projectIndex": 28}, {"directoryIndex": 6, "id": "ALL_BUILD::@e9ece92fe2bc47be420b", "jsonFile": "target-ALL_BUILD-MinSizeRel-c6d18cd0ab434516660b.json", "name": "ALL_BUILD", "projectIndex": 6}, {"directoryIndex": 72, "id": "ALL_BUILD::@9d9cd8ab08d8520a4ac3", "jsonFile": "target-ALL_BUILD-MinSizeRel-3d4cfdff622930c380ca.json", "name": "ALL_BUILD", "projectIndex": 72}, {"directoryIndex": 73, "id": "ALL_BUILD::@116eb0f160f4d76de168", "jsonFile": "target-ALL_BUILD-MinSizeRel-16899497fe6bfb496bf5.json", "name": "ALL_BUILD", "projectIndex": 73}, {"directoryIndex": 7, "id": "ALL_BUILD::@03c7a47b8090dea9b455", "jsonFile": "target-ALL_BUILD-MinSizeRel-ffa581a59595a1d89428.json", "name": "ALL_BUILD", "projectIndex": 7}, {"directoryIndex": 74, "id": "ALL_BUILD::@d7390e83b7e4f79f633d", "jsonFile": "target-ALL_BUILD-MinSizeRel-f7c053f7188b257ad6ab.json", "name": "ALL_BUILD", "projectIndex": 74}, {"directoryIndex": 38, "id": "ALL_BUILD::@7e2e321726c5f3e3edcb", "jsonFile": "target-ALL_BUILD-MinSizeRel-c6bf30b5692cd0ba3e2e.json", "name": "ALL_BUILD", "projectIndex": 38}, {"directoryIndex": 8, "id": "ALL_BUILD::@3f043c5f38f013ef2115", "jsonFile": "target-ALL_BUILD-MinSizeRel-a38675298dfaed7097d8.json", "name": "ALL_BUILD", "projectIndex": 8}, {"directoryIndex": 75, "id": "ALL_BUILD::@ae279e4383f26a866133", "jsonFile": "target-ALL_BUILD-MinSizeRel-0eb479fa5fcd1bd5f128.json", "name": "ALL_BUILD", "projectIndex": 75}, {"directoryIndex": 29, "id": "ALL_BUILD::@75eb8879fc099b4640aa", "jsonFile": "target-ALL_BUILD-MinSizeRel-999f14f3c47b505fa427.json", "name": "ALL_BUILD", "projectIndex": 29}, {"directoryIndex": 9, "id": "ALL_BUILD::@6b827d246feac3c35b9a", "jsonFile": "target-ALL_BUILD-MinSizeRel-bf24b70e9943d8b96cbc.json", "name": "ALL_BUILD", "projectIndex": 9}, {"directoryIndex": 69, "id": "ALL_BUILD::@bfaa0a8775de30d870f0", "jsonFile": "target-ALL_BUILD-MinSizeRel-9918016ebbb419e28d18.json", "name": "ALL_BUILD", "projectIndex": 69}, {"directoryIndex": 49, "id": "ALL_BUILD::@03373f949cbd329c961c", "jsonFile": "target-ALL_BUILD-MinSizeRel-b9a988aed78fcad4eb7e.json", "name": "ALL_BUILD", "projectIndex": 49}, {"directoryIndex": 10, "id": "ALL_BUILD::@6ccf8425ca6a81980105", "jsonFile": "target-ALL_BUILD-MinSizeRel-dad371b5f71f8a659af3.json", "name": "ALL_BUILD", "projectIndex": 10}, {"directoryIndex": 76, "id": "ALL_BUILD::@64c63141ea1fe7a116f6", "jsonFile": "target-ALL_BUILD-MinSizeRel-1048f699b992ebfb0588.json", "name": "ALL_BUILD", "projectIndex": 76}, {"directoryIndex": 36, "id": "ALL_BUILD::@08c5adc7ee1d91091a97", "jsonFile": "target-ALL_BUILD-MinSizeRel-ece8b323ed57b6f1fbe7.json", "name": "ALL_BUILD", "projectIndex": 36}, {"directoryIndex": 11, "id": "ALL_BUILD::@e0567cd60ef58755dd5b", "jsonFile": "target-ALL_BUILD-MinSizeRel-7e71292cd8fb1d3f305b.json", "name": "ALL_BUILD", "projectIndex": 11}, {"directoryIndex": 77, "id": "ALL_BUILD::@f8ebbc87f7fac77328c8", "jsonFile": "target-ALL_BUILD-MinSizeRel-2307048a7f0d82e4133d.json", "name": "ALL_BUILD", "projectIndex": 77}, {"directoryIndex": 30, "id": "ALL_BUILD::@4e1303897d180b86ab2f", "jsonFile": "target-ALL_BUILD-MinSizeRel-5b129cd61e1e6e577f56.json", "name": "ALL_BUILD", "projectIndex": 30}, {"directoryIndex": 12, "id": "ALL_BUILD::@96a57770f6c6f4e493b3", "jsonFile": "target-ALL_BUILD-MinSizeRel-6e1bc5ae653b03810b1e.json", "name": "ALL_BUILD", "projectIndex": 12}, {"directoryIndex": 78, "id": "ALL_BUILD::@f4fb3041b29f01391299", "jsonFile": "target-ALL_BUILD-MinSizeRel-1f9beecd76424e38b6ac.json", "name": "ALL_BUILD", "projectIndex": 78}, {"directoryIndex": 48, "id": "ALL_BUILD::@a384ba46c8f7385844c3", "jsonFile": "target-ALL_BUILD-MinSizeRel-585b87981061d80d6b32.json", "name": "ALL_BUILD", "projectIndex": 48}, {"directoryIndex": 13, "id": "ALL_BUILD::@6eec4415674bd20e6491", "jsonFile": "target-ALL_BUILD-MinSizeRel-67d66f0bb28969695805.json", "name": "ALL_BUILD", "projectIndex": 13}, {"directoryIndex": 79, "id": "ALL_BUILD::@efca4bcc8ad294d52f3d", "jsonFile": "target-ALL_BUILD-MinSizeRel-427747eec773300cb006.json", "name": "ALL_BUILD", "projectIndex": 79}, {"directoryIndex": 42, "id": "ALL_BUILD::@b13ebbd4a3aafa6a0363", "jsonFile": "target-ALL_BUILD-MinSizeRel-b28ba7495172b5cff460.json", "name": "ALL_BUILD", "projectIndex": 42}, {"directoryIndex": 14, "id": "ALL_BUILD::@1bc5057501657c23b12e", "jsonFile": "target-ALL_BUILD-MinSizeRel-44929f2f6ed2fa190302.json", "name": "ALL_BUILD", "projectIndex": 14}, {"directoryIndex": 64, "id": "ALL_BUILD::@14914dfd89874674d41d", "jsonFile": "target-ALL_BUILD-MinSizeRel-901e1781755324a5fec6.json", "name": "ALL_BUILD", "projectIndex": 64}, {"directoryIndex": 31, "id": "ALL_BUILD::@30345e39cecb9bcc06b0", "jsonFile": "target-ALL_BUILD-MinSizeRel-a2f4c2c3699b6d4e8f02.json", "name": "ALL_BUILD", "projectIndex": 31}, {"directoryIndex": 15, "id": "ALL_BUILD::@7c9daef8275400bf8ba5", "jsonFile": "target-ALL_BUILD-MinSizeRel-8eb8385fb4710473c8b0.json", "name": "ALL_BUILD", "projectIndex": 15}, {"directoryIndex": 63, "id": "ALL_BUILD::@e58766abf91db77f862b", "jsonFile": "target-ALL_BUILD-MinSizeRel-c49bfb773b8d9ae8a563.json", "name": "ALL_BUILD", "projectIndex": 63}, {"directoryIndex": 47, "id": "ALL_BUILD::@76c3a22b9f657d2ec026", "jsonFile": "target-ALL_BUILD-MinSizeRel-73870bfe79e2590bf24b.json", "name": "ALL_BUILD", "projectIndex": 47}, {"directoryIndex": 16, "id": "ALL_BUILD::@8670365571700e12b583", "jsonFile": "target-ALL_BUILD-MinSizeRel-f7583928953388d9577a.json", "name": "ALL_BUILD", "projectIndex": 16}, {"directoryIndex": 62, "id": "ALL_BUILD::@7bf30a519259482def19", "jsonFile": "target-ALL_BUILD-MinSizeRel-7285147ac61a7f6b2475.json", "name": "ALL_BUILD", "projectIndex": 62}, {"directoryIndex": 40, "id": "ALL_BUILD::@121a4898e406881ffb23", "jsonFile": "target-ALL_BUILD-MinSizeRel-9c608b9e07c5eb8aa8d3.json", "name": "ALL_BUILD", "projectIndex": 40}, {"directoryIndex": 17, "id": "ALL_BUILD::@f8e8ceb61b4f8c6d7034", "jsonFile": "target-ALL_BUILD-MinSizeRel-242568ff82df40a13a70.json", "name": "ALL_BUILD", "projectIndex": 17}, {"directoryIndex": 61, "id": "ALL_BUILD::@a2142d788288f069154a", "jsonFile": "target-ALL_BUILD-MinSizeRel-765fa3e850ca53bc5583.json", "name": "ALL_BUILD", "projectIndex": 61}, {"directoryIndex": 32, "id": "ALL_BUILD::@5cdc2d2ab21b3b9e72cd", "jsonFile": "target-ALL_BUILD-MinSizeRel-f679fc8971fbb8230b83.json", "name": "ALL_BUILD", "projectIndex": 32}, {"directoryIndex": 18, "id": "ALL_BUILD::@0a3c2e809899f2f13f5a", "jsonFile": "target-ALL_BUILD-MinSizeRel-0d2ad87deda7b2a9aa06.json", "name": "ALL_BUILD", "projectIndex": 18}, {"directoryIndex": 60, "id": "ALL_BUILD::@39b1645ff4c023a4e445", "jsonFile": "target-ALL_BUILD-MinSizeRel-084b090b7dc16868a8bc.json", "name": "ALL_BUILD", "projectIndex": 60}, {"directoryIndex": 46, "id": "ALL_BUILD::@65e3165a8812532710ef", "jsonFile": "target-ALL_BUILD-MinSizeRel-26611f28379a7309f899.json", "name": "ALL_BUILD", "projectIndex": 46}, {"directoryIndex": 19, "id": "ALL_BUILD::@933176848578d8c440c9", "jsonFile": "target-ALL_BUILD-MinSizeRel-c48b3bb23f187ae9ce55.json", "name": "ALL_BUILD", "projectIndex": 19}, {"directoryIndex": 59, "id": "ALL_BUILD::@a99f8207d5ede56c5cae", "jsonFile": "target-ALL_BUILD-MinSizeRel-9c0ab327cff663eb592d.json", "name": "ALL_BUILD", "projectIndex": 59}, {"directoryIndex": 37, "id": "ALL_BUILD::@889b7d31514c76e85624", "jsonFile": "target-ALL_BUILD-MinSizeRel-46891ee6e417731d3bb2.json", "name": "ALL_BUILD", "projectIndex": 37}, {"directoryIndex": 20, "id": "ALL_BUILD::@80cf03468317b5d7fe2b", "jsonFile": "target-ALL_BUILD-MinSizeRel-9a67b5f1091c8b72c93d.json", "name": "ALL_BUILD", "projectIndex": 20}, {"directoryIndex": 58, "id": "ALL_BUILD::@bc252bb14595a0f09d26", "jsonFile": "target-ALL_BUILD-MinSizeRel-0d333457689a8657f8bb.json", "name": "ALL_BUILD", "projectIndex": 58}, {"directoryIndex": 33, "id": "ALL_BUILD::@ccffbf515659b480dabe", "jsonFile": "target-ALL_BUILD-MinSizeRel-d029dae30f7abd04202a.json", "name": "ALL_BUILD", "projectIndex": 33}, {"directoryIndex": 21, "id": "ALL_BUILD::@9c3ce50b53cc3ff6eff5", "jsonFile": "target-ALL_BUILD-MinSizeRel-2bdca0b9ff24f6069f6f.json", "name": "ALL_BUILD", "projectIndex": 21}, {"directoryIndex": 57, "id": "ALL_BUILD::@e336ced093e233e6d829", "jsonFile": "target-ALL_BUILD-MinSizeRel-2490e630ef488d559bda.json", "name": "ALL_BUILD", "projectIndex": 57}, {"directoryIndex": 45, "id": "ALL_BUILD::@bb78083dcad0a236858d", "jsonFile": "target-ALL_BUILD-MinSizeRel-a4715af1c837cd3cfb57.json", "name": "ALL_BUILD", "projectIndex": 45}, {"directoryIndex": 22, "id": "ALL_BUILD::@59f7d9ae5c13d347c5f4", "jsonFile": "target-ALL_BUILD-MinSizeRel-55c4b70ff406511ddf93.json", "name": "ALL_BUILD", "projectIndex": 22}, {"directoryIndex": 56, "id": "ALL_BUILD::@e75f830eb736a5dca1ce", "jsonFile": "target-ALL_BUILD-MinSizeRel-d9762c58eea40fa5b0b3.json", "name": "ALL_BUILD", "projectIndex": 56}, {"directoryIndex": 41, "id": "ALL_BUILD::@1dc2f4735d896dd76909", "jsonFile": "target-ALL_BUILD-MinSizeRel-929fe4cbdbeb7ddf242d.json", "name": "ALL_BUILD", "projectIndex": 41}, {"directoryIndex": 23, "id": "ALL_BUILD::@d1520424919af3a40272", "jsonFile": "target-ALL_BUILD-MinSizeRel-6f3f47d4c763e4b65f51.json", "name": "ALL_BUILD", "projectIndex": 23}, {"directoryIndex": 55, "id": "ALL_BUILD::@fd5e493b37d2bab880d9", "jsonFile": "target-ALL_BUILD-MinSizeRel-603c76eaf26e2c4621cd.json", "name": "ALL_BUILD", "projectIndex": 55}, {"directoryIndex": 34, "id": "ALL_BUILD::@d803dad5c2b28052d845", "jsonFile": "target-ALL_BUILD-MinSizeRel-3f8d66d1990c24f51ece.json", "name": "ALL_BUILD", "projectIndex": 34}, {"directoryIndex": 24, "id": "ALL_BUILD::@cf8a855e37e415d7ca08", "jsonFile": "target-ALL_BUILD-MinSizeRel-4072b617fa6d0b547136.json", "name": "ALL_BUILD", "projectIndex": 24}, {"directoryIndex": 54, "id": "ALL_BUILD::@80c0713ae5ba495463b6", "jsonFile": "target-ALL_BUILD-MinSizeRel-f6c6c0ef380fb6385d06.json", "name": "ALL_BUILD", "projectIndex": 54}, {"directoryIndex": 44, "id": "ALL_BUILD::@7a0ade4671e16056f257", "jsonFile": "target-ALL_BUILD-MinSizeRel-446feef9c15e67387422.json", "name": "ALL_BUILD", "projectIndex": 44}, {"directoryIndex": 25, "id": "ALL_BUILD::@bca145e2342aef659032", "jsonFile": "target-ALL_BUILD-MinSizeRel-eb32980119712b6b14dc.json", "name": "ALL_BUILD", "projectIndex": 25}, {"directoryIndex": 53, "id": "ALL_BUILD::@7d9d822efa235ac321e6", "jsonFile": "target-ALL_BUILD-MinSizeRel-382e2efa87d6a17ef3e9.json", "name": "ALL_BUILD", "projectIndex": 53}, {"directoryIndex": 39, "id": "ALL_BUILD::@e99d12ef8be33386882a", "jsonFile": "target-ALL_BUILD-MinSizeRel-d89f761cec3c2e760907.json", "name": "ALL_BUILD", "projectIndex": 39}, {"directoryIndex": 10, "id": "Analysis_RobotApp::@6ccf8425ca6a81980105", "jsonFile": "target-Analysis_RobotApp-MinSizeRel-a5979873c0669ce60566.json", "name": "Analysis_RobotApp", "projectIndex": 10}, {"directoryIndex": 11, "id": "Analysis_RobotalgorithmscoordinateTransform::@e0567cd60ef58755dd5b", "jsonFile": "target-Analysis_RobotalgorithmscoordinateTransform-MinSizeRel-10b4369aa40e401f03ed.json", "name": "Analysis_RobotalgorithmscoordinateTransform", "projectIndex": 11}, {"directoryIndex": 2, "id": "Analysis_RobotalgorithmspouringControl::@07001b74ee4af3db8a6e", "jsonFile": "target-Analysis_RobotalgorithmspouringControl-MinSizeRel-1d13ee5ca400c5852c58.json", "name": "Analysis_RobotalgorithmspouringControl", "projectIndex": 2}, {"directoryIndex": 12, "id": "Analysis_RobotalgorithmstcpPositionMaintain::@96a57770f6c6f4e493b3", "jsonFile": "target-Analysis_RobotalgorithmstcpPositionMaintain-MinSizeRel-200f81eaaf3e6c9898b0.json", "name": "Analysis_RobotalgorithmstcpPositionMaintain", "projectIndex": 12}, {"directoryIndex": 3, "id": "Analysis_RobotdriversaixsDriver::@19f706e88e1d43a9565c", "jsonFile": "target-Analysis_RobotdriversaixsDriver-MinSizeRel-468918fd2df3c7e4d15c.json", "name": "Analysis_RobotdriversaixsDriver", "projectIndex": 3}, {"directoryIndex": 5, "id": "Analysis_RobotdriversbalanceDriver::@92995a2f85961e8f5b16", "jsonFile": "target-Analysis_RobotdriversbalanceDriver-MinSizeRel-d0e679701010b266faaa.json", "name": "Analysis_RobotdriversbalanceDriver", "projectIndex": 5}, {"directoryIndex": 6, "id": "Analysis_RobotdriversheatingMagneticStirrerDriver::@e9ece92fe2bc47be420b", "jsonFile": "target-Analysis_RobotdriversheatingMagneticStirrerDriver-MinSizeRel-a309ba18f809addca889.json", "name": "Analysis_RobotdriversheatingMagneticStirrerDriver", "projectIndex": 6}, {"directoryIndex": 7, "id": "Analysis_RobotdriversmoistureAnalyzerDriver::@03c7a47b8090dea9b455", "jsonFile": "target-Analysis_RobotdriversmoistureAnalyzerDriver-MinSizeRel-020ce8cbdbf4fdf439b7.json", "name": "Analysis_RobotdriversmoistureAnalyzerDriver", "projectIndex": 7}, {"directoryIndex": 4, "id": "Analysis_RobotdriversplcDriver::@97966baa9ab9c14a9bcf", "jsonFile": "target-Analysis_RobotdriversplcDriver-MinSizeRel-2f23347dc66199d9f6be.json", "name": "Analysis_RobotdriversplcDriver", "projectIndex": 4}, {"directoryIndex": 9, "id": "Analysis_RobotdriversrestInterfaceDriver::@6b827d246feac3c35b9a", "jsonFile": "target-Analysis_RobotdriversrestInterfaceDriver-MinSizeRel-8cf53ebeca4468f769f5.json", "name": "Analysis_RobotdriversrestInterfaceDriver", "projectIndex": 9}, {"directoryIndex": 8, "id": "Analysis_RobotdriversrobotDriver::@3f043c5f38f013ef2115", "jsonFile": "target-Analysis_RobotdriversrobotDriver-MinSizeRel-7152509f80edb323a53c.json", "name": "Analysis_RobotdriversrobotDriver", "projectIndex": 8}, {"directoryIndex": 13, "id": "Analysis_RobottestheaterApiTest::@6eec4415674bd20e6491", "jsonFile": "target-Analysis_RobottestheaterApiTest-MinSizeRel-6c338ba2c330a177c956.json", "name": "Analysis_RobottestheaterApiTest", "projectIndex": 13}, {"directoryIndex": 14, "id": "Analysis_RobottestheatingMagneticStirrerDriver::@1bc5057501657c23b12e", "jsonFile": "target-Analysis_RobottestheatingMagneticStirrerDriver-MinSizeRel-13cbb459218945277bf9.json", "name": "Analysis_RobottestheatingMagneticStirrerDriver", "projectIndex": 14}, {"directoryIndex": 15, "id": "MJServerAPP::@7c9daef8275400bf8ba5", "jsonFile": "target-MJServerAPP-MinSizeRel-1217506080699646d985.json", "name": "MJServerAPP", "projectIndex": 15}, {"directoryIndex": 17, "id": "MJServer_RefactorApp::@f8e8ceb61b4f8c6d7034", "jsonFile": "target-MJServer_RefactorApp-MinSizeRel-c9f4d933796d08d4c67a.json", "name": "MJServer_RefactorApp", "projectIndex": 17}, {"directoryIndex": 16, "id": "MJServer_RefactorLibrary::@8670365571700e12b583", "jsonFile": "target-MJServer_RefactorLibrary-MinSizeRel-4d65348fd1f8e12883fa.json", "name": "MJServer_RefactorLibrary", "projectIndex": 16}, {"directoryIndex": 18, "id": "MJServer_RefactorTestphase1_test::@0a3c2e809899f2f13f5a", "jsonFile": "target-MJServer_RefactorTestphase1_test-MinSizeRel-9935cf707355e550556b.json", "name": "MJServer_RefactorTestphase1_test", "projectIndex": 18}, {"directoryIndex": 19, "id": "MJServer_RefactorTestsimple_abb_client::@933176848578d8c440c9", "jsonFile": "target-MJServer_RefactorTestsimple_abb_client-MinSizeRel-f002f3534bcc61ccfe1f.json", "name": "MJServer_RefactorTestsimple_abb_client", "projectIndex": 19}, {"directoryIndex": 20, "id": "MJServer_RefactorTestsimple_feeder_client::@80cf03468317b5d7fe2b", "jsonFile": "target-MJServer_RefactorTestsimple_feeder_client-MinSizeRel-8ea2476ae4136c9f7eb4.json", "name": "MJServer_RefactorTestsimple_feeder_client", "projectIndex": 20}, {"directoryIndex": 21, "id": "RoboticLaserMarkingAbbDriver::@9c3ce50b53cc3ff6eff5", "jsonFile": "target-RoboticLaserMarkingAbbDriver-MinSizeRel-64c7096d645e3de13080.json", "name": "RoboticLaserMarkingAbbDriver", "projectIndex": 21}, {"directoryIndex": 22, "id": "RoboticLaserMarkingLicenseGenerator::@59f7d9ae5c13d347c5f4", "jsonFile": "target-RoboticLaserMarkingLicenseGenerator-MinSizeRel-811ce292af71b6519a5c.json", "name": "RoboticLaserMarkingLicenseGenerator", "projectIndex": 22}, {"directoryIndex": 23, "id": "RoboticLaserMarkingRFIDDriver::@d1520424919af3a40272", "jsonFile": "target-RoboticLaserMarkingRFIDDriver-MinSizeRel-6cc03294da3fe2b814b4.json", "name": "RoboticLaserMarkingRFIDDriver", "projectIndex": 23}, {"directoryIndex": 25, "id": "RoboticLaserMarkingTestabbsocket::@bca145e2342aef659032", "jsonFile": "target-RoboticLaserMarkingTestabbsocket-MinSizeRel-053ccc9f3e486653bb8c.json", "name": "RoboticLaserMarkingTestabbsocket", "projectIndex": 25}, {"directoryIndex": 26, "id": "RoboticLaserMarkingTestlaser::@fef5c779e5dd4e5a5c7c", "jsonFile": "target-RoboticLaserMarkingTestlaser-MinSizeRel-5730580e3b21dbbe4853.json", "name": "RoboticLaserMarkingTestlaser", "projectIndex": 26}, {"directoryIndex": 27, "id": "RoboticLaserMarkingTestlaserUI::@7e6cec28b989a66fe139", "jsonFile": "target-RoboticLaserMarkingTestlaserUI-MinSizeRel-5b4f5bcd3560477192cb.json", "name": "RoboticLaserMarkingTestlaserUI", "projectIndex": 27}, {"directoryIndex": 28, "id": "RoboticLaserMarkingTestrfiddriver::@1df6ab8cb0b8ddb593f8", "jsonFile": "target-RoboticLaserMarkingTestrfiddriver-MinSizeRel-7f24ab6bb741d618c7ba.json", "name": "RoboticLaserMarkingTestrfiddriver", "projectIndex": 28}, {"directoryIndex": 29, "id": "RoboticLaserMarkingTestrfidserver::@75eb8879fc099b4640aa", "jsonFile": "target-RoboticLaserMarkingTestrfidserver-MinSizeRel-84cbf5010fdf400ad16e.json", "name": "RoboticLaserMarkingTestrfidserver", "projectIndex": 29}, {"directoryIndex": 30, "id": "RoboticLaserMarkingUI::@4e1303897d180b86ab2f", "jsonFile": "target-RoboticLaserMarkingUI-MinSizeRel-6988ffeaf6335ed2c666.json", "name": "RoboticLaserMarkingUI", "projectIndex": 30}, {"directoryIndex": 24, "id": "RoboticLaserMarkinglaserDriver::@cf8a855e37e415d7ca08", "jsonFile": "target-RoboticLaserMarkinglaserDriver-MinSizeRel-eed51488ef878a303dd3.json", "name": "RoboticLaserMarkinglaserDriver", "projectIndex": 24}, {"directoryIndex": 31, "id": "RoboticLaserMarkinglaserDriverSim::@30345e39cecb9bcc06b0", "jsonFile": "target-RoboticLaserMarkinglaserDriverSim-MinSizeRel-9729e52ad24c214ed0a5.json", "name": "RoboticLaserMarkinglaserDriverSim", "projectIndex": 31}, {"directoryIndex": 34, "id": "Testtest_abb_socket::@d803dad5c2b28052d845", "jsonFile": "target-Testtest_abb_socket-MinSizeRel-7b788cc75114e6479f70.json", "name": "Testtest_abb_socket", "projectIndex": 34}, {"directoryIndex": 35, "id": "Testtest_config_manager::@d885fae1c443095a1db7", "jsonFile": "target-Testtest_config_manager-MinSizeRel-ce8d4b10100a99e7cc5f.json", "name": "Testtest_config_manager", "projectIndex": 35}, {"directoryIndex": 36, "id": "Testtest_csv::@08c5adc7ee1d91091a97", "jsonFile": "target-Testtest_csv-MinSizeRel-38039ef762af3b110cff.json", "name": "Testtest_csv", "projectIndex": 36}, {"directoryIndex": 37, "id": "Testtest_event_listener::@889b7d31514c76e85624", "jsonFile": "target-Testtest_event_listener-MinSizeRel-26898b5438b6a5c0440a.json", "name": "Testtest_event_listener", "projectIndex": 37}, {"directoryIndex": 38, "id": "Testtest_executor::@7e2e321726c5f3e3edcb", "jsonFile": "target-Testtest_executor-MinSizeRel-3663a0e07e3f9d995031.json", "name": "Testtest_executor", "projectIndex": 38}, {"directoryIndex": 39, "id": "Testtest_executor_context::@e99d12ef8be33386882a", "jsonFile": "target-Testtest_executor_context-MinSizeRel-9f4dc38fb45b661c58e9.json", "name": "Testtest_executor_context", "projectIndex": 39}, {"directoryIndex": 40, "id": "Testtest_fa2204n_balance::@121a4898e406881ffb23", "jsonFile": "target-Testtest_fa2204n_balance-MinSizeRel-66adb63df206373a3976.json", "name": "Testtest_fa2204n_balance", "projectIndex": 40}, {"directoryIndex": 41, "id": "Testtest_fa2204n_balance_basic::@1dc2f4735d896dd76909", "jsonFile": "target-Testtest_fa2204n_balance_basic-MinSizeRel-ae70e002cf8a00593353.json", "name": "Testtest_fa2204n_balance_basic", "projectIndex": 41}, {"directoryIndex": 42, "id": "Testtest_fileutil::@b13ebbd4a3aafa6a0363", "jsonFile": "target-Testtest_fileutil-MinSizeRel-d0d1d444a545886ca90b.json", "name": "Testtest_fileutil", "projectIndex": 42}, {"directoryIndex": 43, "id": "Testtest_json::@46d5ce0aeb8e42b7284d", "jsonFile": "target-Testtest_json-MinSizeRel-844822b5876e30b16ff6.json", "name": "Testtest_json", "projectIndex": 43}, {"directoryIndex": 44, "id": "Testtest_license_manager::@7a0ade4671e16056f257", "jsonFile": "target-Testtest_license_manager-MinSizeRel-67727d8ae81e36e8b429.json", "name": "Testtest_license_manager", "projectIndex": 44}, {"directoryIndex": 45, "id": "Testtest_license_ui::@bb78083dcad0a236858d", "jsonFile": "target-Testtest_license_ui-MinSizeRel-93eb8c5c555e84982f82.json", "name": "Testtest_license_ui", "projectIndex": 45}, {"directoryIndex": 47, "id": "Testtest_network::@76c3a22b9f657d2ec026", "jsonFile": "target-Testtest_network-MinSizeRel-29d665e95d850ae5acd2.json", "name": "Testtest_network", "projectIndex": 47}, {"directoryIndex": 48, "id": "Testtest_serial::@a384ba46c8f7385844c3", "jsonFile": "target-Testtest_serial-MinSizeRel-44d78a27e4360a95e353.json", "name": "Testtest_serial", "projectIndex": 48}, {"directoryIndex": 49, "id": "Testtest_service_container::@03373f949cbd329c961c", "jsonFile": "target-Testtest_service_container-MinSizeRel-0cf943b74c73f2a182da.json", "name": "Testtest_service_container", "projectIndex": 49}, {"directoryIndex": 50, "id": "Testtest_socket::@6ff24ad6fb1c26ae0a57", "jsonFile": "target-Testtest_socket-MinSizeRel-2eb811f357914dba4263.json", "name": "Testtest_socket", "projectIndex": 50}, {"directoryIndex": 51, "id": "Testtest_sqlite::@e4ccddf7a4c29adf5bd6", "jsonFile": "target-Testtest_sqlite-MinSizeRel-d66282ca62c5013ab059.json", "name": "Testtest_sqlite", "projectIndex": 51}, {"directoryIndex": 52, "id": "Testtest_taskflow::@39116d767a15e3a891df", "jsonFile": "target-Testtest_taskflow-MinSizeRel-1f0536c33c9a2f2fc815.json", "name": "Testtest_taskflow", "projectIndex": 52}, {"directoryIndex": 53, "id": "Testtest_twoaixsrobot::@7d9d822efa235ac321e6", "jsonFile": "target-Testtest_twoaixsrobot-MinSizeRel-d2afde37d7bc49b8c089.json", "name": "Testtest_twoaixsrobot", "projectIndex": 53}, {"directoryIndex": 54, "id": "Testtest_xml::@80c0713ae5ba495463b6", "jsonFile": "target-Testtest_xml-MinSizeRel-f8821e58e3cfc4648454.json", "name": "Testtest_xml", "projectIndex": 54}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-MinSizeRel-1ca4f1b2e21860d5885e.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 1, "id": "fuxicommon::@58335e9a86196d0a97e7", "jsonFile": "target-fuxicommon-MinSizeRel-6b2f2172edce8be57525.json", "name": "fuxicommon", "projectIndex": 1}, {"directoryIndex": 32, "id": "fuxicore::@5cdc2d2ab21b3b9e72cd", "jsonFile": "target-fuxicore-MinSizeRel-5a0e77ae955246d7464f.json", "name": "fuxicore", "projectIndex": 32}, {"directoryIndex": 55, "id": "hardwaredriverAuboArcsDriver::@fd5e493b37d2bab880d9", "jsonFile": "target-hardwaredriverAuboArcsDriver-MinSizeRel-4b116e880200f660f213.json", "name": "hardwaredriverAuboArcsDriver", "projectIndex": 55}, {"directoryIndex": 56, "id": "hardwaredriverAuboDriver::@e75f830eb736a5dca1ce", "jsonFile": "target-hardwaredriverAuboDriver-MinSizeRel-91e2392a41de16ab2854.json", "name": "hardwaredriverAuboDriver", "projectIndex": 56}, {"directoryIndex": 57, "id": "hardwaredriverElectricGripperDriver::@e336ced093e233e6d829", "jsonFile": "target-hardwaredriverElectricGripperDriver-MinSizeRel-64ef091ff85f1ea18090.json", "name": "hardwaredriverElectricGripperDriver", "projectIndex": 57}, {"directoryIndex": 58, "id": "hardwaredriverHikVisionCamera::@bc252bb14595a0f09d26", "jsonFile": "target-hardwaredriverHikVisionCamera-MinSizeRel-fdeffc926a073d0fb254.json", "name": "hardwaredriverHikVisionCamera", "projectIndex": 58}, {"directoryIndex": 59, "id": "hardwaredriverLabelPrinter::@a99f8207d5ede56c5cae", "jsonFile": "target-hardwaredriverLabelPrinter-MinSizeRel-a921e06b0c5bfa25c65d.json", "name": "hardwaredriverLabelPrinter", "projectIndex": 59}, {"directoryIndex": 60, "id": "hardwaredriverMettlerBalance::@39b1645ff4c023a4e445", "jsonFile": "target-hardwaredriverMettlerBalance-MinSizeRel-347bb441141203443bd4.json", "name": "hardwaredriverMettlerBalance", "projectIndex": 60}, {"directoryIndex": 61, "id": "hardwaredriverOpcDa::@a2142d788288f069154a", "jsonFile": "target-hardwaredriverOpcDa-MinSizeRel-313e93a798548c1bc53b.json", "name": "hardwaredriverOpcDa", "projectIndex": 61}, {"directoryIndex": 62, "id": "hardwaredriverOpcUa::@7bf30a519259482def19", "jsonFile": "target-hardwaredriverOpcUa-MinSizeRel-ee16581711fd6a5c077b.json", "name": "hardwaredriverOpcUa", "projectIndex": 62}, {"directoryIndex": 33, "id": "hardwaredriverabbRobotDriver::@ccffbf515659b480dabe", "jsonFile": "target-hardwaredriverabbRobotDriver-MinSizeRel-f3270d5e4f59945a18df.json", "name": "hardwaredriverabbRobotDriver", "projectIndex": 33}, {"directoryIndex": 64, "id": "hardwaredriveragilerobotDriver::@14914dfd89874674d41d", "jsonFile": "target-hardwaredriveragilerobotDriver-MinSizeRel-0978293ba5dbb86ad731.json", "name": "hardwaredriveragilerobotDriver", "projectIndex": 64}, {"directoryIndex": 65, "id": "hardwaredriverfairinoDriver::@8a675cd9715b77cebac5", "jsonFile": "target-hardwaredriverfairinoDriver-MinSizeRel-dfa5ba024d7d5f4a029c.json", "name": "hardwaredriverfairinoDriver", "projectIndex": 65}, {"directoryIndex": 66, "id": "hardwaredriverjunduoHandDriver::@a89b79f2a82dbe076976", "jsonFile": "target-hardwaredriverjunduoHandDriver-MinSizeRel-01374d874db619792bfd.json", "name": "hardwaredriverjunduoHandDriver", "projectIndex": 66}, {"directoryIndex": 67, "id": "hardwaredrivermodbus::@2ef1e00ba9bcf504e7b3", "jsonFile": "target-hardwaredrivermodbus-MinSizeRel-651fb0239d8ca36462c9.json", "name": "hardwaredrivermodbus", "projectIndex": 67}, {"directoryIndex": 68, "id": "hardwaredriverserial::@e813d8aa5825a18a8390", "jsonFile": "target-hardwaredriverserial-MinSizeRel-9e698f876c63b5596b9b.json", "name": "hardwaredriverserial", "projectIndex": 68}, {"directoryIndex": 63, "id": "hardwaredriversocket::@e58766abf91db77f862b", "jsonFile": "target-hardwaredriversocket-MinSizeRel-b0fc4c743012d07721e9.json", "name": "hardwaredriversocket", "projectIndex": 63}, {"directoryIndex": 69, "id": "hardwaredriverusbcamera::@bfaa0a8775de30d870f0", "jsonFile": "target-hardwaredriverusbcamera-MinSizeRel-fecd26616d2eb0e443af.json", "name": "hardwaredriverusbcamera", "projectIndex": 69}, {"directoryIndex": 46, "id": "test_micro_dosing::@65e3165a8812532710ef", "jsonFile": "target-test_micro_dosing-MinSizeRel-85b4530f41cf3578820e.json", "name": "test_micro_dosing", "projectIndex": 46}, {"directoryIndex": 70, "id": "toolcalbuild::@a167bea24520843f7e43", "jsonFile": "target-toolcalbuild-MinSizeRel-b272bff079d9f8952e96.json", "name": "toolcalbuild", "projectIndex": 70}, {"directoryIndex": 72, "id": "toolcaltest::@9d9cd8ab08d8520a4ac3", "jsonFile": "target-toolcaltest-MinSizeRel-9b523a8b38bb8737a189.json", "name": "toolcaltest", "projectIndex": 72}, {"directoryIndex": 71, "id": "toolcameraCalibrator::@51e97efefc2313866ad5", "jsonFile": "target-toolcameraCalibrator-MinSizeRel-fd81c718a8fa375b4470.json", "name": "toolcameraCalibrator", "projectIndex": 71}, {"directoryIndex": 73, "id": "toolcommunication::@116eb0f160f4d76de168", "jsonFile": "target-toolcommunication-MinSizeRel-4641000daa968c87f2b1.json", "name": "toolcommunication", "projectIndex": 73}, {"directoryIndex": 74, "id": "toolhandeyecal::@d7390e83b7e4f79f633d", "jsonFile": "target-toolhandeyecal-MinSizeRel-2ce1eabfffd56f7e817b.json", "name": "toolhandeyecal", "projectIndex": 74}, {"directoryIndex": 75, "id": "toolhandeyecaltest::@ae279e4383f26a866133", "jsonFile": "target-toolhandeyecaltest-MinSizeRel-515f5dc571c138e2b94b.json", "name": "toolhandeyecaltest", "projectIndex": 75}, {"directoryIndex": 76, "id": "toolhandeyecaluihandeyecalui::@64c63141ea1fe7a116f6", "jsonFile": "target-toolhandeyecaluihandeyecalui-MinSizeRel-518d75dfa803d0f5db6b.json", "name": "toolhandeyecaluihandeyecalui", "projectIndex": 76}, {"directoryIndex": 77, "id": "toolhandeyecaluipath::@f8ebbc87f7fac77328c8", "jsonFile": "target-toolhandeyecaluipath-MinSizeRel-78375c53f9e86f67ce50.json", "name": "toolhandeyecaluipath", "projectIndex": 77}, {"directoryIndex": 78, "id": "toolhandeyecaluipathAuto::@f4fb3041b29f01391299", "jsonFile": "target-toolhandeyecaluipathAuto-MinSizeRel-b70de9aa172175f84604.json", "name": "toolhandeyecaluipathAuto", "projectIndex": 78}, {"directoryIndex": 79, "id": "toolverify_calibration::@efca4bcc8ad294d52f3d", "jsonFile": "target-toolverify_calibration-MinSizeRel-dcc048f0075ea2bba0fd.json", "name": "toolverify_calibration", "projectIndex": 79}]}, {"directories": [{"build": ".", "childIndexes": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79], "hasInstallRule": true, "jsonFile": "directory-.-RelWithDebInfo-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [1, 130]}, {"build": "fuxicommon", "hasInstallRule": true, "jsonFile": "directory-fuxicommon-RelWithDebInfo-45b01aad2fc6492274cb.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 1, "source": "fuxicommon", "targetIndexes": [5, 131]}, {"build": "Analysis_Robot/algorithms/pouringControl", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.algorithms.pouringControl-RelWithDebInfo-b6a4a096167825bdc477.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 2, "source": "Analysis_Robot/algorithms/pouringControl", "targetIndexes": [8, 82]}, {"build": "Analysis_Robot/drivers/aixsDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.aixsDriver-RelWithDebInfo-acad4faf4f883a05b16c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 3, "source": "Analysis_Robot/drivers/aixsDriver", "targetIndexes": [0, 84]}, {"build": "Analysis_Robot/drivers/plcDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.plcDriver-RelWithDebInfo-1785bcd9213080dbd037.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 4, "source": "Analysis_Robot/drivers/plcDriver", "targetIndexes": [14, 88]}, {"build": "Analysis_Robot/drivers/balanceDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.balanceDriver-RelWithDebInfo-bd88b671d17006b62362.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 5, "source": "Analysis_Robot/drivers/balanceDriver", "targetIndexes": [17, 85]}, {"build": "Analysis_Robot/drivers/heatingMagneticStirrerDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.heatingMagneticStirrerDriver-RelWithDebInfo-58f3ac9f2a5c9e15f72a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 6, "source": "Analysis_Robot/drivers/heatingMagneticStirrerDriver", "targetIndexes": [20, 86]}, {"build": "Analysis_Robot/drivers/moistureAnalyzerDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.moistureAnalyzerDriver-RelWithDebInfo-013e15aafee981d8281c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 7, "source": "Analysis_Robot/drivers/moistureAnalyzerDriver", "targetIndexes": [23, 87]}, {"build": "Analysis_Robot/drivers/robotDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.robotDriver-RelWithDebInfo-2c83fb91a80b187dc6d1.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 8, "source": "Analysis_Robot/drivers/robotDriver", "targetIndexes": [26, 90]}, {"build": "Analysis_Robot/drivers/restInterfaceDriver", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.drivers.restInterfaceDriver-RelWithDebInfo-32537dba61e43ea53bc5.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 9, "source": "Analysis_Robot/drivers/restInterfaceDriver", "targetIndexes": [29, 89]}, {"build": "Analysis_Robot/App", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.App-RelWithDebInfo-e2ede17c1187489a25e9.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 10, "source": "Analysis_Robot/App", "targetIndexes": [32, 80]}, {"build": "Analysis_Robot/algorithms/coordinateTransform", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.algorithms.coordinateTransform-RelWithDebInfo-0dc862cc663c92448147.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 11, "source": "Analysis_Robot/algorithms/coordinateTransform", "targetIndexes": [35, 81]}, {"build": "Analysis_Robot/algorithms/tcpPositionMaintain", "hasInstallRule": true, "jsonFile": "directory-Analysis_Robot.algorithms.tcpPositionMaintain-RelWithDebInfo-14eb9bf209fc848d31d1.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 12, "source": "Analysis_Robot/algorithms/tcpPositionMaintain", "targetIndexes": [38, 83]}, {"build": "Analysis_Robot/test/heaterApiTest", "jsonFile": "directory-Analysis_Robot.test.heaterApiTest-RelWithDebInfo-b999f5688c4067f4e68d.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 13, "source": "Analysis_Robot/test/heaterApiTest", "targetIndexes": [41, 91]}, {"build": "Analysis_Robot/test/heatingMagneticStirrerDriver", "jsonFile": "directory-Analysis_Robot.test.heatingMagneticStirrerDriver-RelWithDebInfo-e5cbd850de4545ce6cdf.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 14, "source": "Analysis_Robot/test/heatingMagneticStirrerDriver", "targetIndexes": [44, 92]}, {"build": "MJServer/APP", "hasInstallRule": true, "jsonFile": "directory-MJServer.APP-RelWithDebInfo-9a0005353ac4d44a8cb8.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 15, "source": "MJServer/APP", "targetIndexes": [47, 93]}, {"build": "MJServer_Refactor/Library", "hasInstallRule": true, "jsonFile": "directory-MJServer_Refactor.Library-RelWithDebInfo-9ec6a9fe118a63a425b3.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 16, "source": "MJServer_Refactor/Library", "targetIndexes": [50, 95]}, {"build": "MJServer_Refactor/App", "jsonFile": "directory-MJServer_Refactor.App-RelWithDebInfo-25c2817210280de79843.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 17, "source": "MJServer_Refactor/App", "targetIndexes": [53, 94]}, {"build": "MJServer_Refactor/Test/phase1_test", "jsonFile": "directory-MJServer_Refactor.Test.phase1_test-RelWithDebInfo-0a76c9c92d103a235c64.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 18, "source": "MJServer_Refactor/Test/phase1_test", "targetIndexes": [56, 96]}, {"build": "MJServer_Refactor/Test/simple_abb_client", "jsonFile": "directory-MJServer_Refactor.Test.simple_abb_client-RelWithDebInfo-d55604710c3a3fb8f0bb.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 19, "source": "MJServer_Refactor/Test/simple_abb_client", "targetIndexes": [59, 97]}, {"build": "MJServer_Refactor/Test/simple_feeder_client", "jsonFile": "directory-MJServer_Refactor.Test.simple_feeder_client-RelWithDebInfo-615291d1f0d9de4ac87c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 20, "source": "MJServer_Refactor/Test/simple_feeder_client", "targetIndexes": [62, 98]}, {"build": "RoboticLaserMarking/AbbDriver", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.AbbDriver-RelWithDebInfo-6a2fe08206f932672311.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 21, "source": "RoboticLaserMarking/AbbDriver", "targetIndexes": [65, 99]}, {"build": "RoboticLaserMarking/LicenseGenerator", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.LicenseGenerator-RelWithDebInfo-5d8bc3a4c1c58bab6f7a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 22, "source": "RoboticLaserMarking/LicenseGenerator", "targetIndexes": [68, 100]}, {"build": "RoboticLaserMarking/RFIDDriver", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.RFIDDriver-RelWithDebInfo-ce034b8fd7255dd94677.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 23, "source": "RoboticLaserMarking/RFIDDriver", "targetIndexes": [71, 101]}, {"build": "RoboticLaserMarking/laserDriver", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.laserDriver-RelWithDebInfo-50ac02b6f964d67969b6.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 24, "source": "RoboticLaserMarking/laserDriver", "targetIndexes": [74, 108]}, {"build": "RoboticLaserMarking/Test/abbsocket", "jsonFile": "directory-RoboticLaserMarking.Test.abbsocket-RelWithDebInfo-c54e19bdb9ae31bded65.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 25, "source": "RoboticLaserMarking/Test/abbsocket", "targetIndexes": [77, 102]}, {"build": "RoboticLaserMarking/Test/laser", "jsonFile": "directory-RoboticLaserMarking.Test.laser-RelWithDebInfo-fda96428a10838b20da0.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 26, "source": "RoboticLaserMarking/Test/laser", "targetIndexes": [6, 103]}, {"build": "RoboticLaserMarking/Test/laserUI", "jsonFile": "directory-RoboticLaserMarking.Test.laserUI-RelWithDebInfo-906cc9e5b37f734dfdd8.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 27, "source": "RoboticLaserMarking/Test/laserUI", "targetIndexes": [10, 104]}, {"build": "RoboticLaserMarking/Test/rfiddriver", "jsonFile": "directory-RoboticLaserMarking.Test.rfiddriver-RelWithDebInfo-f51044958bce4e484373.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 28, "source": "RoboticLaserMarking/Test/rfiddriver", "targetIndexes": [19, 105]}, {"build": "RoboticLaserMarking/Test/rfidserver", "jsonFile": "directory-RoboticLaserMarking.Test.rfidserver-RelWithDebInfo-7e022394fa2631804126.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 29, "source": "RoboticLaserMarking/Test/rfidserver", "targetIndexes": [28, 106]}, {"build": "RoboticLaserMarking/UI", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.UI-RelWithDebInfo-a0a826d45ad2ea8615b1.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 30, "source": "RoboticLaserMarking/UI", "targetIndexes": [37, 107]}, {"build": "RoboticLaserMarking/laserDriverSim", "hasInstallRule": true, "jsonFile": "directory-RoboticLaserMarking.laserDriverSim-RelWithDebInfo-9fa894fbfa57d970660d.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 31, "source": "RoboticLaserMarking/laserDriverSim", "targetIndexes": [46, 109]}, {"build": "fuxicore", "hasInstallRule": true, "jsonFile": "directory-fuxicore-RelWithDebInfo-6019081bb93cc97e5bd3.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 32, "source": "fuxicore", "targetIndexes": [55, 132]}, {"build": "hardwaredriver/abbRobotDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.abbRobotDriver-RelWithDebInfo-6dd56340ab873b7947eb.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 33, "source": "hardwaredriver/abbRobotDriver", "targetIndexes": [64, 141]}, {"build": "Test/test_abb_socket", "jsonFile": "directory-Test.test_abb_socket-RelWithDebInfo-b83d4bb5753115673b1e.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 34, "source": "Test/test_abb_socket", "targetIndexes": [73, 110]}, {"build": "Test/test_config_manager", "jsonFile": "directory-Test.test_config_manager-RelWithDebInfo-b1e58fcc0034795c6ae3.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 35, "source": "Test/test_config_manager", "targetIndexes": [7, 111]}, {"build": "Test/test_csv", "jsonFile": "directory-Test.test_csv-RelWithDebInfo-46cd2d2264edf5d7592e.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 36, "source": "Test/test_csv", "targetIndexes": [34, 112]}, {"build": "Test/test_event_listener", "jsonFile": "directory-Test.test_event_listener-RelWithDebInfo-87efd742cb5ddc812bd7.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 37, "source": "Test/test_event_listener", "targetIndexes": [61, 113]}, {"build": "Test/test_executor", "jsonFile": "directory-Test.test_executor-RelWithDebInfo-a98e8731e17c62507f58.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 38, "source": "Test/test_executor", "targetIndexes": [25, 114]}, {"build": "Test/test_executor_context", "jsonFile": "directory-Test.test_executor_context-RelWithDebInfo-7ab477d5e8562815cbb7.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 39, "source": "Test/test_executor_context", "targetIndexes": [79, 115]}, {"build": "Test/test_fa2204n_balance", "jsonFile": "directory-Test.test_fa2204n_balance-RelWithDebInfo-7560d87111b5ff21f33f.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 40, "source": "Test/test_fa2204n_balance", "targetIndexes": [52, 116]}, {"build": "Test/test_fa2204n_balance_basic", "jsonFile": "directory-Test.test_fa2204n_balance_basic-RelWithDebInfo-c5e19bbf14befd24dc6e.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 41, "source": "Test/test_fa2204n_balance_basic", "targetIndexes": [70, 117]}, {"build": "Test/test_fileutil", "jsonFile": "directory-Test.test_fileutil-RelWithDebInfo-6a4234fe4fd9c32b60c0.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 42, "source": "Test/test_fileutil", "targetIndexes": [43, 118]}, {"build": "Test/test_json", "jsonFile": "directory-Test.test_json-RelWithDebInfo-70316fee5b76b6255c9c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 43, "source": "Test/test_json", "targetIndexes": [16, 119]}, {"build": "Test/test_license_manager", "jsonFile": "directory-Test.test_license_manager-RelWithDebInfo-adf854e580c4cdcb7fc5.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 44, "source": "Test/test_license_manager", "targetIndexes": [76, 120]}, {"build": "Test/test_license_ui", "hasInstallRule": true, "jsonFile": "directory-Test.test_license_ui-RelWithDebInfo-17e8c4b69d69a26aaf2a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 45, "source": "Test/test_license_ui", "targetIndexes": [67, 121]}, {"build": "Test/test_micro_dosing", "jsonFile": "directory-Test.test_micro_dosing-RelWithDebInfo-5e830cdb711d4e75b3ab.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 46, "source": "Test/test_micro_dosing", "targetIndexes": [58, 149]}, {"build": "Test/test_network", "jsonFile": "directory-Test.test_network-RelWithDebInfo-802d04b39615713bdaa5.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 47, "source": "Test/test_network", "targetIndexes": [49, 122]}, {"build": "Test/test_serial", "jsonFile": "directory-Test.test_serial-RelWithDebInfo-1539ef84150cf55d007b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 48, "source": "Test/test_serial", "targetIndexes": [40, 123]}, {"build": "Test/test_service_container", "jsonFile": "directory-Test.test_service_container-RelWithDebInfo-3c607165866062790453.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 49, "source": "Test/test_service_container", "targetIndexes": [31, 124]}, {"build": "Test/test_socket", "jsonFile": "directory-Test.test_socket-RelWithDebInfo-7646e8df0e04f7d933fc.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 50, "source": "Test/test_socket", "targetIndexes": [11, 125]}, {"build": "Test/test_sqlite", "jsonFile": "directory-Test.test_sqlite-RelWithDebInfo-a3f7e251b42c8d6e85bd.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 51, "source": "Test/test_sqlite", "targetIndexes": [13, 126]}, {"build": "Test/test_taskflow", "jsonFile": "directory-Test.test_taskflow-RelWithDebInfo-338290a1d2d2c191692b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 52, "source": "Test/test_taskflow", "targetIndexes": [4, 127]}, {"build": "Test/test_twoaixsrobot", "jsonFile": "directory-Test.test_twoaixsrobot-RelWithDebInfo-829343d8fb5c82be5c03.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 53, "source": "Test/test_twoaixsrobot", "targetIndexes": [78, 128]}, {"build": "Test/test_xml", "jsonFile": "directory-Test.test_xml-RelWithDebInfo-91dd4b4ea7db1ac593a4.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 54, "source": "Test/test_xml", "targetIndexes": [75, 129]}, {"build": "hardwaredriver/AuboArcsDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.AuboArcsDriver-RelWithDebInfo-c8464a5a4e222d8a4324.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 55, "source": "hardwaredriver/AuboArcsDriver", "targetIndexes": [72, 133]}, {"build": "hardwaredriver/AuboDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.AuboDriver-RelWithDebInfo-f6ef18b990c62843287d.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 56, "source": "hardwaredriver/AuboDriver", "targetIndexes": [69, 134]}, {"build": "hardwaredriver/ElectricGripperDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.ElectricGripperDriver-RelWithDebInfo-9050ee0aa574d02a13c1.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 57, "source": "hardwaredriver/ElectricGripperDriver", "targetIndexes": [66, 135]}, {"build": "hardwaredriver/HikVisionCamera", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.HikVisionCamera-RelWithDebInfo-2f15954957700dc82692.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 58, "source": "hardwaredriver/HikVisionCamera", "targetIndexes": [63, 136]}, {"build": "hardwaredriver/LabelPrinter", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.LabelPrinter-RelWithDebInfo-a98e344906e45e035c7a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 59, "source": "hardwaredriver/LabelPrinter", "targetIndexes": [60, 137]}, {"build": "hardwaredriver/MettlerBalance", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.MettlerBalance-RelWithDebInfo-f65a38351f7de3288dc7.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 60, "source": "hardwaredriver/MettlerBalance", "targetIndexes": [57, 138]}, {"build": "hardwaredriver/OpcDa", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.OpcDa-RelWithDebInfo-5c7bc29dbda15a9b8ae4.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 61, "source": "hardwaredriver/OpcDa", "targetIndexes": [54, 139]}, {"build": "hardwaredriver/OpcUa", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.OpcUa-RelWithDebInfo-36fae1fe277182987806.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 62, "source": "hardwaredriver/OpcUa", "targetIndexes": [51, 140]}, {"build": "hardwaredriver/socket", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.socket-RelWithDebInfo-b084aeaa98c55ca54242.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 63, "source": "hardwaredriver/socket", "targetIndexes": [48, 147]}, {"build": "hardwaredriver/agilerobotDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.agilerobotDriver-RelWithDebInfo-be0c33e6eb56c2575bd9.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 64, "source": "hardwaredriver/agilerobotDriver", "targetIndexes": [45, 142]}, {"build": "hardwaredriver/fairinoDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.fairinoDriver-RelWithDebInfo-b638d5fdff8da064311b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 65, "source": "hardwaredriver/fairinoDriver", "targetIndexes": [3, 143]}, {"build": "hardwaredriver/junduoHandDriver", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.junduoHandDriver-RelWithDebInfo-e7024192621c226dc16f.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 66, "source": "hardwaredriver/junduoHandDriver", "targetIndexes": [2, 144]}, {"build": "hardwaredriver/modbus", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.modbus-RelWithDebInfo-1f7cf0a90d4506a41f5a.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 67, "source": "hardwaredriver/modbus", "targetIndexes": [9, 145]}, {"build": "hardwaredriver/serial", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.serial-RelWithDebInfo-40c48bd4e0de0af8828e.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 68, "source": "hardwaredriver/serial", "targetIndexes": [12, 146]}, {"build": "hardwaredriver/usbcamera", "hasInstallRule": true, "jsonFile": "directory-hardwaredriver.usbcamera-RelWithDebInfo-968bee8cafdf7e5f9ccf.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 69, "source": "hardwaredriver/usbcamera", "targetIndexes": [30, 148]}, {"build": "tool/calbuild", "jsonFile": "directory-tool.calbuild-RelWithDebInfo-9edb4e157f9b6e54846b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 70, "source": "tool/calbuild", "targetIndexes": [15, 150]}, {"build": "tool/cameraCalibrator", "hasInstallRule": true, "jsonFile": "directory-tool.cameraCalibrator-RelWithDebInfo-a861825f388659f4f715.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 71, "source": "tool/cameraCalibrator", "targetIndexes": [18, 152]}, {"build": "tool/caltest", "hasInstallRule": true, "jsonFile": "directory-tool.caltest-RelWithDebInfo-63fb02a9aaef7a0c834c.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 72, "source": "tool/caltest", "targetIndexes": [21, 151]}, {"build": "tool/communication", "hasInstallRule": true, "jsonFile": "directory-tool.communication-RelWithDebInfo-324152a549fff44395ce.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 73, "source": "tool/communication", "targetIndexes": [22, 153]}, {"build": "tool/handeyecal", "hasInstallRule": true, "jsonFile": "directory-tool.handeyecal-RelWithDebInfo-77ea94e986eab795f767.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 74, "source": "tool/handeyecal", "targetIndexes": [24, 154]}, {"build": "tool/handeyecaltest", "jsonFile": "directory-tool.handeyecaltest-RelWithDebInfo-f9f22629594fc4ab2dbc.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 75, "source": "tool/handeyecaltest", "targetIndexes": [27, 155]}, {"build": "tool/handeyecalui/handeyecalui", "jsonFile": "directory-tool.handeyecalui.handeyecalui-RelWithDebInfo-1fafdc8136eaae57e66b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 76, "source": "tool/handeyecalui/handeyecalui", "targetIndexes": [33, 156]}, {"build": "tool/handeyecaluipath", "jsonFile": "directory-tool.handeyecaluipath-RelWithDebInfo-e479629856c728efc292.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 77, "source": "tool/handeyecaluipath", "targetIndexes": [36, 157]}, {"build": "tool/handeyecaluipathAuto", "jsonFile": "directory-tool.handeyecaluipathAuto-RelWithDebInfo-0a177976965c3cc59f22.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 78, "source": "tool/handeyecaluipathAuto", "targetIndexes": [39, 158]}, {"build": "tool/verify_calibration", "jsonFile": "directory-tool.verify_calibration-RelWithDebInfo-2d3946f9054860a13e8b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 79, "source": "tool/verify_calibration", "targetIndexes": [42, 159]}], "name": "RelWithDebInfo", "projects": [{"childIndexes": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79], "directoryIndexes": [0], "name": "Project", "targetIndexes": [1, 130]}, {"directoryIndexes": [1], "name": "fuxicommon", "parentIndex": 0, "targetIndexes": [5, 131]}, {"directoryIndexes": [2], "name": "Analysis_RobotalgorithmspouringControl", "parentIndex": 0, "targetIndexes": [8, 82]}, {"directoryIndexes": [3], "name": "Analysis_RobotdriversaixsDriver", "parentIndex": 0, "targetIndexes": [0, 84]}, {"directoryIndexes": [4], "name": "Analysis_RobotdriversplcDriver", "parentIndex": 0, "targetIndexes": [14, 88]}, {"directoryIndexes": [5], "name": "Analysis_RobotdriversbalanceDriver", "parentIndex": 0, "targetIndexes": [17, 85]}, {"directoryIndexes": [6], "name": "Analysis_RobotdriversheatingMagneticStirrerDriver", "parentIndex": 0, "targetIndexes": [20, 86]}, {"directoryIndexes": [7], "name": "Analysis_RobotdriversmoistureAnalyzerDriver", "parentIndex": 0, "targetIndexes": [23, 87]}, {"directoryIndexes": [8], "name": "Analysis_RobotdriversrobotDriver", "parentIndex": 0, "targetIndexes": [26, 90]}, {"directoryIndexes": [9], "name": "Analysis_RobotdriversrestInterfaceDriver", "parentIndex": 0, "targetIndexes": [29, 89]}, {"directoryIndexes": [10], "name": "Analysis_RobotApp", "parentIndex": 0, "targetIndexes": [32, 80]}, {"directoryIndexes": [11], "name": "Analysis_RobotalgorithmscoordinateTransform", "parentIndex": 0, "targetIndexes": [35, 81]}, {"directoryIndexes": [12], "name": "Analysis_RobotalgorithmstcpPositionMaintain", "parentIndex": 0, "targetIndexes": [38, 83]}, {"directoryIndexes": [13], "name": "Analysis_RobottestheaterApiTest", "parentIndex": 0, "targetIndexes": [41, 91]}, {"directoryIndexes": [14], "name": "Analysis_RobottestheatingMagneticStirrerDriver", "parentIndex": 0, "targetIndexes": [44, 92]}, {"directoryIndexes": [15], "name": "MJServerAPP", "parentIndex": 0, "targetIndexes": [47, 93]}, {"directoryIndexes": [16], "name": "MJServer_RefactorLibrary", "parentIndex": 0, "targetIndexes": [50, 95]}, {"directoryIndexes": [17], "name": "MJServer_RefactorApp", "parentIndex": 0, "targetIndexes": [53, 94]}, {"directoryIndexes": [18], "name": "MJServer_RefactorTestphase1_test", "parentIndex": 0, "targetIndexes": [56, 96]}, {"directoryIndexes": [19], "name": "MJServer_RefactorTestsimple_abb_client", "parentIndex": 0, "targetIndexes": [59, 97]}, {"directoryIndexes": [20], "name": "MJServer_RefactorTestsimple_feeder_client", "parentIndex": 0, "targetIndexes": [62, 98]}, {"directoryIndexes": [21], "name": "RoboticLaserMarkingAbbDriver", "parentIndex": 0, "targetIndexes": [65, 99]}, {"directoryIndexes": [22], "name": "RoboticLaserMarkingLicenseGenerator", "parentIndex": 0, "targetIndexes": [68, 100]}, {"directoryIndexes": [23], "name": "RoboticLaserMarkingRFIDDriver", "parentIndex": 0, "targetIndexes": [71, 101]}, {"directoryIndexes": [24], "name": "RoboticLaserMarkinglaserDriver", "parentIndex": 0, "targetIndexes": [74, 108]}, {"directoryIndexes": [25], "name": "RoboticLaserMarkingTestabbsocket", "parentIndex": 0, "targetIndexes": [77, 102]}, {"directoryIndexes": [26], "name": "RoboticLaserMarkingTestlaser", "parentIndex": 0, "targetIndexes": [6, 103]}, {"directoryIndexes": [27], "name": "RoboticLaserMarkingTestlaserUI", "parentIndex": 0, "targetIndexes": [10, 104]}, {"directoryIndexes": [28], "name": "RoboticLaserMarkingTestrfiddriver", "parentIndex": 0, "targetIndexes": [19, 105]}, {"directoryIndexes": [29], "name": "RoboticLaserMarkingTestrfidserver", "parentIndex": 0, "targetIndexes": [28, 106]}, {"directoryIndexes": [30], "name": "RoboticLaserMarkingUI", "parentIndex": 0, "targetIndexes": [37, 107]}, {"directoryIndexes": [31], "name": "RoboticLaserMarkinglaserDriverSim", "parentIndex": 0, "targetIndexes": [46, 109]}, {"directoryIndexes": [32], "name": "fuxicore", "parentIndex": 0, "targetIndexes": [55, 132]}, {"directoryIndexes": [33], "name": "hardwaredriverabbRobotDriver", "parentIndex": 0, "targetIndexes": [64, 141]}, {"directoryIndexes": [34], "name": "Testtest_abb_socket", "parentIndex": 0, "targetIndexes": [73, 110]}, {"directoryIndexes": [35], "name": "Testtest_config_manager", "parentIndex": 0, "targetIndexes": [7, 111]}, {"directoryIndexes": [36], "name": "Testtest_csv", "parentIndex": 0, "targetIndexes": [34, 112]}, {"directoryIndexes": [37], "name": "Testtest_event_listener", "parentIndex": 0, "targetIndexes": [61, 113]}, {"directoryIndexes": [38], "name": "Testtest_executor", "parentIndex": 0, "targetIndexes": [25, 114]}, {"directoryIndexes": [39], "name": "Testtest_executor_context", "parentIndex": 0, "targetIndexes": [79, 115]}, {"directoryIndexes": [40], "name": "Testtest_fa2204n_balance", "parentIndex": 0, "targetIndexes": [52, 116]}, {"directoryIndexes": [41], "name": "Testtest_fa2204n_balance_basic", "parentIndex": 0, "targetIndexes": [70, 117]}, {"directoryIndexes": [42], "name": "Testtest_fileutil", "parentIndex": 0, "targetIndexes": [43, 118]}, {"directoryIndexes": [43], "name": "Testtest_json", "parentIndex": 0, "targetIndexes": [16, 119]}, {"directoryIndexes": [44], "name": "Testtest_license_manager", "parentIndex": 0, "targetIndexes": [76, 120]}, {"directoryIndexes": [45], "name": "Testtest_license_ui", "parentIndex": 0, "targetIndexes": [67, 121]}, {"directoryIndexes": [46], "name": "test_micro_dosing", "parentIndex": 0, "targetIndexes": [58, 149]}, {"directoryIndexes": [47], "name": "Testtest_network", "parentIndex": 0, "targetIndexes": [49, 122]}, {"directoryIndexes": [48], "name": "Testtest_serial", "parentIndex": 0, "targetIndexes": [40, 123]}, {"directoryIndexes": [49], "name": "Testtest_service_container", "parentIndex": 0, "targetIndexes": [31, 124]}, {"directoryIndexes": [50], "name": "Testtest_socket", "parentIndex": 0, "targetIndexes": [11, 125]}, {"directoryIndexes": [51], "name": "Testtest_sqlite", "parentIndex": 0, "targetIndexes": [13, 126]}, {"directoryIndexes": [52], "name": "Testtest_taskflow", "parentIndex": 0, "targetIndexes": [4, 127]}, {"directoryIndexes": [53], "name": "Testtest_twoaixsrobot", "parentIndex": 0, "targetIndexes": [78, 128]}, {"directoryIndexes": [54], "name": "Testtest_xml", "parentIndex": 0, "targetIndexes": [75, 129]}, {"directoryIndexes": [55], "name": "hardwaredriverAuboArcsDriver", "parentIndex": 0, "targetIndexes": [72, 133]}, {"directoryIndexes": [56], "name": "hardwaredriverAuboDriver", "parentIndex": 0, "targetIndexes": [69, 134]}, {"directoryIndexes": [57], "name": "hardwaredriverElectricGripperDriver", "parentIndex": 0, "targetIndexes": [66, 135]}, {"directoryIndexes": [58], "name": "hardwaredriverHikVisionCamera", "parentIndex": 0, "targetIndexes": [63, 136]}, {"directoryIndexes": [59], "name": "hardwaredriverLabelPrinter", "parentIndex": 0, "targetIndexes": [60, 137]}, {"directoryIndexes": [60], "name": "hardwaredriverMettlerBalance", "parentIndex": 0, "targetIndexes": [57, 138]}, {"directoryIndexes": [61], "name": "hardwaredriverOpcDa", "parentIndex": 0, "targetIndexes": [54, 139]}, {"directoryIndexes": [62], "name": "hardwaredriverOpcUa", "parentIndex": 0, "targetIndexes": [51, 140]}, {"directoryIndexes": [63], "name": "hardwaredriversocket", "parentIndex": 0, "targetIndexes": [48, 147]}, {"directoryIndexes": [64], "name": "hardwaredriveragilerobotDriver", "parentIndex": 0, "targetIndexes": [45, 142]}, {"directoryIndexes": [65], "name": "hardwaredriverfairinoDriver", "parentIndex": 0, "targetIndexes": [3, 143]}, {"directoryIndexes": [66], "name": "hardwaredriverjunduoHandDriver", "parentIndex": 0, "targetIndexes": [2, 144]}, {"directoryIndexes": [67], "name": "hardwaredrivermodbus", "parentIndex": 0, "targetIndexes": [9, 145]}, {"directoryIndexes": [68], "name": "hardwaredriverserial", "parentIndex": 0, "targetIndexes": [12, 146]}, {"directoryIndexes": [69], "name": "hardwaredriverusbcamera", "parentIndex": 0, "targetIndexes": [30, 148]}, {"directoryIndexes": [70], "name": "toolcalbuild", "parentIndex": 0, "targetIndexes": [15, 150]}, {"directoryIndexes": [71], "name": "toolcameraCalibrator", "parentIndex": 0, "targetIndexes": [18, 152]}, {"directoryIndexes": [72], "name": "toolcaltest", "parentIndex": 0, "targetIndexes": [21, 151]}, {"directoryIndexes": [73], "name": "toolcommunication", "parentIndex": 0, "targetIndexes": [22, 153]}, {"directoryIndexes": [74], "name": "toolhandeyecal", "parentIndex": 0, "targetIndexes": [24, 154]}, {"directoryIndexes": [75], "name": "toolhandeyecaltest", "parentIndex": 0, "targetIndexes": [27, 155]}, {"directoryIndexes": [76], "name": "toolhandeyecaluihandeyecalui", "parentIndex": 0, "targetIndexes": [33, 156]}, {"directoryIndexes": [77], "name": "toolhandeyecaluipath", "parentIndex": 0, "targetIndexes": [36, 157]}, {"directoryIndexes": [78], "name": "toolhandeyecaluipathAuto", "parentIndex": 0, "targetIndexes": [39, 158]}, {"directoryIndexes": [79], "name": "toolverify_calibration", "parentIndex": 0, "targetIndexes": [42, 159]}], "targets": [{"directoryIndex": 3, "id": "ALL_BUILD::@19f706e88e1d43a9565c", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-85da0b39f38e7483b20e.json", "name": "ALL_BUILD", "projectIndex": 3}, {"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-342279978964b07ce418.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 66, "id": "ALL_BUILD::@a89b79f2a82dbe076976", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-40a7d901ed8e10a0c7f5.json", "name": "ALL_BUILD", "projectIndex": 66}, {"directoryIndex": 65, "id": "ALL_BUILD::@8a675cd9715b77cebac5", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-2e630e3e3d96ead79510.json", "name": "ALL_BUILD", "projectIndex": 65}, {"directoryIndex": 52, "id": "ALL_BUILD::@39116d767a15e3a891df", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-a7eeeef2e82f92727250.json", "name": "ALL_BUILD", "projectIndex": 52}, {"directoryIndex": 1, "id": "ALL_BUILD::@58335e9a86196d0a97e7", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-0841e45febd559571c13.json", "name": "ALL_BUILD", "projectIndex": 1}, {"directoryIndex": 26, "id": "ALL_BUILD::@fef5c779e5dd4e5a5c7c", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-68f406d96290d6b7df07.json", "name": "ALL_BUILD", "projectIndex": 26}, {"directoryIndex": 35, "id": "ALL_BUILD::@d885fae1c443095a1db7", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-2569c2c10fad6870c72b.json", "name": "ALL_BUILD", "projectIndex": 35}, {"directoryIndex": 2, "id": "ALL_BUILD::@07001b74ee4af3db8a6e", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-a93df49a17068820adac.json", "name": "ALL_BUILD", "projectIndex": 2}, {"directoryIndex": 67, "id": "ALL_BUILD::@2ef1e00ba9bcf504e7b3", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-29cfabbfeabe66f20220.json", "name": "ALL_BUILD", "projectIndex": 67}, {"directoryIndex": 27, "id": "ALL_BUILD::@7e6cec28b989a66fe139", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-07b7923a48da597b2076.json", "name": "ALL_BUILD", "projectIndex": 27}, {"directoryIndex": 50, "id": "ALL_BUILD::@6ff24ad6fb1c26ae0a57", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-5e2fbceb8d24c84e42cf.json", "name": "ALL_BUILD", "projectIndex": 50}, {"directoryIndex": 68, "id": "ALL_BUILD::@e813d8aa5825a18a8390", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-19c438a9df50cace71d5.json", "name": "ALL_BUILD", "projectIndex": 68}, {"directoryIndex": 51, "id": "ALL_BUILD::@e4ccddf7a4c29adf5bd6", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-119fa9189d354d4060ac.json", "name": "ALL_BUILD", "projectIndex": 51}, {"directoryIndex": 4, "id": "ALL_BUILD::@97966baa9ab9c14a9bcf", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-5339ba61d8ad5be7b663.json", "name": "ALL_BUILD", "projectIndex": 4}, {"directoryIndex": 70, "id": "ALL_BUILD::@a167bea24520843f7e43", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-da1d6472f79bdb30833d.json", "name": "ALL_BUILD", "projectIndex": 70}, {"directoryIndex": 43, "id": "ALL_BUILD::@46d5ce0aeb8e42b7284d", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-430b2635e5f4a69b8869.json", "name": "ALL_BUILD", "projectIndex": 43}, {"directoryIndex": 5, "id": "ALL_BUILD::@92995a2f85961e8f5b16", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-8982b17ad107170d8e83.json", "name": "ALL_BUILD", "projectIndex": 5}, {"directoryIndex": 71, "id": "ALL_BUILD::@51e97efefc2313866ad5", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-54994d3f173f21751830.json", "name": "ALL_BUILD", "projectIndex": 71}, {"directoryIndex": 28, "id": "ALL_BUILD::@1df6ab8cb0b8ddb593f8", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-c504476d8ceceb45d7c2.json", "name": "ALL_BUILD", "projectIndex": 28}, {"directoryIndex": 6, "id": "ALL_BUILD::@e9ece92fe2bc47be420b", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-c6d18cd0ab434516660b.json", "name": "ALL_BUILD", "projectIndex": 6}, {"directoryIndex": 72, "id": "ALL_BUILD::@9d9cd8ab08d8520a4ac3", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-3d4cfdff622930c380ca.json", "name": "ALL_BUILD", "projectIndex": 72}, {"directoryIndex": 73, "id": "ALL_BUILD::@116eb0f160f4d76de168", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-16899497fe6bfb496bf5.json", "name": "ALL_BUILD", "projectIndex": 73}, {"directoryIndex": 7, "id": "ALL_BUILD::@03c7a47b8090dea9b455", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-ffa581a59595a1d89428.json", "name": "ALL_BUILD", "projectIndex": 7}, {"directoryIndex": 74, "id": "ALL_BUILD::@d7390e83b7e4f79f633d", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-f7c053f7188b257ad6ab.json", "name": "ALL_BUILD", "projectIndex": 74}, {"directoryIndex": 38, "id": "ALL_BUILD::@7e2e321726c5f3e3edcb", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-c6bf30b5692cd0ba3e2e.json", "name": "ALL_BUILD", "projectIndex": 38}, {"directoryIndex": 8, "id": "ALL_BUILD::@3f043c5f38f013ef2115", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-a38675298dfaed7097d8.json", "name": "ALL_BUILD", "projectIndex": 8}, {"directoryIndex": 75, "id": "ALL_BUILD::@ae279e4383f26a866133", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-0eb479fa5fcd1bd5f128.json", "name": "ALL_BUILD", "projectIndex": 75}, {"directoryIndex": 29, "id": "ALL_BUILD::@75eb8879fc099b4640aa", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-999f14f3c47b505fa427.json", "name": "ALL_BUILD", "projectIndex": 29}, {"directoryIndex": 9, "id": "ALL_BUILD::@6b827d246feac3c35b9a", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-bf24b70e9943d8b96cbc.json", "name": "ALL_BUILD", "projectIndex": 9}, {"directoryIndex": 69, "id": "ALL_BUILD::@bfaa0a8775de30d870f0", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-9918016ebbb419e28d18.json", "name": "ALL_BUILD", "projectIndex": 69}, {"directoryIndex": 49, "id": "ALL_BUILD::@03373f949cbd329c961c", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-b9a988aed78fcad4eb7e.json", "name": "ALL_BUILD", "projectIndex": 49}, {"directoryIndex": 10, "id": "ALL_BUILD::@6ccf8425ca6a81980105", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-dad371b5f71f8a659af3.json", "name": "ALL_BUILD", "projectIndex": 10}, {"directoryIndex": 76, "id": "ALL_BUILD::@64c63141ea1fe7a116f6", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-1048f699b992ebfb0588.json", "name": "ALL_BUILD", "projectIndex": 76}, {"directoryIndex": 36, "id": "ALL_BUILD::@08c5adc7ee1d91091a97", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-ece8b323ed57b6f1fbe7.json", "name": "ALL_BUILD", "projectIndex": 36}, {"directoryIndex": 11, "id": "ALL_BUILD::@e0567cd60ef58755dd5b", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-7e71292cd8fb1d3f305b.json", "name": "ALL_BUILD", "projectIndex": 11}, {"directoryIndex": 77, "id": "ALL_BUILD::@f8ebbc87f7fac77328c8", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-2307048a7f0d82e4133d.json", "name": "ALL_BUILD", "projectIndex": 77}, {"directoryIndex": 30, "id": "ALL_BUILD::@4e1303897d180b86ab2f", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-5b129cd61e1e6e577f56.json", "name": "ALL_BUILD", "projectIndex": 30}, {"directoryIndex": 12, "id": "ALL_BUILD::@96a57770f6c6f4e493b3", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-6e1bc5ae653b03810b1e.json", "name": "ALL_BUILD", "projectIndex": 12}, {"directoryIndex": 78, "id": "ALL_BUILD::@f4fb3041b29f01391299", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-1f9beecd76424e38b6ac.json", "name": "ALL_BUILD", "projectIndex": 78}, {"directoryIndex": 48, "id": "ALL_BUILD::@a384ba46c8f7385844c3", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-585b87981061d80d6b32.json", "name": "ALL_BUILD", "projectIndex": 48}, {"directoryIndex": 13, "id": "ALL_BUILD::@6eec4415674bd20e6491", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-67d66f0bb28969695805.json", "name": "ALL_BUILD", "projectIndex": 13}, {"directoryIndex": 79, "id": "ALL_BUILD::@efca4bcc8ad294d52f3d", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-427747eec773300cb006.json", "name": "ALL_BUILD", "projectIndex": 79}, {"directoryIndex": 42, "id": "ALL_BUILD::@b13ebbd4a3aafa6a0363", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-b28ba7495172b5cff460.json", "name": "ALL_BUILD", "projectIndex": 42}, {"directoryIndex": 14, "id": "ALL_BUILD::@1bc5057501657c23b12e", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-44929f2f6ed2fa190302.json", "name": "ALL_BUILD", "projectIndex": 14}, {"directoryIndex": 64, "id": "ALL_BUILD::@14914dfd89874674d41d", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-901e1781755324a5fec6.json", "name": "ALL_BUILD", "projectIndex": 64}, {"directoryIndex": 31, "id": "ALL_BUILD::@30345e39cecb9bcc06b0", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-a2f4c2c3699b6d4e8f02.json", "name": "ALL_BUILD", "projectIndex": 31}, {"directoryIndex": 15, "id": "ALL_BUILD::@7c9daef8275400bf8ba5", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-8eb8385fb4710473c8b0.json", "name": "ALL_BUILD", "projectIndex": 15}, {"directoryIndex": 63, "id": "ALL_BUILD::@e58766abf91db77f862b", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-c49bfb773b8d9ae8a563.json", "name": "ALL_BUILD", "projectIndex": 63}, {"directoryIndex": 47, "id": "ALL_BUILD::@76c3a22b9f657d2ec026", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-73870bfe79e2590bf24b.json", "name": "ALL_BUILD", "projectIndex": 47}, {"directoryIndex": 16, "id": "ALL_BUILD::@8670365571700e12b583", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-f7583928953388d9577a.json", "name": "ALL_BUILD", "projectIndex": 16}, {"directoryIndex": 62, "id": "ALL_BUILD::@7bf30a519259482def19", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-7285147ac61a7f6b2475.json", "name": "ALL_BUILD", "projectIndex": 62}, {"directoryIndex": 40, "id": "ALL_BUILD::@121a4898e406881ffb23", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-9c608b9e07c5eb8aa8d3.json", "name": "ALL_BUILD", "projectIndex": 40}, {"directoryIndex": 17, "id": "ALL_BUILD::@f8e8ceb61b4f8c6d7034", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-242568ff82df40a13a70.json", "name": "ALL_BUILD", "projectIndex": 17}, {"directoryIndex": 61, "id": "ALL_BUILD::@a2142d788288f069154a", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-765fa3e850ca53bc5583.json", "name": "ALL_BUILD", "projectIndex": 61}, {"directoryIndex": 32, "id": "ALL_BUILD::@5cdc2d2ab21b3b9e72cd", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-f679fc8971fbb8230b83.json", "name": "ALL_BUILD", "projectIndex": 32}, {"directoryIndex": 18, "id": "ALL_BUILD::@0a3c2e809899f2f13f5a", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-0d2ad87deda7b2a9aa06.json", "name": "ALL_BUILD", "projectIndex": 18}, {"directoryIndex": 60, "id": "ALL_BUILD::@39b1645ff4c023a4e445", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-084b090b7dc16868a8bc.json", "name": "ALL_BUILD", "projectIndex": 60}, {"directoryIndex": 46, "id": "ALL_BUILD::@65e3165a8812532710ef", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-26611f28379a7309f899.json", "name": "ALL_BUILD", "projectIndex": 46}, {"directoryIndex": 19, "id": "ALL_BUILD::@933176848578d8c440c9", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-c48b3bb23f187ae9ce55.json", "name": "ALL_BUILD", "projectIndex": 19}, {"directoryIndex": 59, "id": "ALL_BUILD::@a99f8207d5ede56c5cae", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-9c0ab327cff663eb592d.json", "name": "ALL_BUILD", "projectIndex": 59}, {"directoryIndex": 37, "id": "ALL_BUILD::@889b7d31514c76e85624", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-46891ee6e417731d3bb2.json", "name": "ALL_BUILD", "projectIndex": 37}, {"directoryIndex": 20, "id": "ALL_BUILD::@80cf03468317b5d7fe2b", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-9a67b5f1091c8b72c93d.json", "name": "ALL_BUILD", "projectIndex": 20}, {"directoryIndex": 58, "id": "ALL_BUILD::@bc252bb14595a0f09d26", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-0d333457689a8657f8bb.json", "name": "ALL_BUILD", "projectIndex": 58}, {"directoryIndex": 33, "id": "ALL_BUILD::@ccffbf515659b480dabe", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-d029dae30f7abd04202a.json", "name": "ALL_BUILD", "projectIndex": 33}, {"directoryIndex": 21, "id": "ALL_BUILD::@9c3ce50b53cc3ff6eff5", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-2bdca0b9ff24f6069f6f.json", "name": "ALL_BUILD", "projectIndex": 21}, {"directoryIndex": 57, "id": "ALL_BUILD::@e336ced093e233e6d829", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-2490e630ef488d559bda.json", "name": "ALL_BUILD", "projectIndex": 57}, {"directoryIndex": 45, "id": "ALL_BUILD::@bb78083dcad0a236858d", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-a4715af1c837cd3cfb57.json", "name": "ALL_BUILD", "projectIndex": 45}, {"directoryIndex": 22, "id": "ALL_BUILD::@59f7d9ae5c13d347c5f4", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-55c4b70ff406511ddf93.json", "name": "ALL_BUILD", "projectIndex": 22}, {"directoryIndex": 56, "id": "ALL_BUILD::@e75f830eb736a5dca1ce", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-d9762c58eea40fa5b0b3.json", "name": "ALL_BUILD", "projectIndex": 56}, {"directoryIndex": 41, "id": "ALL_BUILD::@1dc2f4735d896dd76909", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-929fe4cbdbeb7ddf242d.json", "name": "ALL_BUILD", "projectIndex": 41}, {"directoryIndex": 23, "id": "ALL_BUILD::@d1520424919af3a40272", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-6f3f47d4c763e4b65f51.json", "name": "ALL_BUILD", "projectIndex": 23}, {"directoryIndex": 55, "id": "ALL_BUILD::@fd5e493b37d2bab880d9", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-603c76eaf26e2c4621cd.json", "name": "ALL_BUILD", "projectIndex": 55}, {"directoryIndex": 34, "id": "ALL_BUILD::@d803dad5c2b28052d845", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-3f8d66d1990c24f51ece.json", "name": "ALL_BUILD", "projectIndex": 34}, {"directoryIndex": 24, "id": "ALL_BUILD::@cf8a855e37e415d7ca08", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-4072b617fa6d0b547136.json", "name": "ALL_BUILD", "projectIndex": 24}, {"directoryIndex": 54, "id": "ALL_BUILD::@80c0713ae5ba495463b6", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-f6c6c0ef380fb6385d06.json", "name": "ALL_BUILD", "projectIndex": 54}, {"directoryIndex": 44, "id": "ALL_BUILD::@7a0ade4671e16056f257", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-446feef9c15e67387422.json", "name": "ALL_BUILD", "projectIndex": 44}, {"directoryIndex": 25, "id": "ALL_BUILD::@bca145e2342aef659032", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-eb32980119712b6b14dc.json", "name": "ALL_BUILD", "projectIndex": 25}, {"directoryIndex": 53, "id": "ALL_BUILD::@7d9d822efa235ac321e6", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-382e2efa87d6a17ef3e9.json", "name": "ALL_BUILD", "projectIndex": 53}, {"directoryIndex": 39, "id": "ALL_BUILD::@e99d12ef8be33386882a", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-d89f761cec3c2e760907.json", "name": "ALL_BUILD", "projectIndex": 39}, {"directoryIndex": 10, "id": "Analysis_RobotApp::@6ccf8425ca6a81980105", "jsonFile": "target-Analysis_RobotApp-RelWithDebInfo-5e6cd0edda024841e4b2.json", "name": "Analysis_RobotApp", "projectIndex": 10}, {"directoryIndex": 11, "id": "Analysis_RobotalgorithmscoordinateTransform::@e0567cd60ef58755dd5b", "jsonFile": "target-Analysis_RobotalgorithmscoordinateTransform-RelWithDebInfo-e8eb8706991c6f409f57.json", "name": "Analysis_RobotalgorithmscoordinateTransform", "projectIndex": 11}, {"directoryIndex": 2, "id": "Analysis_RobotalgorithmspouringControl::@07001b74ee4af3db8a6e", "jsonFile": "target-Analysis_RobotalgorithmspouringControl-RelWithDebInfo-a07672b02af026eec849.json", "name": "Analysis_RobotalgorithmspouringControl", "projectIndex": 2}, {"directoryIndex": 12, "id": "Analysis_RobotalgorithmstcpPositionMaintain::@96a57770f6c6f4e493b3", "jsonFile": "target-Analysis_RobotalgorithmstcpPositionMaintain-RelWithDebInfo-6f86323ad484fa286097.json", "name": "Analysis_RobotalgorithmstcpPositionMaintain", "projectIndex": 12}, {"directoryIndex": 3, "id": "Analysis_RobotdriversaixsDriver::@19f706e88e1d43a9565c", "jsonFile": "target-Analysis_RobotdriversaixsDriver-RelWithDebInfo-d0c27194d9a562dc761b.json", "name": "Analysis_RobotdriversaixsDriver", "projectIndex": 3}, {"directoryIndex": 5, "id": "Analysis_RobotdriversbalanceDriver::@92995a2f85961e8f5b16", "jsonFile": "target-Analysis_RobotdriversbalanceDriver-RelWithDebInfo-10ee19543743db4f7a52.json", "name": "Analysis_RobotdriversbalanceDriver", "projectIndex": 5}, {"directoryIndex": 6, "id": "Analysis_RobotdriversheatingMagneticStirrerDriver::@e9ece92fe2bc47be420b", "jsonFile": "target-Analysis_RobotdriversheatingMagneticStirrerDriver-RelWithDebInfo-f699dd22e5886ae6d3d8.json", "name": "Analysis_RobotdriversheatingMagneticStirrerDriver", "projectIndex": 6}, {"directoryIndex": 7, "id": "Analysis_RobotdriversmoistureAnalyzerDriver::@03c7a47b8090dea9b455", "jsonFile": "target-Analysis_RobotdriversmoistureAnalyzerDriver-RelWithDebInfo-113f5cc658069d2d2710.json", "name": "Analysis_RobotdriversmoistureAnalyzerDriver", "projectIndex": 7}, {"directoryIndex": 4, "id": "Analysis_RobotdriversplcDriver::@97966baa9ab9c14a9bcf", "jsonFile": "target-Analysis_RobotdriversplcDriver-RelWithDebInfo-d8229c198af480a3ac01.json", "name": "Analysis_RobotdriversplcDriver", "projectIndex": 4}, {"directoryIndex": 9, "id": "Analysis_RobotdriversrestInterfaceDriver::@6b827d246feac3c35b9a", "jsonFile": "target-Analysis_RobotdriversrestInterfaceDriver-RelWithDebInfo-c99cbaa64207d1c1ef23.json", "name": "Analysis_RobotdriversrestInterfaceDriver", "projectIndex": 9}, {"directoryIndex": 8, "id": "Analysis_RobotdriversrobotDriver::@3f043c5f38f013ef2115", "jsonFile": "target-Analysis_RobotdriversrobotDriver-RelWithDebInfo-8619eef105d0201adbf8.json", "name": "Analysis_RobotdriversrobotDriver", "projectIndex": 8}, {"directoryIndex": 13, "id": "Analysis_RobottestheaterApiTest::@6eec4415674bd20e6491", "jsonFile": "target-Analysis_RobottestheaterApiTest-RelWithDebInfo-3f197badad09e7b7cf8f.json", "name": "Analysis_RobottestheaterApiTest", "projectIndex": 13}, {"directoryIndex": 14, "id": "Analysis_RobottestheatingMagneticStirrerDriver::@1bc5057501657c23b12e", "jsonFile": "target-Analysis_RobottestheatingMagneticStirrerDriver-RelWithDebInfo-ba9358b584cf28495bf8.json", "name": "Analysis_RobottestheatingMagneticStirrerDriver", "projectIndex": 14}, {"directoryIndex": 15, "id": "MJServerAPP::@7c9daef8275400bf8ba5", "jsonFile": "target-MJServerAPP-RelWithDebInfo-12af2fe83c9696a969fe.json", "name": "MJServerAPP", "projectIndex": 15}, {"directoryIndex": 17, "id": "MJServer_RefactorApp::@f8e8ceb61b4f8c6d7034", "jsonFile": "target-MJServer_RefactorApp-RelWithDebInfo-e8a59cf5605e129711f0.json", "name": "MJServer_RefactorApp", "projectIndex": 17}, {"directoryIndex": 16, "id": "MJServer_RefactorLibrary::@8670365571700e12b583", "jsonFile": "target-MJServer_RefactorLibrary-RelWithDebInfo-562811558673e4e37628.json", "name": "MJServer_RefactorLibrary", "projectIndex": 16}, {"directoryIndex": 18, "id": "MJServer_RefactorTestphase1_test::@0a3c2e809899f2f13f5a", "jsonFile": "target-MJServer_RefactorTestphase1_test-RelWithDebInfo-1b5198b7397814d14068.json", "name": "MJServer_RefactorTestphase1_test", "projectIndex": 18}, {"directoryIndex": 19, "id": "MJServer_RefactorTestsimple_abb_client::@933176848578d8c440c9", "jsonFile": "target-MJServer_RefactorTestsimple_abb_client-RelWithDebInfo-e30496e76a31b80e8122.json", "name": "MJServer_RefactorTestsimple_abb_client", "projectIndex": 19}, {"directoryIndex": 20, "id": "MJServer_RefactorTestsimple_feeder_client::@80cf03468317b5d7fe2b", "jsonFile": "target-MJServer_RefactorTestsimple_feeder_client-RelWithDebInfo-95e5385e48d038bc787e.json", "name": "MJServer_RefactorTestsimple_feeder_client", "projectIndex": 20}, {"directoryIndex": 21, "id": "RoboticLaserMarkingAbbDriver::@9c3ce50b53cc3ff6eff5", "jsonFile": "target-RoboticLaserMarkingAbbDriver-RelWithDebInfo-277a0014ebb3f6cadb6b.json", "name": "RoboticLaserMarkingAbbDriver", "projectIndex": 21}, {"directoryIndex": 22, "id": "RoboticLaserMarkingLicenseGenerator::@59f7d9ae5c13d347c5f4", "jsonFile": "target-RoboticLaserMarkingLicenseGenerator-RelWithDebInfo-f4e45c08f3a11c0efbdc.json", "name": "RoboticLaserMarkingLicenseGenerator", "projectIndex": 22}, {"directoryIndex": 23, "id": "RoboticLaserMarkingRFIDDriver::@d1520424919af3a40272", "jsonFile": "target-RoboticLaserMarkingRFIDDriver-RelWithDebInfo-a6e61024ac60a45d5f3e.json", "name": "RoboticLaserMarkingRFIDDriver", "projectIndex": 23}, {"directoryIndex": 25, "id": "RoboticLaserMarkingTestabbsocket::@bca145e2342aef659032", "jsonFile": "target-RoboticLaserMarkingTestabbsocket-RelWithDebInfo-1b5efcd1cb5b2586b4a0.json", "name": "RoboticLaserMarkingTestabbsocket", "projectIndex": 25}, {"directoryIndex": 26, "id": "RoboticLaserMarkingTestlaser::@fef5c779e5dd4e5a5c7c", "jsonFile": "target-RoboticLaserMarkingTestlaser-RelWithDebInfo-8ac105a29fd930f79731.json", "name": "RoboticLaserMarkingTestlaser", "projectIndex": 26}, {"directoryIndex": 27, "id": "RoboticLaserMarkingTestlaserUI::@7e6cec28b989a66fe139", "jsonFile": "target-RoboticLaserMarkingTestlaserUI-RelWithDebInfo-6343eab6df50cedb05e1.json", "name": "RoboticLaserMarkingTestlaserUI", "projectIndex": 27}, {"directoryIndex": 28, "id": "RoboticLaserMarkingTestrfiddriver::@1df6ab8cb0b8ddb593f8", "jsonFile": "target-RoboticLaserMarkingTestrfiddriver-RelWithDebInfo-af65719202c1ad1338a6.json", "name": "RoboticLaserMarkingTestrfiddriver", "projectIndex": 28}, {"directoryIndex": 29, "id": "RoboticLaserMarkingTestrfidserver::@75eb8879fc099b4640aa", "jsonFile": "target-RoboticLaserMarkingTestrfidserver-RelWithDebInfo-d1cbc1d347badfb26c07.json", "name": "RoboticLaserMarkingTestrfidserver", "projectIndex": 29}, {"directoryIndex": 30, "id": "RoboticLaserMarkingUI::@4e1303897d180b86ab2f", "jsonFile": "target-RoboticLaserMarkingUI-RelWithDebInfo-f9f6087d0f3ce420700d.json", "name": "RoboticLaserMarkingUI", "projectIndex": 30}, {"directoryIndex": 24, "id": "RoboticLaserMarkinglaserDriver::@cf8a855e37e415d7ca08", "jsonFile": "target-RoboticLaserMarkinglaserDriver-RelWithDebInfo-feb48440dd467304bee3.json", "name": "RoboticLaserMarkinglaserDriver", "projectIndex": 24}, {"directoryIndex": 31, "id": "RoboticLaserMarkinglaserDriverSim::@30345e39cecb9bcc06b0", "jsonFile": "target-RoboticLaserMarkinglaserDriverSim-RelWithDebInfo-83b81d3f2378cc832082.json", "name": "RoboticLaserMarkinglaserDriverSim", "projectIndex": 31}, {"directoryIndex": 34, "id": "Testtest_abb_socket::@d803dad5c2b28052d845", "jsonFile": "target-Testtest_abb_socket-RelWithDebInfo-8e1bda6be8dbc860a036.json", "name": "Testtest_abb_socket", "projectIndex": 34}, {"directoryIndex": 35, "id": "Testtest_config_manager::@d885fae1c443095a1db7", "jsonFile": "target-Testtest_config_manager-RelWithDebInfo-8948b5dbb979f5a057f4.json", "name": "Testtest_config_manager", "projectIndex": 35}, {"directoryIndex": 36, "id": "Testtest_csv::@08c5adc7ee1d91091a97", "jsonFile": "target-Testtest_csv-RelWithDebInfo-6581c7350f68581dab78.json", "name": "Testtest_csv", "projectIndex": 36}, {"directoryIndex": 37, "id": "Testtest_event_listener::@889b7d31514c76e85624", "jsonFile": "target-Testtest_event_listener-RelWithDebInfo-37b120e45b51db9ba3f5.json", "name": "Testtest_event_listener", "projectIndex": 37}, {"directoryIndex": 38, "id": "Testtest_executor::@7e2e321726c5f3e3edcb", "jsonFile": "target-Testtest_executor-RelWithDebInfo-c6c47e13db8bd61b8489.json", "name": "Testtest_executor", "projectIndex": 38}, {"directoryIndex": 39, "id": "Testtest_executor_context::@e99d12ef8be33386882a", "jsonFile": "target-Testtest_executor_context-RelWithDebInfo-f215679d206dbe563bbf.json", "name": "Testtest_executor_context", "projectIndex": 39}, {"directoryIndex": 40, "id": "Testtest_fa2204n_balance::@121a4898e406881ffb23", "jsonFile": "target-Testtest_fa2204n_balance-RelWithDebInfo-da73e70d50bbb74b506e.json", "name": "Testtest_fa2204n_balance", "projectIndex": 40}, {"directoryIndex": 41, "id": "Testtest_fa2204n_balance_basic::@1dc2f4735d896dd76909", "jsonFile": "target-Testtest_fa2204n_balance_basic-RelWithDebInfo-a5d6553aea4d0b8991e5.json", "name": "Testtest_fa2204n_balance_basic", "projectIndex": 41}, {"directoryIndex": 42, "id": "Testtest_fileutil::@b13ebbd4a3aafa6a0363", "jsonFile": "target-Testtest_fileutil-RelWithDebInfo-6e0f3b8cf2a106e92d3d.json", "name": "Testtest_fileutil", "projectIndex": 42}, {"directoryIndex": 43, "id": "Testtest_json::@46d5ce0aeb8e42b7284d", "jsonFile": "target-Testtest_json-RelWithDebInfo-b3caf488268c430add96.json", "name": "Testtest_json", "projectIndex": 43}, {"directoryIndex": 44, "id": "Testtest_license_manager::@7a0ade4671e16056f257", "jsonFile": "target-Testtest_license_manager-RelWithDebInfo-fc5b28ea04e0f295a92f.json", "name": "Testtest_license_manager", "projectIndex": 44}, {"directoryIndex": 45, "id": "Testtest_license_ui::@bb78083dcad0a236858d", "jsonFile": "target-Testtest_license_ui-RelWithDebInfo-18df46888b096b359e90.json", "name": "Testtest_license_ui", "projectIndex": 45}, {"directoryIndex": 47, "id": "Testtest_network::@76c3a22b9f657d2ec026", "jsonFile": "target-Testtest_network-RelWithDebInfo-02cee6f04e3dbe77b3ec.json", "name": "Testtest_network", "projectIndex": 47}, {"directoryIndex": 48, "id": "Testtest_serial::@a384ba46c8f7385844c3", "jsonFile": "target-Testtest_serial-RelWithDebInfo-7121e7e68e23955eb16f.json", "name": "Testtest_serial", "projectIndex": 48}, {"directoryIndex": 49, "id": "Testtest_service_container::@03373f949cbd329c961c", "jsonFile": "target-Testtest_service_container-RelWithDebInfo-eaa08547561b03d8f2f3.json", "name": "Testtest_service_container", "projectIndex": 49}, {"directoryIndex": 50, "id": "Testtest_socket::@6ff24ad6fb1c26ae0a57", "jsonFile": "target-Testtest_socket-RelWithDebInfo-4d0e38de04665c93a030.json", "name": "Testtest_socket", "projectIndex": 50}, {"directoryIndex": 51, "id": "Testtest_sqlite::@e4ccddf7a4c29adf5bd6", "jsonFile": "target-Testtest_sqlite-RelWithDebInfo-895ace6b9b55813a6ea0.json", "name": "Testtest_sqlite", "projectIndex": 51}, {"directoryIndex": 52, "id": "Testtest_taskflow::@39116d767a15e3a891df", "jsonFile": "target-Testtest_taskflow-RelWithDebInfo-b324ceb6137cd54d322f.json", "name": "Testtest_taskflow", "projectIndex": 52}, {"directoryIndex": 53, "id": "Testtest_twoaixsrobot::@7d9d822efa235ac321e6", "jsonFile": "target-Testtest_twoaixsrobot-RelWithDebInfo-4d433072276782e9091f.json", "name": "Testtest_twoaixsrobot", "projectIndex": 53}, {"directoryIndex": 54, "id": "Testtest_xml::@80c0713ae5ba495463b6", "jsonFile": "target-Testtest_xml-RelWithDebInfo-04e2dd352bec7cd421e7.json", "name": "Testtest_xml", "projectIndex": 54}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-RelWithDebInfo-1ca4f1b2e21860d5885e.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 1, "id": "fuxicommon::@58335e9a86196d0a97e7", "jsonFile": "target-fuxicommon-RelWithDebInfo-cc9537d58aae9a92a63e.json", "name": "fuxicommon", "projectIndex": 1}, {"directoryIndex": 32, "id": "fuxicore::@5cdc2d2ab21b3b9e72cd", "jsonFile": "target-fuxicore-RelWithDebInfo-a67b33d22722e5f9440a.json", "name": "fuxicore", "projectIndex": 32}, {"directoryIndex": 55, "id": "hardwaredriverAuboArcsDriver::@fd5e493b37d2bab880d9", "jsonFile": "target-hardwaredriverAuboArcsDriver-RelWithDebInfo-023c6aeec758497a82e3.json", "name": "hardwaredriverAuboArcsDriver", "projectIndex": 55}, {"directoryIndex": 56, "id": "hardwaredriverAuboDriver::@e75f830eb736a5dca1ce", "jsonFile": "target-hardwaredriverAuboDriver-RelWithDebInfo-da3af842a49734f06fcc.json", "name": "hardwaredriverAuboDriver", "projectIndex": 56}, {"directoryIndex": 57, "id": "hardwaredriverElectricGripperDriver::@e336ced093e233e6d829", "jsonFile": "target-hardwaredriverElectricGripperDriver-RelWithDebInfo-eab92edc41032b07f775.json", "name": "hardwaredriverElectricGripperDriver", "projectIndex": 57}, {"directoryIndex": 58, "id": "hardwaredriverHikVisionCamera::@bc252bb14595a0f09d26", "jsonFile": "target-hardwaredriverHikVisionCamera-RelWithDebInfo-17471df87c3f1b08482e.json", "name": "hardwaredriverHikVisionCamera", "projectIndex": 58}, {"directoryIndex": 59, "id": "hardwaredriverLabelPrinter::@a99f8207d5ede56c5cae", "jsonFile": "target-hardwaredriverLabelPrinter-RelWithDebInfo-28d4c7a23a115be6484f.json", "name": "hardwaredriverLabelPrinter", "projectIndex": 59}, {"directoryIndex": 60, "id": "hardwaredriverMettlerBalance::@39b1645ff4c023a4e445", "jsonFile": "target-hardwaredriverMettlerBalance-RelWithDebInfo-33016b51de8d817a3c82.json", "name": "hardwaredriverMettlerBalance", "projectIndex": 60}, {"directoryIndex": 61, "id": "hardwaredriverOpcDa::@a2142d788288f069154a", "jsonFile": "target-hardwaredriverOpcDa-RelWithDebInfo-186d361e4907d97cf63d.json", "name": "hardwaredriverOpcDa", "projectIndex": 61}, {"directoryIndex": 62, "id": "hardwaredriverOpcUa::@7bf30a519259482def19", "jsonFile": "target-hardwaredriverOpcUa-RelWithDebInfo-d00e1f1ac88783677c4c.json", "name": "hardwaredriverOpcUa", "projectIndex": 62}, {"directoryIndex": 33, "id": "hardwaredriverabbRobotDriver::@ccffbf515659b480dabe", "jsonFile": "target-hardwaredriverabbRobotDriver-RelWithDebInfo-a5edcbdf5b428b82a9ae.json", "name": "hardwaredriverabbRobotDriver", "projectIndex": 33}, {"directoryIndex": 64, "id": "hardwaredriveragilerobotDriver::@14914dfd89874674d41d", "jsonFile": "target-hardwaredriveragilerobotDriver-RelWithDebInfo-a83df90d1ab859e0675c.json", "name": "hardwaredriveragilerobotDriver", "projectIndex": 64}, {"directoryIndex": 65, "id": "hardwaredriverfairinoDriver::@8a675cd9715b77cebac5", "jsonFile": "target-hardwaredriverfairinoDriver-RelWithDebInfo-a33a69d6a1b2d9a920ee.json", "name": "hardwaredriverfairinoDriver", "projectIndex": 65}, {"directoryIndex": 66, "id": "hardwaredriverjunduoHandDriver::@a89b79f2a82dbe076976", "jsonFile": "target-hardwaredriverjunduoHandDriver-RelWithDebInfo-4b8b7f0d185c3916f005.json", "name": "hardwaredriverjunduoHandDriver", "projectIndex": 66}, {"directoryIndex": 67, "id": "hardwaredrivermodbus::@2ef1e00ba9bcf504e7b3", "jsonFile": "target-hardwaredrivermodbus-RelWithDebInfo-8048763c7b36c4fb0f52.json", "name": "hardwaredrivermodbus", "projectIndex": 67}, {"directoryIndex": 68, "id": "hardwaredriverserial::@e813d8aa5825a18a8390", "jsonFile": "target-hardwaredriverserial-RelWithDebInfo-b44fac2d940a29a2bcb0.json", "name": "hardwaredriverserial", "projectIndex": 68}, {"directoryIndex": 63, "id": "hardwaredriversocket::@e58766abf91db77f862b", "jsonFile": "target-hardwaredriversocket-RelWithDebInfo-240c979dd24f32960497.json", "name": "hardwaredriversocket", "projectIndex": 63}, {"directoryIndex": 69, "id": "hardwaredriverusbcamera::@bfaa0a8775de30d870f0", "jsonFile": "target-hardwaredriverusbcamera-RelWithDebInfo-1ab4ec8b9de95bfdce9f.json", "name": "hardwaredriverusbcamera", "projectIndex": 69}, {"directoryIndex": 46, "id": "test_micro_dosing::@65e3165a8812532710ef", "jsonFile": "target-test_micro_dosing-RelWithDebInfo-0b100d0ce2214ab773e7.json", "name": "test_micro_dosing", "projectIndex": 46}, {"directoryIndex": 70, "id": "toolcalbuild::@a167bea24520843f7e43", "jsonFile": "target-toolcalbuild-RelWithDebInfo-ac7cb69e93fee593f4f4.json", "name": "toolcalbuild", "projectIndex": 70}, {"directoryIndex": 72, "id": "toolcaltest::@9d9cd8ab08d8520a4ac3", "jsonFile": "target-toolcaltest-RelWithDebInfo-432165b4220a1866aff6.json", "name": "toolcaltest", "projectIndex": 72}, {"directoryIndex": 71, "id": "toolcameraCalibrator::@51e97efefc2313866ad5", "jsonFile": "target-toolcameraCalibrator-RelWithDebInfo-10aee4e4a4b2e11db570.json", "name": "toolcameraCalibrator", "projectIndex": 71}, {"directoryIndex": 73, "id": "toolcommunication::@116eb0f160f4d76de168", "jsonFile": "target-toolcommunication-RelWithDebInfo-84239e414ed4c46e080c.json", "name": "toolcommunication", "projectIndex": 73}, {"directoryIndex": 74, "id": "toolhandeyecal::@d7390e83b7e4f79f633d", "jsonFile": "target-toolhandeyecal-RelWithDebInfo-92846e827944d634904c.json", "name": "toolhandeyecal", "projectIndex": 74}, {"directoryIndex": 75, "id": "toolhandeyecaltest::@ae279e4383f26a866133", "jsonFile": "target-toolhandeyecaltest-RelWithDebInfo-6775712e2185519e4145.json", "name": "toolhandeyecaltest", "projectIndex": 75}, {"directoryIndex": 76, "id": "toolhandeyecaluihandeyecalui::@64c63141ea1fe7a116f6", "jsonFile": "target-toolhandeyecaluihandeyecalui-RelWithDebInfo-aa2a09e5c996c63c251f.json", "name": "toolhandeyecaluihandeyecalui", "projectIndex": 76}, {"directoryIndex": 77, "id": "toolhandeyecaluipath::@f8ebbc87f7fac77328c8", "jsonFile": "target-toolhandeyecaluipath-RelWithDebInfo-a97dbfea3225ba6247d7.json", "name": "toolhandeyecaluipath", "projectIndex": 77}, {"directoryIndex": 78, "id": "toolhandeyecaluipathAuto::@f4fb3041b29f01391299", "jsonFile": "target-toolhandeyecaluipathAuto-RelWithDebInfo-df681abd07a1e334698a.json", "name": "toolhandeyecaluipathAuto", "projectIndex": 78}, {"directoryIndex": 79, "id": "toolverify_calibration::@efca4bcc8ad294d52f3d", "jsonFile": "target-toolverify_calibration-RelWithDebInfo-a45fc05e1151d60b7a21.json", "name": "toolverify_calibration", "projectIndex": 79}]}], "kind": "codemodel", "paths": {"build": "D:/newfuxios/build", "source": "D:/newfuxios"}, "version": {"major": 2, "minor": 7}}