{"cmake": {"generator": {"multiConfig": true, "name": "Visual Studio 16 2019", "platform": "x64"}, "paths": {"cmake": "D:/opt/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/bin/cmake.exe", "cpack": "D:/opt/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/bin/cpack.exe", "ctest": "D:/opt/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/bin/ctest.exe", "root": "D:/opt/vcpkg/downloads/tools/cmake-3.30.1-windows/cmake-3.30.1-windows-i386/share/cmake-3.30"}, "version": {"isDirty": false, "major": 3, "minor": 30, "patch": 1, "string": "3.30.1", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-e8e5570f6cbe1dd0ff03.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, {"jsonFile": "cache-v2-b6d964d5699363fd04cb.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-68c0e6d7c20ada26c14e.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, {"jsonFile": "toolchains-v1-cb838f5e21b267f50e76.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"client-vscode": {"query.json": {"requests": [{"kind": "cache", "version": 2}, {"kind": "codemodel", "version": 2}, {"kind": "toolchains", "version": 1}, {"kind": "cmakeFiles", "version": 1}], "responses": [{"jsonFile": "cache-v2-b6d964d5699363fd04cb.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "codemodel-v2-e8e5570f6cbe1dd0ff03.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, {"jsonFile": "toolchains-v1-cb838f5e21b267f50e76.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-68c0e6d7c20ada26c14e.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}]}}}}