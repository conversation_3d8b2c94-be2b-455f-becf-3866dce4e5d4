#ifndef BALANCE_DRIVER_H
#define BALANCE_DRIVER_H

#include <string>
#include <memory>
#include <functional>
#include <vector>
#include <cstdint>

#ifdef _WIN32
#include <windows.h>
#else
#include <termios.h>
#include <unistd.h>
#include <fcntl.h>
#endif

namespace AnalysisRobot {
namespace Balance {

/**
 * @brief 天平驱动状态枚举
 */
enum class BalanceStatus {
    DISCONNECTED = 0,   // 未连接
    CONNECTED = 1,      // 已连接
    READING = 2,        // 读取中
    FAULT = 3           // 错误状态
};

/**
 * @brief 天平读数结果
 */
struct WeightReading {
    bool success;           // 读取是否成功
    double weight;          // 重量值（克）
    uint32_t rawValue;      // 原始内码值
    std::string errorMsg;   // 错误信息
    
    WeightReading() : success(false), weight(0.0), rawValue(0) {}
};

/**
 * @brief 天平驱动配置
 */
struct BalanceConfig {
    std::string serialPort;     // 串口号，如"COM1"
    int baudRate;               // 波特率，默认9600
    char parity;                // 校验位，默认'N'
    int dataBits;               // 数据位，默认8
    int stopBits;               // 停止位，默认1
    int slaveId;                // 从机地址，默认1
    int responseTimeout;        // 响应超时时间（毫秒），默认1000
    
    BalanceConfig() 
        : serialPort("COM6")
        , baudRate(9600)
        , parity('N')
        , dataBits(8)
        , stopBits(1)
        , slaveId(1)
        , responseTimeout(1000) {}
};

/**
 * @brief 状态回调函数类型
 */
using StatusCallback = std::function<void(BalanceStatus status, const std::string& message)>;

/**
 * @brief FA2204N天平驱动类
 * 
 * 支持MODBUS RTU协议的天平设备，主要功能：
 * - 读取重量
 * - 去皮操作
 * - 状态监控
 */
class BalanceDriver {
public:
    /**
     * @brief 构造函数
     */
    BalanceDriver();
    
    /**
     * @brief 析构函数
     */
    ~BalanceDriver();
    
    /**
     * @brief 连接天平
     * @param config 配置参数
     * @return 是否成功
     */
    bool connect(const BalanceConfig& config);
    
    /**
     * @brief 断开连接
     */
    void disconnect();
    
    /**
     * @brief 检查连接状态
     * @return 是否已连接
     */
    bool isConnected() const;
    
    /**
     * @brief 读取重量
     * @return 重量读数结果
     */
    WeightReading readWeight();
    
    /**
     * @brief 去皮操作
     * @return 是否成功
     */
    bool tare();
    
    /**
     * @brief 获取当前状态
     * @return 当前状态
     */
    BalanceStatus getStatus() const;
    
    /**
     * @brief 设置状态回调函数
     * @param callback 回调函数
     */
    void setStatusCallback(StatusCallback callback);
    
    /**
     * @brief 获取最后的错误信息
     * @return 错误信息
     */
    std::string getLastError() const;
    
    /**
     * @brief 获取设备信息
     * @return 设备信息字符串
     */
    std::string getDeviceInfo() const;

private:
#ifdef _WIN32
    HANDLE m_serialHandle;              // Windows串口句柄
#else
    int m_serialFd;                     // Linux串口文件描述符
#endif
    BalanceConfig m_config;             // 配置参数
    BalanceStatus m_status;             // 当前状态
    StatusCallback m_statusCallback;    // 状态回调
    std::string m_lastError;            // 最后错误信息

    /**
     * @brief 设置状态
     * @param status 新状态
     * @param message 状态消息
     */
    void setStatus(BalanceStatus status, const std::string& message = "");

    /**
     * @brief 设置错误信息
     * @param error 错误信息
     */
    void setError(const std::string& error);

    /**
     * @brief 将内码值转换为重量（克）
     * @param rawValue 内码值
     * @return 重量值
     */
    double convertRawToWeight(uint32_t rawValue);

    /**
     * @brief 打开串口
     * @return 是否成功
     */
    bool openSerialPort();

    /**
     * @brief 关闭串口
     */
    void closeSerialPort();

    /**
     * @brief 配置串口参数
     * @return 是否成功
     */
    bool configureSerialPort();

    /**
     * @brief 发送MODBUS请求
     * @param request 请求数据
     * @return 是否成功
     */
    bool sendModbusRequest(const std::vector<uint8_t>& request);

    /**
     * @brief 接收MODBUS响应
     * @param response 响应数据缓冲区
     * @param expectedLength 期望的响应长度
     * @return 实际接收的字节数，-1表示失败
     */
    int receiveModbusResponse(std::vector<uint8_t>& response, int expectedLength);

    /**
     * @brief 计算CRC16校验码
     * @param data 数据
     * @param length 数据长度
     * @return CRC16校验码
     */
    uint16_t calculateCRC16(const uint8_t* data, int length);

    /**
     * @brief 验证CRC16校验码
     * @param data 数据（包含CRC）
     * @param length 数据总长度
     * @return 是否校验通过
     */
    bool verifyCRC16(const uint8_t* data, int length);

    /**
     * @brief 构建读取重量的MODBUS请求
     * @return 请求数据
     */
    std::vector<uint8_t> buildReadWeightRequest();

    /**
     * @brief 构建去皮的MODBUS请求
     * @return 请求数据
     */
    std::vector<uint8_t> buildTareRequest();

    /**
     * @brief 解析重量读取响应
     * @param response 响应数据
     * @param result 解析结果
     * @return 是否成功
     */
    bool parseWeightResponse(const std::vector<uint8_t>& response, WeightReading& result);
};

} // namespace Balance
} // namespace AnalysisRobot

#endif // BALANCE_DRIVER_H
