{"artifacts": [{"path": "D:/newfuxios/install/x64-install/dev/bin/MinSizeRel/RoboticLaserMarkingLicenseGenerator.exe"}, {"path": "D:/newfuxios/install/x64-install/dev/bin/MinSizeRel/RoboticLaserMarkingLicenseGenerator.pdb"}], "backtrace": 3, "backtraceGraph": {"commands": ["add_executable", "include", "target_link_libraries", "add_unique_libraries", "ADD_DEFINITIONS", "include_directories"], "files": ["builder/cmake/executable.cmake", "RoboticLaserMarking/LicenseGenerator/CMakeLists.txt", "builder/cmake/common.cmake", "builder/cmake/add_eigen.cmake", "builder/cmake/add_glog.cmake"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 3, "parent": 0}, {"file": 0, "parent": 1}, {"command": 0, "file": 0, "line": 24, "parent": 2}, {"command": 3, "file": 0, "line": 30, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 4}, {"command": 3, "file": 0, "line": 35, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 6}, {"command": 3, "file": 0, "line": 35, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 8}, {"command": 3, "file": 0, "line": 35, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 10}, {"command": 2, "file": 0, "line": 46, "parent": 2}, {"command": 3, "file": 0, "line": 35, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 13}, {"command": 3, "file": 0, "line": 35, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 15}, {"command": 3, "file": 0, "line": 35, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 17}, {"command": 4, "file": 0, "line": 17, "parent": 2}, {"command": 4, "file": 0, "line": 13, "parent": 2}, {"command": 4, "file": 0, "line": 16, "parent": 2}, {"command": 4, "file": 0, "line": 14, "parent": 2}, {"command": 4, "file": 0, "line": 15, "parent": 2}, {"command": 1, "file": 0, "line": 1, "parent": 2}, {"file": 2, "parent": 24}, {"command": 5, "file": 2, "line": 54, "parent": 25}, {"command": 1, "file": 2, "line": 81, "parent": 25}, {"file": 3, "parent": 27}, {"command": 5, "file": 3, "line": 30, "parent": 28}, {"command": 1, "file": 2, "line": 81, "parent": 25}, {"file": 4, "parent": 30}, {"command": 5, "file": 4, "line": 16, "parent": 31}, {"command": 5, "file": 0, "line": 34, "parent": 2}, {"command": 5, "file": 0, "line": 34, "parent": 2}, {"command": 5, "file": 0, "line": 34, "parent": 2}, {"command": 5, "file": 0, "line": 34, "parent": 2}, {"command": 5, "file": 0, "line": 34, "parent": 2}, {"command": 5, "file": 0, "line": 34, "parent": 2}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc /MD /O1 /Ob1 /DNDEBUG -std:c++20"}], "defines": [{"backtrace": 9, "define": "QT_CHARTS_LIB"}, {"backtrace": 19, "define": "QT_CORE_LIB"}, {"backtrace": 20, "define": "QT_DISABLE_DEPRECATED_BEFORE=0"}, {"backtrace": 21, "define": "QT_GUI_LIB"}, {"backtrace": 11, "define": "QT_NETWORK_LIB"}, {"backtrace": 22, "define": "QT_NO_DEBUG"}, {"backtrace": 7, "define": "QT_SQL_LIB"}, {"backtrace": 23, "define": "QT_WIDGETS_LIB"}], "includes": [{"backtrace": 0, "path": "D:/newfuxios/build/RoboticLaserMarking/LicenseGenerator/RoboticLaserMarkingLicenseGenerator_autogen/include_MinSizeRel"}, {"backtrace": 26, "path": "D:/newfuxios/RoboticLaserMarking/LicenseGenerator/include"}, {"backtrace": 29, "path": "C:/opt/PCL/3rdParty/Eigen/eigen3"}, {"backtrace": 32, "path": "C:/opt/glog/include"}, {"backtrace": 33, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt"}, {"backtrace": 33, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt/QtCore"}, {"backtrace": 33, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/./mkspecs/win32-msvc"}, {"backtrace": 34, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt/QtGui"}, {"backtrace": 34, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt/QtANGLE"}, {"backtrace": 35, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt/QtSql"}, {"backtrace": 36, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt/QtWidgets"}, {"backtrace": 37, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt/QtCharts"}, {"backtrace": 38, "isSystem": true, "path": "C:/ProgramData/anaconda3/Library/include/qt/QtNetwork"}], "language": "CXX", "languageStandard": {"backtraces": [18], "standard": "20"}, "sourceIndexes": [0, 1, 2]}], "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "RoboticLaserMarkingLicenseGenerator::@59f7d9ae5c13d347c5f4", "link": {"commandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc /MD /O1 /Ob1 /DNDEBUG", "role": "flags"}, {"fragment": "/machine:x64                                                                                                                                                 /INCREMENTAL:NO /subsystem:console /machine:x64", "role": "flags"}, {"backtrace": 5, "fragment": "C:\\opt\\glog\\lib\\glogd.lib", "role": "libraries"}, {"backtrace": 7, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5Sql_conda.lib", "role": "libraries"}, {"backtrace": 9, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5Charts_conda.lib", "role": "libraries"}, {"backtrace": 11, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5Network_conda.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "ws2_32.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "kernel32.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "user32.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "gdi32.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "winspool.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "shell32.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "ole32.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "oleaut32.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "uuid.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "comdlg32.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "advapi32.lib", "role": "libraries"}, {"backtrace": 14, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5Widgets_conda.lib", "role": "libraries"}, {"backtrace": 16, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5Gui_conda.lib", "role": "libraries"}, {"backtrace": 18, "fragment": "C:\\ProgramData\\anaconda3\\Library\\lib\\Qt5Core_conda.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "RoboticLaserMarkingLicenseGenerator", "nameOnDisk": "RoboticLaserMarkingLicenseGenerator.exe", "paths": {"build": "RoboticLaserMarking/LicenseGenerator", "source": "RoboticLaserMarking/LicenseGenerator"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [3]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/RoboticLaserMarking/LicenseGenerator/RoboticLaserMarkingLicenseGenerator_autogen/mocs_compilation_MinSizeRel.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "RoboticLaserMarking/LicenseGenerator/src/MainWindow.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "RoboticLaserMarking/LicenseGenerator/src/main.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "RoboticLaserMarking/LicenseGenerator/include/MainWindow.h", "sourceGroupIndex": 1}], "type": "EXECUTABLE"}