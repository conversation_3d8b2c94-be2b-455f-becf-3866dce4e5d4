{"artifacts": [{"path": "D:/newfuxios/install/x64-install/dev/bin/RoboticLaserMarkingUI.exe"}, {"path": "D:/newfuxios/install/x64-install/dev/bin/RoboticLaserMarkingUI.pdb"}], "backtrace": 3, "backtraceGraph": {"commands": ["add_executable", "include", "target_link_libraries", "add_unique_libraries", "set_property", "find_package", "boost_find_component", "ADD_DEFINITIONS", "include_directories"], "files": ["builder/cmake/executable.cmake", "RoboticLaserMarking/UI/CMakeLists.txt", "builder/cmake/common.cmake", "builder/cmake/library.cmake", "fuxicommon/CMakeLists.txt", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_filesystem-1.78.0/libboost_filesystem-variant-vc142-mt-gd-x64-1_78-static.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_filesystem-1.78.0/boost_filesystem-config.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/Boost-1.78.0/BoostConfig.cmake", "C:/Program Files/JetBrains/CLion 2025.1.3/bin/cmake/win/x64/share/cmake-3.31/Modules/FindBoost.cmake", "builder/cmake/add_boost.cmake", "C:/opt/PCL/3rdParty/Boost/lib/cmake/boost_thread-1.78.0/boost_thread-config.cmake", "builder/cmake/add_eigen.cmake", "builder/cmake/add_glog.cmake", "builder/cmake/add_libmodbus.cmake", "builder/cmake/add_laserControlFrame.cmake"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 5, "parent": 0}, {"file": 0, "parent": 1}, {"command": 0, "file": 0, "line": 24, "parent": 2}, {"command": 3, "file": 0, "line": 30, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 4}, {"command": 3, "file": 0, "line": 35, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 6}, {"command": 3, "file": 0, "line": 35, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 8}, {"command": 3, "file": 0, "line": 35, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 10}, {"command": 2, "file": 0, "line": 46, "parent": 2}, {"file": 4}, {"command": 1, "file": 4, "line": 3, "parent": 13}, {"file": 3, "parent": 14}, {"command": 3, "file": 3, "line": 78, "parent": 15}, {"command": 2, "file": 3, "line": 53, "parent": 16}, {"command": 1, "file": 3, "line": 1, "parent": 15}, {"file": 2, "parent": 18}, {"command": 1, "file": 2, "line": 81, "parent": 19}, {"file": 9, "parent": 20}, {"command": 5, "file": 9, "line": 33, "parent": 21}, {"file": 8, "parent": 22}, {"command": 5, "file": 8, "line": 610, "parent": 23}, {"file": 7, "parent": 24}, {"command": 6, "file": 7, "line": 262, "parent": 25}, {"command": 5, "file": 7, "line": 141, "parent": 26}, {"file": 6, "parent": 27}, {"command": 1, "file": 6, "line": 53, "parent": 28}, {"file": 5, "parent": 29}, {"command": 4, "file": 5, "line": 104, "parent": 30}, {"command": 4, "file": 6, "line": 103, "parent": 28}, {"command": 6, "file": 7, "line": 262, "parent": 25}, {"command": 5, "file": 7, "line": 141, "parent": 33}, {"file": 10, "parent": 34}, {"command": 4, "file": 10, "line": 103, "parent": 35}, {"command": 3, "file": 3, "line": 84, "parent": 15}, {"command": 2, "file": 3, "line": 53, "parent": 37}, {"command": 3, "file": 3, "line": 84, "parent": 15}, {"command": 2, "file": 3, "line": 53, "parent": 39}, {"command": 3, "file": 3, "line": 84, "parent": 15}, {"command": 2, "file": 3, "line": 53, "parent": 41}, {"command": 3, "file": 3, "line": 84, "parent": 15}, {"command": 2, "file": 3, "line": 53, "parent": 43}, {"command": 3, "file": 0, "line": 35, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 45}, {"command": 3, "file": 0, "line": 35, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 47}, {"command": 3, "file": 0, "line": 35, "parent": 2}, {"command": 2, "file": 2, "line": 137, "parent": 49}, {"command": 7, "file": 0, "line": 17, "parent": 2}, {"command": 7, "file": 0, "line": 13, "parent": 2}, {"command": 7, "file": 0, "line": 16, "parent": 2}, {"command": 7, "file": 0, "line": 14, "parent": 2}, {"command": 7, "file": 0, "line": 15, "parent": 2}, {"command": 1, "file": 0, "line": 1, "parent": 2}, {"file": 2, "parent": 56}, {"command": 8, "file": 2, "line": 54, "parent": 57}, {"command": 1, "file": 2, "line": 81, "parent": 57}, {"file": 11, "parent": 59}, {"command": 8, "file": 11, "line": 30, "parent": 60}, {"command": 1, "file": 2, "line": 81, "parent": 57}, {"file": 12, "parent": 62}, {"command": 8, "file": 12, "line": 16, "parent": 63}, {"command": 1, "file": 2, "line": 81, "parent": 57}, {"file": 13, "parent": 65}, {"command": 8, "file": 13, "line": 8, "parent": 66}, {"command": 1, "file": 2, "line": 81, "parent": 57}, {"file": 14, "parent": 68}, {"command": 8, "file": 14, "line": 41, "parent": 69}, {"command": 8, "file": 0, "line": 34, "parent": 2}, {"command": 8, "file": 0, "line": 34, "parent": 2}, {"command": 8, "file": 0, "line": 34, "parent": 2}, {"command": 8, "file": 0, "line": 34, "parent": 2}, {"command": 8, "file": 0, "line": 34, "parent": 2}, {"command": 8, "file": 0, "line": 34, "parent": 2}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc /MDd /Zi /Ob0 /Od /RTC1 -std:c++20"}], "defines": [{"backtrace": 5, "define": "BOOST_ATOMIC_NO_LIB"}, {"backtrace": 5, "define": "BOOST_CHRONO_NO_LIB"}, {"backtrace": 5, "define": "BOOST_DATE_TIME_NO_LIB"}, {"backtrace": 5, "define": "BOOST_FILESYSTEM_NO_LIB"}, {"backtrace": 5, "define": "BOOST_IOSTREAMS_NO_LIB"}, {"backtrace": 5, "define": "BOOST_PROGRAM_OPTIONS_NO_LIB"}, {"backtrace": 5, "define": "BOOST_SERIALIZATION_NO_LIB"}, {"backtrace": 5, "define": "BOOST_SYSTEM_NO_LIB"}, {"backtrace": 5, "define": "BOOST_THREAD_NO_LIB"}, {"backtrace": 9, "define": "QT_CHARTS_LIB"}, {"backtrace": 51, "define": "QT_CORE_LIB"}, {"backtrace": 52, "define": "QT_DISABLE_DEPRECATED_BEFORE=0"}, {"backtrace": 53, "define": "QT_GUI_LIB"}, {"backtrace": 11, "define": "QT_NETWORK_LIB"}, {"backtrace": 54, "define": "QT_NO_DEBUG"}, {"backtrace": 5, "define": "QT_SQL_LIB"}, {"backtrace": 55, "define": "QT_WIDGETS_LIB"}, {"backtrace": 5, "define": "RTTR_DLL"}], "includes": [{"backtrace": 0, "path": "D:/newfuxios/cmake-build-debug/RoboticLaserMarking/UI/RoboticLaserMarkingUI_autogen/include"}, {"backtrace": 58, "path": "D:/newfuxios/RoboticLaserMarking/UI/include"}, {"backtrace": 61, "path": "C:/opt/PCL/3rdParty/Eigen/eigen3"}, {"backtrace": 64, "path": "C:/opt/glog/include"}, {"backtrace": 67, "path": "C:/opt/libmodbus/include"}, {"backtrace": 70, "path": "C:/opt/laserControlFrame/include"}, {"backtrace": 5, "path": "D:/newfuxios/RoboticLaserMarking/RFIDDriver/include"}, {"backtrace": 5, "path": "D:/newfuxios/RoboticLaserMarking/laserDriver/include"}, {"backtrace": 5, "path": "D:/newfuxios/RoboticLaserMarking/AbbDriver/include"}, {"backtrace": 5, "path": "D:/newfuxios/fuxicommon/include"}, {"backtrace": 5, "path": "C:/opt/openssl/include"}, {"backtrace": 71, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include"}, {"backtrace": 71, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore"}, {"backtrace": 71, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/./mkspecs/win32-msvc"}, {"backtrace": 72, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui"}, {"backtrace": 72, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE"}, {"backtrace": 73, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtSql"}, {"backtrace": 74, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets"}, {"backtrace": 75, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCharts"}, {"backtrace": 76, "isSystem": true, "path": "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtNetwork"}, {"backtrace": 5, "isSystem": true, "path": "C:/opt/PCL/3rdParty/Boost/include/boost-1_78"}, {"backtrace": 5, "isSystem": true, "path": "C:/opt/rttr/include"}], "language": "CXX", "languageStandard": {"backtraces": [5, 5], "standard": "20"}, "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16]}], "dependencies": [{"backtrace": 5, "id": "fuxicommon::@58335e9a86196d0a97e7"}, {"backtrace": 5, "id": "RoboticLaserMarkingAbbDriver::@9c3ce50b53cc3ff6eff5"}, {"backtrace": 5, "id": "RoboticLaserMarkingRFIDDriver::@d1520424919af3a40272"}, {"backtrace": 5, "id": "RoboticLaserMarkinglaserDriver::@cf8a855e37e415d7ca08"}, {"backtrace": 0, "id": "RoboticLaserMarkingUI_autogen::@4e1303897d180b86ab2f"}], "id": "RoboticLaserMarkingUI::@4e1303897d180b86ab2f", "link": {"commandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc /MDd /Zi /Ob0 /Od /RTC1", "role": "flags"}, {"fragment": "/machine:x64                                                                                                                                                                                                                                                                         /debug /INCREMENTAL /subsystem:console /machine:x64", "role": "flags"}, {"backtrace": 5, "fragment": "RoboticLaserMarking\\RFIDDriver\\RoboticLaserMarkingRFIDDriver.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "RoboticLaserMarking\\laserDriver\\RoboticLaserMarkinglaserDriver.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "RoboticLaserMarking\\AbbDriver\\RoboticLaserMarkingAbbDriver.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\laserControlFrame\\lib\\LaserControlFrameSDK.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\libmodbus\\lib\\modbus.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\glog\\lib\\glogd.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "fuxicommon\\fuxicommon.lib", "role": "libraries"}, {"backtrace": 7, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Sqld.lib", "role": "libraries"}, {"backtrace": 9, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Chartsd.lib", "role": "libraries"}, {"backtrace": 11, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Networkd.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "ws2_32.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "kernel32.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "user32.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "gdi32.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "winspool.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "shell32.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "ole32.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "oleaut32.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "uuid.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "comdlg32.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "advapi32.lib", "role": "libraries"}, {"backtrace": 17, "fragment": "C:\\opt\\opencv\\build\\x64\\vc16\\lib\\opencv_world4110d.lib", "role": "libraries"}, {"backtrace": 17, "fragment": "C:\\opt\\opencv\\build\\x64\\vc16\\lib\\opencv_img_hash4110d.lib", "role": "libraries"}, {"backtrace": 17, "fragment": "C:\\opt\\xerces-c\\lib\\xerces-c_3D.lib", "role": "libraries"}, {"backtrace": 17, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_system-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 17, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_filesystem-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 31, "fragment": "bcrypt.lib", "role": "libraries"}, {"backtrace": 31, "fragment": "synchronization.lib", "role": "libraries"}, {"backtrace": 32, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_atomic-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 17, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_date_time-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 17, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_iostreams-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 17, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_serialization-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 17, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_thread-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 36, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_chrono-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 17, "fragment": "C:\\opt\\PCL\\3rdParty\\Boost\\lib\\libboost_program_options-vc142-mt-gd-x64-1_78.lib", "role": "libraries"}, {"backtrace": 17, "fragment": "C:\\opt\\openssl\\lib\\libssl.lib", "role": "libraries"}, {"backtrace": 17, "fragment": "C:\\opt\\openssl\\lib\\libcrypto.lib", "role": "libraries"}, {"backtrace": 17, "fragment": "C:\\opt\\openssl\\lib\\jsoncpp.lib", "role": "libraries"}, {"backtrace": 17, "fragment": "C:\\opt\\openssl\\lib\\libcurl.lib", "role": "libraries"}, {"backtrace": 17, "fragment": "C:\\opt\\openssl\\lib\\zlib.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "ws2_32.lib", "role": "libraries"}, {"backtrace": 17, "fragment": "crypt32.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\opt\\glog\\lib\\glogd.lib", "role": "libraries"}, {"backtrace": 17, "fragment": "C:\\opt\\rttr\\lib\\rttr_core_d.lib", "role": "libraries"}, {"backtrace": 38, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Sqld.lib", "role": "libraries"}, {"backtrace": 40, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Widgetsd.lib", "role": "libraries"}, {"backtrace": 42, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Guid.lib", "role": "libraries"}, {"backtrace": 44, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Cored.lib", "role": "libraries"}, {"backtrace": 46, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Widgetsd.lib", "role": "libraries"}, {"backtrace": 48, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Guid.lib", "role": "libraries"}, {"backtrace": 50, "fragment": "C:\\Qt\\Qt5.14.2\\5.14.2\\msvc2017_64\\lib\\Qt5Cored.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "user32.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "advapi32.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "RoboticLaserMarkingUI", "nameOnDisk": "RoboticLaserMarkingUI.exe", "paths": {"build": "RoboticLaserMarking/UI", "source": "RoboticLaserMarking/UI"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "cmake-build-debug/RoboticLaserMarking/UI/RoboticLaserMarkingUI_autogen/mocs_compilation.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "RoboticLaserMarking/UI/src/DatabaseQueryWorker.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "RoboticLaserMarking/UI/src/DetailedReportWidget.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "RoboticLaserMarking/UI/src/DeviceControlUtils.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "RoboticLaserMarking/UI/src/DeviceStatusWidget.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "RoboticLaserMarking/UI/src/LicenseManager.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "RoboticLaserMarking/UI/src/LogWidget.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "RoboticLaserMarking/UI/src/MainWindow.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "RoboticLaserMarking/UI/src/ProductionStatistics.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "RoboticLaserMarking/UI/src/ProductionStatisticsManager.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "RoboticLaserMarking/UI/src/RegisterDialog.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "RoboticLaserMarking/UI/src/StatisticsExporter.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "RoboticLaserMarking/UI/src/SystemSettingsDialog.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "RoboticLaserMarking/UI/src/TemplateEditDialog.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "RoboticLaserMarking/UI/src/TemplateManager.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "RoboticLaserMarking/UI/src/UIUtils.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "RoboticLaserMarking/UI/src/main.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "path": "RoboticLaserMarking/UI/include/AppStyle.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "RoboticLaserMarking/UI/include/DatabaseQueryWorker.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "RoboticLaserMarking/UI/include/DetailedReportWidget.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "RoboticLaserMarking/UI/include/DeviceControlUtils.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "RoboticLaserMarking/UI/include/DeviceStatusWidget.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "RoboticLaserMarking/UI/include/LicenseManager.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "RoboticLaserMarking/UI/include/LogWidget.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "RoboticLaserMarking/UI/include/MainWindow.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "RoboticLaserMarking/UI/include/ProductionStatistics.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "RoboticLaserMarking/UI/include/ProductionStatisticsManager.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "RoboticLaserMarking/UI/include/RegisterDialog.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "RoboticLaserMarking/UI/include/StatisticsExporter.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "RoboticLaserMarking/UI/include/SystemSettingsDialog.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "RoboticLaserMarking/UI/include/TemplateEditDialog.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "RoboticLaserMarking/UI/include/TemplateManager.h", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "RoboticLaserMarking/UI/include/UIUtils.h", "sourceGroupIndex": 1}], "type": "EXECUTABLE"}